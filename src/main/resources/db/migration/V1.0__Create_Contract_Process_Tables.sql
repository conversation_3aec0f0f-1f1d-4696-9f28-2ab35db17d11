-- 合同流程相关表的完整建表SQL
-- V1.0__Create_Contract_Process_Tables.sql

-- 1. 合同流程状态控制表
CREATE TABLE IF NOT EXISTS `t_contract_process_state` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `contract_id` VARCHAR(64) NOT NULL COMMENT '合同ID',
    `process_instance_id` VARCHAR(128) NOT NULL COMMENT '流程实例ID，用于区分同一合同的不同流程实例',
    `trigger_type` VARCHAR(32) NOT NULL COMMENT '触发类型：START_RENTAL(发车), END_RENTAL(收车)',
    `current_state` VARCHAR(64) NOT NULL COMMENT '当前状态',
    `previous_state` VARCHAR(64) COMMENT '上一个状态',
    `process_data` TEXT COMMENT '流程数据(JSON格式)',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `max_retry_count` INT DEFAULT 3 COMMENT '最大重试次数',
    `next_retry_time` TIMESTAMP NULL COMMENT '下次重试时间',
    `error_message` TEXT COMMENT '错误信息',
    `is_manual_intervention` TINYINT(1) DEFAULT 0 COMMENT '是否需要人工干预',
    `is_deleted` INT DEFAULT 0 COMMENT '状态（0=正常   1=已删除）',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_oper_id` BIGINT DEFAULT -1 COMMENT '创建人ID',
    `create_oper_name` VARCHAR(128) DEFAULT '' COMMENT '创建人名称',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_oper_id` BIGINT DEFAULT -1 COMMENT '更新人ID',
    `update_oper_name` VARCHAR(128) DEFAULT '' COMMENT '更新人名称',
    `created_time` TIMESTAMP NULL COMMENT '创建时间（兼容字段）',
    `updated_time` TIMESTAMP NULL COMMENT '更新时间（兼容字段）',
    `created_by` VARCHAR(128) COMMENT '创建人（兼容字段）',
    `updated_by` VARCHAR(128) COMMENT '更新人（兼容字段）',
    `version` INT DEFAULT 1 COMMENT '版本号，用于乐观锁控制',
    
    -- 索引
    INDEX `idx_contract_id` (`contract_id`),
    INDEX `idx_process_instance_id` (`process_instance_id`),
    INDEX `idx_trigger_type` (`trigger_type`),
    INDEX `idx_current_state` (`current_state`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`),
    INDEX `idx_create_oper_id` (`create_oper_id`),
    INDEX `idx_update_oper_id` (`update_oper_id`),
    INDEX `idx_next_retry_time` (`next_retry_time`),
    
    -- 复合索引
    INDEX `idx_contract_id_deleted` (`contract_id`, `is_deleted`),
    INDEX `idx_process_instance_deleted` (`process_instance_id`, `is_deleted`),
    INDEX `idx_trigger_current_state` (`trigger_type`, `current_state`),
    INDEX `idx_state_retry_time` (`current_state`, `next_retry_time`),
    INDEX `idx_create_time_range` (`create_time`, `is_deleted`),
    
    -- 唯一索引
    UNIQUE INDEX `uk_process_instance_id` (`process_instance_id`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同流程状态控制表';

-- 2. 合同流程操作日志表
CREATE TABLE IF NOT EXISTS `t_contract_process_log` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `contract_id` VARCHAR(64) NOT NULL COMMENT '合同ID',
    `process_instance_id` VARCHAR(128) COMMENT '流程实例ID',
    `operation_type` VARCHAR(64) NOT NULL COMMENT '操作类型',
    `from_state` VARCHAR(64) COMMENT '源状态',
    `to_state` VARCHAR(64) COMMENT '目标状态',
    `operation_result` VARCHAR(32) NOT NULL COMMENT '操作结果(SUCCESS/FAILED)',
    `request_data` TEXT COMMENT '请求数据',
    `response_data` TEXT COMMENT '响应数据',
    `error_code` VARCHAR(64) COMMENT '错误码',
    `error_message` TEXT COMMENT '错误信息',
    `execution_time` BIGINT COMMENT '执行时间(毫秒)',
    `operator` VARCHAR(128) COMMENT '操作人',
    `third_party_request_id` VARCHAR(128) COMMENT '第三方请求ID',
    `request_url` VARCHAR(512) COMMENT '请求URL',
    `request_method` VARCHAR(16) COMMENT '请求方法',
    `request_headers` TEXT COMMENT '请求头',
    `response_status` INT COMMENT '响应状态码',
    `response_headers` TEXT COMMENT '响应头',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `is_deleted` INT DEFAULT 0 COMMENT '状态（0=正常   1=已删除）',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_oper_id` BIGINT DEFAULT -1 COMMENT '创建人ID',
    `create_oper_name` VARCHAR(128) DEFAULT '' COMMENT '创建人名称',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_oper_id` BIGINT DEFAULT -1 COMMENT '更新人ID',
    `update_oper_name` VARCHAR(128) DEFAULT '' COMMENT '更新人名称',
    `created_time` TIMESTAMP NULL COMMENT '创建时间（兼容字段）',
    
    -- 索引
    INDEX `idx_contract_id` (`contract_id`),
    INDEX `idx_process_instance_id` (`process_instance_id`),
    INDEX `idx_operation_type` (`operation_type`),
    INDEX `idx_operation_result` (`operation_result`),
    INDEX `idx_third_party_request_id` (`third_party_request_id`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`),
    INDEX `idx_create_oper_id` (`create_oper_id`),
    INDEX `idx_update_oper_id` (`update_oper_id`),
    
    -- 复合索引
    INDEX `idx_contract_id_deleted` (`contract_id`, `is_deleted`),
    INDEX `idx_process_instance_deleted` (`process_instance_id`, `is_deleted`),
    INDEX `idx_operation_result_time` (`operation_result`, `create_time`),
    INDEX `idx_create_time_range` (`create_time`, `is_deleted`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同流程操作日志表';



-- 创建视图，方便查询未删除的记录
CREATE OR REPLACE VIEW `v_contract_process_state` AS
SELECT * FROM `t_contract_process_state` WHERE `is_deleted` = 0;

CREATE OR REPLACE VIEW `v_contract_process_log` AS
SELECT * FROM `t_contract_process_log` WHERE `is_deleted` = 0;
