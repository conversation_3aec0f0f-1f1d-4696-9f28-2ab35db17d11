-- 合同流程相关表的完整建表SQL
-- V1.0__Create_Contract_Process_Tables.sql

-- 1. 合同流程状态控制表
CREATE TABLE IF NOT EXISTS `t_contract_process_state` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `contract_id` VARCHAR(64) NOT NULL COMMENT '合同ID',
    `process_instance_id` VARCHAR(128) NOT NULL COMMENT '流程实例ID，用于区分同一合同的不同流程实例',
    `trigger_type` VARCHAR(32) NOT NULL COMMENT '触发类型：START_RENTAL(发车), END_RENTAL(收车)',
    `current_state` VARCHAR(64) NOT NULL COMMENT '当前状态',
    `previous_state` VARCHAR(64) COMMENT '上一个状态',
    `process_data` TEXT COMMENT '流程数据(JSON格式)',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `max_retry_count` INT DEFAULT 3 COMMENT '最大重试次数',
    `next_retry_time` TIMESTAMP NULL COMMENT '下次重试时间',
    `error_message` TEXT COMMENT '错误信息',
    `is_manual_intervention` TINYINT(1) DEFAULT 0 COMMENT '是否需要人工干预',
    `is_deleted` INT DEFAULT 0 COMMENT '状态（0=正常   1=已删除）',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_oper_id` BIGINT DEFAULT -1 COMMENT '创建人ID',
    `create_oper_name` VARCHAR(128) DEFAULT '' COMMENT '创建人名称',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_oper_id` BIGINT DEFAULT -1 COMMENT '更新人ID',
    `update_oper_name` VARCHAR(128) DEFAULT '' COMMENT '更新人名称',
    `created_time` TIMESTAMP NULL COMMENT '创建时间（兼容字段）',
    `updated_time` TIMESTAMP NULL COMMENT '更新时间（兼容字段）',
    `created_by` VARCHAR(128) COMMENT '创建人（兼容字段）',
    `updated_by` VARCHAR(128) COMMENT '更新人（兼容字段）',
    `version` INT DEFAULT 1 COMMENT '版本号，用于乐观锁控制',
    
    -- 索引
    INDEX `idx_contract_id` (`contract_id`),
    INDEX `idx_process_instance_id` (`process_instance_id`),
    INDEX `idx_trigger_type` (`trigger_type`),
    INDEX `idx_current_state` (`current_state`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`),
    INDEX `idx_create_oper_id` (`create_oper_id`),
    INDEX `idx_update_oper_id` (`update_oper_id`),
    INDEX `idx_next_retry_time` (`next_retry_time`),
    
    -- 复合索引
    INDEX `idx_contract_id_deleted` (`contract_id`, `is_deleted`),
    INDEX `idx_process_instance_deleted` (`process_instance_id`, `is_deleted`),
    INDEX `idx_trigger_current_state` (`trigger_type`, `current_state`),
    INDEX `idx_state_retry_time` (`current_state`, `next_retry_time`),
    INDEX `idx_create_time_range` (`create_time`, `is_deleted`),
    
    -- 唯一索引
    UNIQUE INDEX `uk_process_instance_id` (`process_instance_id`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同流程状态控制表';

-- 2. 合同流程操作日志表
CREATE TABLE IF NOT EXISTS `t_contract_process_log` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `contract_id` VARCHAR(64) NOT NULL COMMENT '合同ID',
    `process_instance_id` VARCHAR(128) COMMENT '流程实例ID',
    `operation_type` VARCHAR(64) NOT NULL COMMENT '操作类型',
    `from_state` VARCHAR(64) COMMENT '源状态',
    `to_state` VARCHAR(64) COMMENT '目标状态',
    `operation_result` VARCHAR(32) NOT NULL COMMENT '操作结果(SUCCESS/FAILED)',
    `request_data` TEXT COMMENT '请求数据',
    `response_data` TEXT COMMENT '响应数据',
    `error_code` VARCHAR(64) COMMENT '错误码',
    `error_message` TEXT COMMENT '错误信息',
    `execution_time` BIGINT COMMENT '执行时间(毫秒)',
    `operator` VARCHAR(128) COMMENT '操作人',
    `third_party_request_id` VARCHAR(128) COMMENT '第三方请求ID',
    `request_url` VARCHAR(512) COMMENT '请求URL',
    `request_method` VARCHAR(16) COMMENT '请求方法',
    `request_headers` TEXT COMMENT '请求头',
    `response_status` INT COMMENT '响应状态码',
    `response_headers` TEXT COMMENT '响应头',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `is_deleted` INT DEFAULT 0 COMMENT '状态（0=正常   1=已删除）',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_oper_id` BIGINT DEFAULT -1 COMMENT '创建人ID',
    `create_oper_name` VARCHAR(128) DEFAULT '' COMMENT '创建人名称',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_oper_id` BIGINT DEFAULT -1 COMMENT '更新人ID',
    `update_oper_name` VARCHAR(128) DEFAULT '' COMMENT '更新人名称',
    `created_time` TIMESTAMP NULL COMMENT '创建时间（兼容字段）',
    
    -- 索引
    INDEX `idx_contract_id` (`contract_id`),
    INDEX `idx_process_instance_id` (`process_instance_id`),
    INDEX `idx_operation_type` (`operation_type`),
    INDEX `idx_operation_result` (`operation_result`),
    INDEX `idx_third_party_request_id` (`third_party_request_id`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_update_time` (`update_time`),
    INDEX `idx_create_oper_id` (`create_oper_id`),
    INDEX `idx_update_oper_id` (`update_oper_id`),
    
    -- 复合索引
    INDEX `idx_contract_id_deleted` (`contract_id`, `is_deleted`),
    INDEX `idx_process_instance_deleted` (`process_instance_id`, `is_deleted`),
    INDEX `idx_operation_result_time` (`operation_result`, `create_time`),
    INDEX `idx_create_time_range` (`create_time`, `is_deleted`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='合同流程操作日志表';

-- 3. 事件处理器配置表（新增）
CREATE TABLE IF NOT EXISTS `t_event_handler_config` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `event_type` VARCHAR(64) NOT NULL COMMENT '事件类型',
    `handler_name` VARCHAR(128) NOT NULL COMMENT '处理器名称',
    `handler_class` VARCHAR(256) NOT NULL COMMENT '处理器类名',
    `execution_order` INT DEFAULT 0 COMMENT '执行顺序',
    `execution_mode` VARCHAR(16) DEFAULT 'SERIAL' COMMENT '执行模式：SERIAL(串行), PARALLEL(并行)',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `retry_count` INT DEFAULT 3 COMMENT '重试次数',
    `timeout_seconds` INT DEFAULT 30 COMMENT '超时时间(秒)',
    `config_data` TEXT COMMENT '配置数据(JSON格式)',
    `description` VARCHAR(512) COMMENT '描述',
    `is_deleted` INT DEFAULT 0 COMMENT '状态（0=正常   1=已删除）',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_oper_id` BIGINT DEFAULT -1 COMMENT '创建人ID',
    `create_oper_name` VARCHAR(128) DEFAULT '' COMMENT '创建人名称',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_oper_id` BIGINT DEFAULT -1 COMMENT '更新人ID',
    `update_oper_name` VARCHAR(128) DEFAULT '' COMMENT '更新人名称',
    
    -- 索引
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_handler_name` (`handler_name`),
    INDEX `idx_is_enabled` (`is_enabled`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_execution_order` (`execution_order`),
    
    -- 复合索引
    INDEX `idx_event_enabled_order` (`event_type`, `is_enabled`, `execution_order`),
    UNIQUE INDEX `uk_event_handler` (`event_type`, `handler_name`, `is_deleted`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='事件处理器配置表';

-- 4. 状态转换配置表（新增）
CREATE TABLE IF NOT EXISTS `t_state_transition_config` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `event_type` VARCHAR(64) NOT NULL COMMENT '事件类型',
    `current_state` VARCHAR(64) NOT NULL COMMENT '当前状态',
    `next_state` VARCHAR(64) NOT NULL COMMENT '下一状态',
    `condition_expression` VARCHAR(512) COMMENT '条件表达式',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `description` VARCHAR(512) COMMENT '描述',
    `is_deleted` INT DEFAULT 0 COMMENT '状态（0=正常   1=已删除）',
    `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_oper_id` BIGINT DEFAULT -1 COMMENT '创建人ID',
    `create_oper_name` VARCHAR(128) DEFAULT '' COMMENT '创建人名称',
    `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_oper_id` BIGINT DEFAULT -1 COMMENT '更新人ID',
    `update_oper_name` VARCHAR(128) DEFAULT '' COMMENT '更新人名称',
    
    -- 索引
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_current_state` (`current_state`),
    INDEX `idx_next_state` (`next_state`),
    INDEX `idx_is_enabled` (`is_enabled`),
    INDEX `idx_is_deleted` (`is_deleted`),
    INDEX `idx_priority` (`priority`),
    
    -- 复合索引
    INDEX `idx_event_current_enabled` (`event_type`, `current_state`, `is_enabled`),
    UNIQUE INDEX `uk_event_current_next` (`event_type`, `current_state`, `next_state`, `is_deleted`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='状态转换配置表';

-- 插入默认的事件处理器配置
INSERT INTO `t_event_handler_config` (`event_type`, `handler_name`, `handler_class`, `execution_order`, `execution_mode`, `description`) VALUES
('MQ_MESSAGE_RECEIVED', 'VehicleCheckHandler', 'com.saicmobility.evcard.md.order.contract.handler.VehicleCheckHandler', 1, 'SERIAL', '验车处理器'),
('VEHICLE_CHECK_SUCCESS', 'SmsNotificationHandler', 'com.saicmobility.evcard.md.order.contract.handler.SmsNotificationHandler', 1, 'SERIAL', '短信通知处理器'),
('SMS_SEND_SUCCESS', 'ContractSealHandler', 'com.saicmobility.evcard.md.order.contract.handler.ContractSealHandler', 1, 'SERIAL', '合同盖章处理器'),
('SEAL_SUCCESS', 'ProcessCompleteHandler', 'com.saicmobility.evcard.md.order.contract.handler.ProcessCompleteHandler', 1, 'SERIAL', '流程完成处理器');

-- 插入默认的状态转换配置
INSERT INTO `t_state_transition_config` (`event_type`, `current_state`, `next_state`, `description`) VALUES
('MQ_MESSAGE_RECEIVED', 'INIT', 'VEHICLE_CHECKING', '接收MQ消息后开始验车'),
('VEHICLE_CHECK_SUCCESS', 'VEHICLE_CHECKING', 'SMS_SENDING', '验车成功后发送短信'),
('VEHICLE_CHECK_FAILED', 'VEHICLE_CHECKING', 'VEHICLE_CHECK_FAILED', '验车失败'),
('SMS_SEND_SUCCESS', 'SMS_SENDING', 'SEALING', '短信发送成功后开始盖章'),
('SMS_SEND_FAILED', 'SMS_SENDING', 'SMS_SEND_FAILED', '短信发送失败'),
('SEAL_SUCCESS', 'SEALING', 'PROCESS_COMPLETED', '盖章成功，流程完成'),
('SEAL_FAILED', 'SEALING', 'SEAL_FAILED', '盖章失败');

-- 创建视图，方便查询未删除的记录
CREATE OR REPLACE VIEW `v_contract_process_state` AS
SELECT * FROM `t_contract_process_state` WHERE `is_deleted` = 0;

CREATE OR REPLACE VIEW `v_contract_process_log` AS
SELECT * FROM `t_contract_process_log` WHERE `is_deleted` = 0;

CREATE OR REPLACE VIEW `v_event_handler_config` AS
SELECT * FROM `t_event_handler_config` WHERE `is_deleted` = 0 AND `is_enabled` = 1;

CREATE OR REPLACE VIEW `v_state_transition_config` AS
SELECT * FROM `t_state_transition_config` WHERE `is_deleted` = 0 AND `is_enabled` = 1;
