package com.extracme.evcard.rpc.exception;


import com.extracme.evcard.rpc.enums.StatusCode;
import com.saicmobility.evcard.md.order.enums.ExceptionEnum;

public class BusinessException extends Exception {

    private static final long serialVersionUID = 639278944405695671L;

    protected int code;

    protected String message;


    public BusinessException(StatusCode statusCode) {
        this.code = statusCode.getCode();
        this.message = statusCode.getMsg();
    }
    
    public BusinessException(ExceptionEnum exceptionEnum) {
    	 this.code = exceptionEnum.getErrCode();
         this.message = exceptionEnum.getErrMsg();
    }

    public BusinessException(int code) {
        super(String.valueOf(code));
        this.code = code;
        this.message = String.valueOf(code);
    }

    public BusinessException(String message) {
        super(message);
        this.message = message;
    }

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }

}

