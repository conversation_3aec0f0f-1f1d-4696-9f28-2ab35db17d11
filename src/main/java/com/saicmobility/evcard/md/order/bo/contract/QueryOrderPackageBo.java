package com.saicmobility.evcard.md.order.bo.contract;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/13
 */
@Data
public class QueryOrderPackageBo {
    private boolean usePackage; // 本子订单是否使用了套餐
    private boolean canUpgradeEarlyBird; // 本子订单的套餐是否能升级为早鸟套餐
    private boolean useEarlyBird; // 本子订单，用户是否选择升级为早鸟套餐
    private boolean inPackageRange; // 入参的基准时间是否进入套餐时间范围
    private int earlyBirdNumAfterBaseTime; // 当前子订单之后有几笔早鸟已支付（未使用）
}
