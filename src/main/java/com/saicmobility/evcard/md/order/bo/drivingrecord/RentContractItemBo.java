package com.saicmobility.evcard.md.order.bo.drivingrecord;

import java.math.BigDecimal;
import java.util.List;

import com.saicmobility.evcard.md.mdorderservice.api.AccompanyingCardAmount;
import com.saicmobility.evcard.md.order.entity.ContractItemExemptionRecord;
import com.saicmobility.evcard.md.order.utils.AccompanyingCardUtil;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import org.apache.commons.lang3.StringUtils;

import com.saicmobility.evcard.md.mdorderservice.api.RentContractItem;
import com.saicmobility.evcard.md.order.bo.contract.DiscountAmountAppBo;
import com.saicmobility.evcard.md.order.bo.contract.RentAmountAppBo;
import com.saicmobility.evcard.md.order.bo.contract.ServiceAmountAppBo;

import lombok.Data;

/**
 * 行车记录使用- 租车合同项列表
 */
@Data
public class RentContractItemBo {

    //  合同项编号
    private String contractItemId;

    // 明细总金额
    private String totalAmount;

    // 开始时间
    private String startTime;

    // 结束时间
    private String endTime;

    //  时间段类型 1超时段
//    private Integer periodStatus;

    // 租车费
    private RentAmountAppBo rent;

    // 燃油费
	// private OilAmountBo oil;

    // 服务费
    private ServiceAmountAppBo service;

    // 折扣减免
    private DiscountAmountAppBo discount;

    // 预付选择的优惠券信息
    private PreCouponInfoBo preCouponInfo;

    // 预付选择的会员卡信息
    private PreCardInfoBo preCardInfo;

    // 退款预付款信息
    private RefundPrepayInfoBo refundPrepayInfoBo;

    // -----------------随享卡减免明细------------------
    // 随享卡减免明细
    private List<ContractItemExemptionRecord> accompanyingCardItemExemptionRecordList;
    // 随享卡id
    private long userAccompanyingCardId;
    // 车型免费升级减免金额
    private BigDecimal modifyGoodsModelExemptionAmount;

    public RentContractItem toRes() {
        RentContractItem.Builder builder = RentContractItem.newBuilder()
                .setContractItemId(contractItemId)
                .setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(totalAmount))
                .setStartTime(startTime)
                .setEndTime(endTime);
		if (rent != null) {
			builder.setRent(rent.toRes());
		}
		if (service != null) {
			builder.setService(service.toRes());
		}
		// if (oil != null && oil.getOilAmount() != null &&
		// oil.getOilAmount().compareTo(BigDecimal.ZERO) != 0) {
		// builder.setOil(oil.toRes());
		// }
        if (discount != null && discount.getServiceAmount() != null && discount.getServiceAmount().compareTo(BigDecimal.ZERO) != 0) {
            builder.setDiscount(discount.toRes());
        }
        if (preCouponInfo != null) {
            builder.setPreCouponInfo(preCouponInfo.toRes());
        }
        if (preCardInfo != null && StringUtils.isNotBlank(preCardInfo.getUserCardNo())) {
            builder.setPreCardInfo(preCardInfo.toRes());
        }
        if (refundPrepayInfoBo != null) {
            builder.setRefundPrepayInfo(refundPrepayInfoBo.toRes());
        }
        AccompanyingCardAmount accompanyingCardAmount = AccompanyingCardUtil.toRes(accompanyingCardItemExemptionRecordList, userAccompanyingCardId);
        if (accompanyingCardAmount != null) {
            builder.setAccompanyingCardAmount(accompanyingCardAmount);
        }
        if (modifyGoodsModelExemptionAmount != null && modifyGoodsModelExemptionAmount.compareTo(BigDecimal.ZERO) > 0) {
            builder.setModifyGoodsModelExemptionAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(modifyGoodsModelExemptionAmount.toPlainString()));
        }
        return builder.build();
    }
}
