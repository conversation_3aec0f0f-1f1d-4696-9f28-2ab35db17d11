package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.ContractAddInfo;
import lombok.Data;

@Data
public class ContractAddInfoBo {

    private String mid;

    private String contractId;

    // 车辆所属机构
    private String vehicleOrgId;

    // 车辆运营机构
    private String operationOrgId;


    public ContractAddInfo toContractAddInfo() {
        return ContractAddInfo.newBuilder()
                .setContractId(getContractId())
                .setVehicleOrgId(getVehicleOrgId())
                .setOperationOrgId(getOperationOrgId())
                .build();
    }
}
