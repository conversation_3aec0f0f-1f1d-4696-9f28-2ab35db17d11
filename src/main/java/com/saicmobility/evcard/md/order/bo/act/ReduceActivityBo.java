package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.GetReduceActivityRes;
import com.saicmobility.evcard.md.mdactservice.api.GetStoreAvailableReduceActivityRes;

import com.saicmobility.evcard.md.mdorderservice.api.ReduceActivity;
import lombok.Data;

/**
 * 立减活动
 */
@Data
public class ReduceActivityBo {

    // 活动id
    private long id;

    //  活动图片
    private String activityPicture;

    //  活动标题
    private String title;

    //  活动内容
    private String content;

    //  开始时间
    private String startTime;

    //  结束时间
    private String endTime;

    //  活动立减金额
    private String activityDiscount;

    public static ReduceActivityBo from(GetStoreAvailableReduceActivityRes storeAvailableReduceActivity) {
        ReduceActivityBo bo = new ReduceActivityBo();
        bo.setId(storeAvailableReduceActivity.getId());
        bo.setActivityPicture(storeAvailableReduceActivity.getActivityPicUrl());
        bo.setTitle(storeAvailableReduceActivity.getActivityName());
        bo.setContent(storeAvailableReduceActivity.getActivityRuleDescription());
        bo.setStartTime(storeAvailableReduceActivity.getActivityStartTime());
        bo.setEndTime(storeAvailableReduceActivity.getActivityEndTime());
        bo.setActivityDiscount(storeAvailableReduceActivity.getActivityDiscount());
        return bo;
    }

    public static ReduceActivityBo from(GetReduceActivityRes getReduceActivityRes) {
        ReduceActivityBo bo = new ReduceActivityBo();
        bo.setId(getReduceActivityRes.getId());
        bo.setActivityPicture(getReduceActivityRes.getActivityPicUrl());
        bo.setTitle(getReduceActivityRes.getActivityName());
        bo.setContent(getReduceActivityRes.getActivityRuleDescription());
        bo.setStartTime(getReduceActivityRes.getActivityStartTime());
        bo.setEndTime(getReduceActivityRes.getActivityEndTime());
        bo.setActivityDiscount(getReduceActivityRes.getActivityDiscount());
        return bo;
    }

    public ReduceActivity toRes(){
        return ReduceActivity.newBuilder()
                .setActivityPicture(this.getActivityPicture())
                .setTitle(this.getTitle())
                .setContent(this.getContent())
                .setStartTime(this.getStartTime())
                .setEndTime(this.getEndTime())
                .setActivityDiscount(this.getActivityDiscount())
                .build();
    }
}
