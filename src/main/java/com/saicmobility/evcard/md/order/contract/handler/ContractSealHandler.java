package com.saicmobility.evcard.md.order.contract.handler;

import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.core.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 合同盖章处理器
 * 处理合同盖章相关的业务逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class ContractSealHandler extends AbstractEventHandler {

    @Override
    public String getHandlerName() {
        return "ContractSealHandler";
    }

    @Override
    protected EventDrivenStateMachine.ProcessResult doHandle(ProcessContext context) {
        log.info("开始执行合同盖章处理: contractId={}, triggerType={}", 
                context.getContractId(), context.getTriggerType());
        
        try {
            // 1. 准备盖章数据
            SealRequest request = prepareSealRequest(context);
            
            // 2. 调用盖章接口
            SealResponse response = callSealService(request);
            
            // 3. 处理响应结果
            if (response.isSuccess()) {
                // 盖章成功，设置第三方请求ID用于后续回调匹配
                context.setAttribute("sealRequestId", response.getRequestId());
                context.setAttribute("sealResult", response);
                
                log.info("盖章接口调用成功: contractId={}, requestId={}", 
                        context.getContractId(), response.getRequestId());
                
                return EventDrivenStateMachine.ProcessResult.success("盖章接口调用成功")
                        .setData(response);
            } else {
                log.error("盖章接口调用失败: contractId={}, error={}", 
                        context.getContractId(), response.getErrorMessage());
                
                return EventDrivenStateMachine.ProcessResult.failure("SEAL_FAILED", 
                        "盖章接口调用失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("盖章处理异常: contractId={}", context.getContractId(), e);
            return EventDrivenStateMachine.ProcessResult.failure("SEAL_ERROR", 
                    "盖章处理异常: " + e.getMessage());
        }
    }

    /**
     * 准备盖章请求数据
     */
    private SealRequest prepareSealRequest(ProcessContext context) {
        SealRequest request = new SealRequest();
        request.setContractId(context.getContractId());
        request.setProcessInstanceId(context.getProcessInstanceId());
        request.setTriggerType(context.getTriggerType());
        
        // 根据触发类型设置盖章类型
        if ("START_RENTAL".equals(context.getTriggerType())) {
            request.setSealType("START_RENTAL_SEAL");
            request.setDescription("发车合同盖章");
        } else if ("END_RENTAL".equals(context.getTriggerType())) {
            request.setSealType("END_RENTAL_SEAL");
            request.setDescription("收车合同盖章");
        } else {
            request.setSealType("STANDARD_SEAL");
            request.setDescription("标准合同盖章");
        }
        
        // 从事件数据中获取文档信息
        Object eventData = context.getEventData();
        if (eventData instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) eventData;
            request.setDocumentUrl((String) dataMap.get("documentUrl"));
            request.setDocumentId((String) dataMap.get("documentId"));
            request.setSealPosition((String) dataMap.get("sealPosition"));
        }
        
        // 设置默认值
        if (request.getDocumentUrl() == null) {
            request.setDocumentUrl("https://contract-system/documents/" + context.getContractId() + ".pdf");
        }
        if (request.getSealPosition() == null) {
            request.setSealPosition("BOTTOM_RIGHT");
        }
        
        return request;
    }

    /**
     * 调用盖章服务
     */
    private SealResponse callSealService(SealRequest request) {
        log.info("调用盖章服务: contractId={}, sealType={}", request.getContractId(), request.getSealType());
        
        try {
            // 模拟调用第三方盖章服务
            // 实际实现中这里应该调用真实的第三方接口
            
            // 生成请求ID
            String requestId = generateRequestId(request.getContractId());
            
            // 模拟异步盖章
            simulateAsyncSeal(request, requestId);
            
            // 返回成功响应
            SealResponse response = new SealResponse();
            response.setSuccess(true);
            response.setRequestId(requestId);
            response.setMessage("盖章请求已提交，等待异步回调");
            
            return response;
            
        } catch (Exception e) {
            log.error("调用盖章服务异常: contractId={}", request.getContractId(), e);
            
            SealResponse response = new SealResponse();
            response.setSuccess(false);
            response.setErrorMessage("盖章服务调用异常: " + e.getMessage());
            
            return response;
        }
    }

    /**
     * 模拟异步盖章处理
     */
    private void simulateAsyncSeal(SealRequest request, String requestId) {
        // 在实际实现中，这里会调用第三方接口
        // 第三方会异步处理盖章请求，并通过回调通知结果
        
        log.info("模拟异步盖章处理: contractId={}, requestId={}", request.getContractId(), requestId);
        
        // 这里可以发送HTTP请求到第三方盖章系统
        // 第三方系统会在盖章完成后调用我们的回调接口
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId(String contractId) {
        return "SEAL_" + contractId + "_" + System.currentTimeMillis();
    }

    /**
     * 盖章请求
     */
    public static class SealRequest {
        private String contractId;
        private String processInstanceId;
        private String triggerType;
        private String sealType;
        private String documentUrl;
        private String documentId;
        private String sealPosition;
        private String description;

        // Getters and Setters
        public String getContractId() { return contractId; }
        public void setContractId(String contractId) { this.contractId = contractId; }
        
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public String getTriggerType() { return triggerType; }
        public void setTriggerType(String triggerType) { this.triggerType = triggerType; }
        
        public String getSealType() { return sealType; }
        public void setSealType(String sealType) { this.sealType = sealType; }
        
        public String getDocumentUrl() { return documentUrl; }
        public void setDocumentUrl(String documentUrl) { this.documentUrl = documentUrl; }
        
        public String getDocumentId() { return documentId; }
        public void setDocumentId(String documentId) { this.documentId = documentId; }
        
        public String getSealPosition() { return sealPosition; }
        public void setSealPosition(String sealPosition) { this.sealPosition = sealPosition; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    /**
     * 盖章响应
     */
    public static class SealResponse {
        private boolean success;
        private String requestId;
        private String message;
        private String errorMessage;
        private Object data;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }
}
