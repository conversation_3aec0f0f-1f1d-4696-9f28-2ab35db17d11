package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.QueryVipAuditDetailRes;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
@Data
public class QueryVipAuditDetailBo {
    private int retCode; // 返回码
    private String retMsg; // 返回消息
    private String mobilePhone; // 用户手机号
    private String costBearOrgCode; // 成本承担运营机构
    private int isExemptDeposit; // 是否免押：1-是、2-否
    private int isPrePay; // 是否预付：1-是、2-否
    private int discountType; // 折扣类型：1-整单折扣、2-整单减至
    private long discountContent; // 折扣内容
    private String remark; // 备注信息
    private List<String> fileUrl; // 附件地址列表(阿里云的地址)，最多支持5个
    private String oaAuthId; // oa审批单号
    private int oaAuthStatus; // oa审批状态：1-初始（审批中）、2-审批通过、3-审批拒绝
    private String auditStartTime; // 审批发起时间，格式：yyyyMMddHHmmss
    private String auditResultTime; // 审批完成时间，格式：yyyyMMddHHmmss

    public QueryVipAuditDetailRes toRes() {
        return QueryVipAuditDetailRes.newBuilder()
                .setRetCode(retCode)
                .setRetMsg(retMsg)
                .setMobilePhone(mobilePhone)
                .setCostBearOrgCode(costBearOrgCode)
                .setIsExemptDeposit(isExemptDeposit)
                .setIsPrePay(isPrePay)
                .setDiscountType(discountType)
                .setDiscountContent(discountContent)
                .setRemark(remark)
                .addAllFileUrl(CollectionUtils.isEmpty(fileUrl) ? new ArrayList<>() : fileUrl)
                .setOaAuthId(oaAuthId)
                .setOaAuthStatus(oaAuthStatus)
                .setAuditStartTime(auditStartTime)
                .setAuditResultTime(auditResultTime)
                .build();
    }
}
