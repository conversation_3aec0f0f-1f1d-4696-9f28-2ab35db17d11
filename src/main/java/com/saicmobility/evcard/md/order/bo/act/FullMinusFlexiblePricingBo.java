package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class FullMinusFlexiblePricingBo {
    private String days;  //满多少天  天数
    private String minDiscountAmount;   //最小优惠金额
    private String maxDiscountAmount;   //最大优惠金额

    public static FullMinusFlexiblePricingBo from(FullMinusFlexiblePricing item) {
        FullMinusFlexiblePricingBo bo = new FullMinusFlexiblePricingBo();
        bo.setDays(item.getDays());
        bo.setMinDiscountAmount(item.getMinDiscountAmount());
        bo.setMaxDiscountAmount(item.getMaxDiscountAmount());
        return bo;
    }
}
