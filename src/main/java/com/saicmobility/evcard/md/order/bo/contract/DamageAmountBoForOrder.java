package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.DamageAmount;
import com.saicmobility.evcard.md.mdorderservice.api.DamageAmountItem;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class DamageAmountBoForOrder {

    private BigDecimal damageAmount; // 总违约金额

    private List<DamageAmountItemForOrder> damageAmountItem; // 违约金明细列表

    public DamageAmount toRes() {
        DamageAmount.Builder builder = DamageAmount.newBuilder();
        builder.setDamageAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(damageAmount.toPlainString()));
        if (CollectionUtils.isNotEmpty(damageAmountItem)) {
            List<DamageAmountItem> itemList = damageAmountItem.stream().map(DamageAmountItemForOrder::toRes).collect(Collectors.toList());
            builder.addAllDamageAmountItem(itemList);
        }
        return builder.build();
    }
}
