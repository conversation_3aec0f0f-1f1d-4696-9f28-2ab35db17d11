package com.saicmobility.evcard.md.order.bo.external.billing;

import java.io.Serializable;
import java.math.BigDecimal;

import com.saicmobility.evcard.md.mdbillingservice.api.CalServiceAmount;

import lombok.Data;

@Data
public class ServiceAmountBo implements Serializable {

    // 根据item表的amountType
    private Integer amountType;
    // 金额
    private BigDecimal amount;
    //  数量（天数）
    private BigDecimal num;
    //  单价
    private BigDecimal unitPrice;
    // 原价  上门送取服务费使用，折扣前金额
    private BigDecimal originalAmount;

    private String extra;

    // 扩展字段
    private String ext;

    // 扩展字段2
    private String ext2;

    public static ServiceAmountBo from(CalServiceAmount serviceAmount) {
        ServiceAmountBo serviceAmountBo = new ServiceAmountBo();
        serviceAmountBo.setAmountType(serviceAmount.getAmountType());
        serviceAmountBo.setAmount(new BigDecimal(serviceAmount.getAmount()));
        serviceAmountBo.setNum(new BigDecimal(serviceAmount.getNum()));
        serviceAmountBo.setUnitPrice(new BigDecimal(serviceAmount.getUnitPrice()));
        serviceAmountBo.setExtra(serviceAmount.getExtra());
        return serviceAmountBo;
    }
}
