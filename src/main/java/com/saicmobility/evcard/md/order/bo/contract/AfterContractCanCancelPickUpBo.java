package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.GetAfterContractCanCancelPickUpRes;

import lombok.Data;

/**
 * 取消上门取车服务条件
 */
@Data
public class AfterContractCanCancelPickUpBo {

    private Integer freeCancelStatus; // 1：免费取消容时内 2：免费取消容时外，在还车时间点前 3：还车时间点后，迟到容时内，巡检迟到 4：还车时间点后，迟到容时内，巡检未迟到 5：还车时间点后，迟到超过容时，巡检迟到 6：还车时间点后，迟到超过容时，巡检未迟到

    private String returnPickUpFee; // 退上门取车服务费金额

    private String payPickUpDamageFee; // 收上门取车服务取消违约金额

    private String payOvertimeDamageFee; // 收超时违约金额

    private String coupon; // 补偿优惠券金额

    public static AfterContractCanCancelPickUpBo buildRes(int freeCancelStatus, String returnPickUpFee,
                                                          String payPickUpDamageFee, String payOvertimeDamageFee,
                                                          String coupon) {
        AfterContractCanCancelPickUpBo bo = new AfterContractCanCancelPickUpBo();
        bo.setFreeCancelStatus(freeCancelStatus);
        bo.setReturnPickUpFee(returnPickUpFee);
        bo.setPayPickUpDamageFee(payPickUpDamageFee);
        bo.setPayOvertimeDamageFee(payOvertimeDamageFee);
        bo.setCoupon(coupon);
        return bo;
    }

    public GetAfterContractCanCancelPickUpRes toRes() {
        return GetAfterContractCanCancelPickUpRes.newBuilder()
                .setFreeCancelStatus(freeCancelStatus)
                .setReturnPickUpFee(returnPickUpFee)
                .setPayPickUpDamageFee(payPickUpDamageFee)
                .setPayOvertimeDamageFee(payOvertimeDamageFee)
                .setCoupon(coupon)
                .build();
    }
}
