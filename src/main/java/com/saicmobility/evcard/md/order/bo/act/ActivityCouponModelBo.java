package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.ActivityCouponModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/22 22:36
 */
@Data
public class ActivityCouponModelBo {

    //活动券模板记录id
    private Long activityCouponId;

    //所属市场活动编号
    private Long activityId;

    //活动状态（0:待发布 1:待上线 2:进行中 3:已停止 4:暂停中）
    private Long activityStatus;

    //优惠券模板编号
    private Long couponSeq;

    //优惠券模板名称
    private String couponName;

    //所属运营公司编号，记录当时活动所属的运营公司作为发券单位
    private String orgCode;

    //活动类型
    private Integer activityType;

    //有效期类型（1固定起止时间 2灵活时长-到帐后n天起生效m天）
    private Integer validTimeType;

    //有效时长：生效后几天过期(validTimeType=2时有值)
    private Integer validDays;

    //有效时长: 到账后几天开始生效(validTimeType=2时有值)
    private Integer effectiveDays;

    //有效期：开始日期 YYYY-MM-DD(validTimeType=1时有值)
    private String startDate;

    //有效期：结束日期 YYYY-MM-DD(validTimeType=1时有值)
    private String expiresDate;

    //发放张数，缺省为1
    private Integer offerQuantity;

    //发券目标对象，0缺省 1仅邀请人 2仅被邀请人(仅邀请好友活动使用)
    private Integer couponTarget;

    //优惠券类型 1:直扣 2：折扣.
    private Integer couponType;

    public static ActivityCouponModelBo from(ActivityCouponModel coupon) {
        ActivityCouponModelBo bo = new ActivityCouponModelBo();
        bo.setActivityCouponId(coupon.getActivityCouponId());
        bo.setActivityId(coupon.getActivityId());
        bo.setActivityStatus(coupon.getActivityStatus());
        bo.setCouponSeq(coupon.getCouponSeq());
        bo.setCouponName(coupon.getCouponName());
        bo.setOrgCode(coupon.getOrgCode());
        bo.setActivityType(coupon.getActivityType());
        bo.setValidTimeType(coupon.getValidTimeType());
        bo.setValidDays(coupon.getValidDays());
        bo.setEffectiveDays(coupon.getEffectiveDays());
        bo.setStartDate(coupon.getStartDate());
        bo.setExpiresDate(coupon.getExpiresDate());
        bo.setOfferQuantity(coupon.getOfferQuantity());
        bo.setCouponTarget(coupon.getCouponTarget());
        bo.setCouponType(coupon.getCouponType());
        return bo;
    }
}
