package com.saicmobility.evcard.md.order.bo.drivingrecord;

import com.saicmobility.evcard.md.mdorderservice.api.CalFeeDetail;
import com.saicmobility.evcard.md.mdorderservice.api.RentContractItem;
import com.saicmobility.evcard.md.mdorderservice.api.SearchDrivingRecordRes;
import com.saicmobility.evcard.md.order.bo.contract.NightServiceAmountInfoBo;
import com.saicmobility.evcard.md.order.bo.fee.CalFeeDetailBo;
import com.saicmobility.evcard.md.order.dto.contractitem.AddFuelServiceAmountInfoDto;
import com.saicmobility.evcard.md.order.dto.contractitem.FuelSubAmountInfoDto;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 行程记录结果
 */
@Data
public class SearchDrivingRecordBo {

    // 租车合同项列表
    private List<RentContractItemBo> rentContractItem;

    //  当日油价
    private String oilPrice;

    // 油价城市
    private String oilCityName;

    //  燃油标号
    private int oilLabel;

	// 电单价
	private String eleUnitPrice;

    //  合同总金额
    private String contractTotalAmount;

    //  已支付预付款金额
    private String preTotalAmount;

    //  超时违约金
    private String overTimeDamageAmount;

	// 超时违约金描述
	private String overTimeDamageAmountExtra;

    //  巡检迟到，赔偿减免金额
    private String compensationDiscount;

    // 上门取车服务相关信息
    private PickUpServerInfoBo pickUpServerInfoBo;

	private FuelSubAmountInfoDto oilSubInfo; // 燃油差值

	private FuelSubAmountInfoDto eleSubInfo; // 电量差值

	private AddFuelServiceAmountInfoDto addOilServiceInfo; // 补油服务费

	private AddFuelServiceAmountInfoDto addEleServiceInfo; // 补电服务费

	private NightServiceAmountInfoBo nightServiceAmountInfoBo; // 还车后需退还夜间服务费

	private String forceRenewAmount; // 强行续租金额

	private List<CalFeeDetailBo> orgFeeDetail; // 费用明细

	private String reduceUpgradeVehicleAmount; // 免费升级车型的减免

	private String vipDiscountAmount;// vip优惠金额

    public SearchDrivingRecordRes toRes() {
        SearchDrivingRecordRes.Builder builder = SearchDrivingRecordRes.newBuilder()
                .addAllRentContractItem(getRentContractItemList())
                .setOilPrice(oilPrice)
                .setOilName(oilCityName)
				.setOilLabel(oilLabel).setEleUnitPrice(eleUnitPrice);
		builder.addAllFeeDetail(getOrgFeeDetailList());
		if (StringUtils.isNoneBlank(contractTotalAmount)
				&& new BigDecimal(contractTotalAmount).compareTo(BigDecimal.ZERO) != 0) {
			builder.setContractTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(contractTotalAmount));
		} else {
			builder.setContractTotalAmount("0");
		}
		if (StringUtils.isNoneBlank(preTotalAmount)) {
			builder.setPreTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(preTotalAmount));
		} else {
			builder.setPreTotalAmount(null);
		}
		if (StringUtils.isNoneBlank(overTimeDamageAmount)
				&& new BigDecimal(overTimeDamageAmount).compareTo(BigDecimal.ZERO) != 0) {
			builder.setOverTimeDamageAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(overTimeDamageAmount));
		} else {
			builder.setOverTimeDamageAmount(null);
		}
		builder.setOverTimeDamageAmountExtra(overTimeDamageAmountExtra);
		if (StringUtils.isNoneBlank(compensationDiscount)
				&& new BigDecimal(compensationDiscount).compareTo(BigDecimal.ZERO) != 0) {
			builder.setCompensationDiscount(BigDecimalUtil.toPlainStringNoDecimalPoint(compensationDiscount));
		} else {
			builder.setCompensationDiscount(null);
		}
        if (pickUpServerInfoBo != null) {
            builder.setPickUpServerInfo(pickUpServerInfoBo.toRes());
        }
		if (oilSubInfo != null) {
			builder.setOilSubInfo(oilSubInfo.toRes());
		}
		if (eleSubInfo != null) {
			builder.setEleSubInfo(eleSubInfo.toRes());
		}
		if (addOilServiceInfo != null) {
			builder.setAddOilServiceInfo(addOilServiceInfo.toRes());
		}
		if (addEleServiceInfo != null) {
			builder.setAddEleServiceInfo(addEleServiceInfo.toRes());
		}
		if (nightServiceAmountInfoBo != null) {
			builder.setNightServiceInfo(nightServiceAmountInfoBo.toRes());
		}
		if (StringUtils.isNotEmpty(forceRenewAmount)) {
			builder.setForceRenewAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(forceRenewAmount));
		}
		if(StringUtils.isNotEmpty(reduceUpgradeVehicleAmount)) {
			builder.setReduceUpgradeVehicleAmount(reduceUpgradeVehicleAmount);
		}

		if(StringUtils.isNotEmpty(vipDiscountAmount)){
			builder.setVipDiscountAmount(vipDiscountAmount);
		}

        return builder.build();
    }

    private List<RentContractItem> getRentContractItemList(){
        List<RentContractItem> rentContractItemList = new ArrayList<>();
        if (CollectionUtils.isEmpty(rentContractItem)) {
            return rentContractItemList;
        }
        for (RentContractItemBo rentContractItemBo : rentContractItem) {
            rentContractItemList.add(rentContractItemBo.toRes());
        }
        return rentContractItemList;
    }

	private List<CalFeeDetail> getOrgFeeDetailList() {
		List<CalFeeDetail> orgFeeDetailList = new ArrayList<>();
		if (CollectionUtils.isEmpty(orgFeeDetail)) {
			return orgFeeDetailList;
		}
		for (CalFeeDetailBo calFeeDetailBo : orgFeeDetail) {
			orgFeeDetailList.add(calFeeDetailBo.toRes());
		}
		return orgFeeDetailList;
	}
}
