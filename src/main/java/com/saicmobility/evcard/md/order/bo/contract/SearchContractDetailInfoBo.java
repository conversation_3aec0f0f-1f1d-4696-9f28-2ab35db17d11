package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.*;
import com.saicmobility.evcard.md.order.bo.drivingrecord.OilAmountBo;
import com.saicmobility.evcard.md.order.bo.feedback.FeedbackInfoBo;
import com.saicmobility.evcard.md.order.dto.contractitem.AddFuelServiceAmountInfoDto;
import com.saicmobility.evcard.md.order.dto.contractitem.FuelSubAmountInfoDto;
import com.saicmobility.evcard.md.order.entity.CarRentalContractExemptionItem;
import com.saicmobility.evcard.md.order.utils.AccompanyingCardUtil;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
public class SearchContractDetailInfoBo {

    private String mid;

    private String settlementOrderNo; // 结算订单号

    private Integer contractStatus; //  合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消


    private Long pickUpStoreId; //  计划取车门店id

    private String pickUpOrgCode; // 取车门店机构

    private Long pickUpShopSeq; //  取车虚拟门店id

    private String pickUpStoreName; //  计划取车门店名称

    private String pickUpDateTime; //  计划取车时间 yyyymmddhhmmss


    private Integer chooseSendService; // 是否选择了送车上门服务 1是 2否

    private String sendPointName; // 送车上门取车点名称

    private String sendPointAddress; // 送车上门取车点地址

    private String sendPointLongitude; // 送车上门经度

    private String sendPointLatitude; // 送车上门维度

    private String sendPointDistance; // 取车点距离，单位：公里

    private Long sendDeliveryServiceId; // 送车上门服务id


    private Long returnStoreId; //  计划还车门店id

    private String returnOrgCode; // 还车门店机构

    private Long returnShopSeq; //  还车虚拟门店id

    private String returnStoreName; //  计划还车门店名称

    private String returnDateTime; //  计划还车时间 yyyymmddhhmmss


    private Integer choosePickUpService; //  // 选择上门取车服务 1是 2否

    private String pickUpPointName; // 上门取车取车点名称

    private String pickUpPointAddress; // 上门取车取车点地址

    private String pickUpPointLongitude; // 上门取车经度

    private String pickUpPointLatitude; // 上门取车维度

    private String pickUpPointDistance; // 上门取车取车点距离，单位：公里

    private Long pickUpDeliveryServiceId; // 上门取车服务id

    private long goodsModelId; // 商品车型id

    private String goodsModelName; // 商品车型名称

    private String vin; // 车架号

    private String vehicleNo; // 车牌号

    private String vehicleModelPicForAndroid; // 车型图片

    private String vehicleModelPicForIos; // 车型图片

    private List<ContractTravelInfoBo> contractTravelInfo; // 行程列表

    private String totalAmount = "0.00"; // 订单总金额

    private String realAmount = "0.00"; // 订单实付金额

    private String mileage; // 用车里程

    private Integer costTime; // 用车时长

    private String drivingLicenseFrontPage; // 车辆行驶证正页图片

    private String drivingLicenseSupplementPage; // 车辆行驶证副页图片

    // ---- 费用明细 ----

    private RentAmountAppBo rent; //  租车费

	 private OilAmountBo oil; // 燃油费

    private ServiceAmountAppBo service; // 服务费

    private ServiceAmountAppBo addService; // 增值服务费

    private DamageAmountBoForOrder damage; // 违约金

    private DiscountAmountAppBo discount; // 折扣减免

    private String storeDiscountTotalAmount; // 门店总减免金额

    private String customerDiscountTotalAmount; // 客服总减免金额

    private String reduceUpgradeVehicleAmount; // 免费升级车型的减免

    //---- 支付方式 ----

    private List<PayInfo> payInfo; // 支付信息

    //---- 退款信息 ----

    private List<RefundInfo> refundInfo; // 退款信息

    private RefundFail refundFailInfo; // 预付款退款失败信息

    //---- 反馈信息 ----
    private FeedbackInfoBo feedbackInfo; // 反馈信息  为空时说明未评价

    //   燃油类别 0 纯电动,1 油电混动,2燃料电池,3 燃油
    private int oilType;
    //   订单总数
    private int orderCount;
    //   碳减排量
    private int reduceCarbon;
    //   分享可得的积分
    private int shareCanIntegral;
    //  订单发放的优惠券列表
    List<OrderRewardCoupon> couponDtoList = new ArrayList<>();

	private FuelSubAmountInfoDto oilSubInfo; // 燃油差值

	private FuelSubAmountInfoDto eleSubInfo; // 电量差值

	private AddFuelServiceAmountInfoDto addOilServiceInfo; // 补油服务费

	private AddFuelServiceAmountInfoDto addEleServiceInfo; // 补电服务费

    // -----------------随享卡减免明细------------------
    // 随享卡减免明细
    private List<CarRentalContractExemptionItem> accompanyingCardItemExemptionRecordList;
    // 随享卡id
    private long userAccompanyingCardId;

    // 保障服务费
    private ServiceAmountAppBo assureService;

    // 门店手续费
    private ServiceAmountAppBo storeProcedures;

    //增值业务费
    private ServiceAmountAppBo valueAddService;


    private long vehicleModelId;//资产车型id  车生活使用

    //补充金额
    private String supplyAmount;

    private int contractStatusNew; // 合同状态新： 1:已预约(待支付) 2:待取车(备车中) 3:待取车(已备车) 4:已取车 5:还车中(收车中) 6:已还车(待支付) 7:已取消(待支付) 8:已完成(已支付) 9:已取消(已支付) 10:已取消

    private int isHideOrderFlag;//是否隐藏订单： 0：默认值，否；1：是

    private String vipDiscountAmount;//vip折扣减免金额
    public SearchContractDetailInfoRes toRes() {
        List<ContractTravelInfo> contractTravelInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractTravelInfo)) {
            for (ContractTravelInfoBo bo : contractTravelInfo) {
                contractTravelInfoList.add(bo.toRes());
            }
        }

        SearchContractDetailInfoRes.Builder builder = SearchContractDetailInfoRes.newBuilder()
                .setContractStatus(contractStatus)
                .setPickUpStoreId(pickUpStoreId)
                .setPickUpOrgCode(pickUpOrgCode)
                .setPickUpShopSeq(pickUpShopSeq)
                .setPickUpStoreName(Optional.ofNullable(pickUpStoreName).orElse(StringUtils.EMPTY))
                .setPickUpDateTime(pickUpDateTime)
                .setChooseSendService(chooseSendService)
                .setSendPointName(Optional.ofNullable(sendPointName).orElse(StringUtils.EMPTY))
                .setSendPointAddress(Optional.ofNullable(sendPointAddress).orElse(StringUtils.EMPTY))
                .setSendPointLongitude(Optional.ofNullable(sendPointLongitude).orElse(StringUtils.EMPTY))
                .setSendPointLatitude(Optional.ofNullable(sendPointLatitude).orElse(StringUtils.EMPTY))
                .setSendPointDistance(Optional.ofNullable(sendPointDistance).orElse(StringUtils.EMPTY))
                .setSendDeliveryServiceId(Optional.ofNullable(sendDeliveryServiceId).orElse(0l))
                .setReturnStoreId(Optional.ofNullable(returnStoreId).orElse(0l))
                .setReturnOrgCode(returnOrgCode)
                .setReturnShopSeq(Optional.ofNullable(returnShopSeq).orElse(0l))
                .setReturnStoreName(Optional.ofNullable(returnStoreName).orElse(StringUtils.EMPTY))
                .setReturnDateTime(Optional.ofNullable(returnDateTime).orElse(StringUtils.EMPTY))
                .setChoosePickUpService(choosePickUpService)
                .setPickUpPointName(Optional.ofNullable(pickUpPointName).orElse(StringUtils.EMPTY))
                .setPickUpPointAddress(Optional.ofNullable(pickUpPointAddress).orElse(StringUtils.EMPTY))
                .setPickUpPointLongitude(Optional.ofNullable(pickUpPointLongitude).orElse(StringUtils.EMPTY))
                .setPickUpPointLatitude(Optional.ofNullable(pickUpPointLatitude).orElse(StringUtils.EMPTY))
                .setPickUpPointDistance(Optional.ofNullable(pickUpPointDistance).orElse(StringUtils.EMPTY))
                .setPickUpDeliveryServiceId(Optional.ofNullable(pickUpDeliveryServiceId).orElse(0l))
                .setGoodsModelId(goodsModelId)
                .setGoodsModelName(Optional.ofNullable(goodsModelName).orElse(StringUtils.EMPTY))
                .setVin(Optional.ofNullable(vin).orElse(StringUtils.EMPTY))
                .setVehicleNo(Optional.ofNullable(vehicleNo).orElse(StringUtils.EMPTY))
                .setVehicleModelPicForAndroid(Optional.ofNullable(vehicleModelPicForAndroid).orElse(StringUtils.EMPTY))
                .setVehicleModelPicForIos(Optional.ofNullable(vehicleModelPicForIos).orElse(StringUtils.EMPTY))
                .addAllContractTravelInfo(contractTravelInfoList)
                .setTotalAmount(Optional.ofNullable(BigDecimalUtil.toPlainStringNoDecimalPoint(totalAmount)).orElse(StringUtils.EMPTY))
                .setRealAmount(Optional.ofNullable(BigDecimalUtil.toPlainStringNoDecimalPoint(realAmount)).orElse(StringUtils.EMPTY))
                .setMileage(Optional.ofNullable(mileage).orElse(StringUtils.EMPTY))
                .setCostTime(Optional.ofNullable(costTime).orElse(0))
                .setDrivingLicenseFrontPage(Optional.ofNullable(drivingLicenseFrontPage).orElse(StringUtils.EMPTY))
                .setDrivingLicenseSupplementPage(Optional.ofNullable(drivingLicenseSupplementPage).orElse(StringUtils.EMPTY))
                .setStoreDiscountTotalAmount(Optional.ofNullable(storeDiscountTotalAmount).orElse(StringUtils.EMPTY))
                .setCustomerDiscountTotalAmount(Optional.ofNullable(customerDiscountTotalAmount).orElse(StringUtils.EMPTY))
                .setOilType(oilType)
                .setOrderCount(orderCount)
                .setReduceCarbon(reduceCarbon)
                .setShareCanIntegral(shareCanIntegral)
                .setVehicleModelId(getVehicleModelId())
                .setReduceUpgradeVehicleAmount(getReduceUpgradeVehicleAmount())
                .setVipDiscountAmount(vipDiscountAmount)
                .addAllOrderCouponDto(couponDtoList);

        if (rent != null) {
            builder.setRent(rent.toRes());
        }
		 if (oil != null) {
		 builder.setOil(oil.toRes());
		 }
        if (service != null) {
            builder.setService(service.toRes());
        }
        if (assureService != null && assureService.getServiceAmount() != null) {
            builder.setAssureService(assureService.toRes());
        }
        if (storeProcedures != null && storeProcedures.getServiceAmount() != null) {
            builder.setStoreProcedures(storeProcedures.toRes());
        }

        if (valueAddService != null && valueAddService.getServiceAmount() != null) {
            builder.setValueAddService(valueAddService.toRes());
        }


        if (addService != null) {
            builder.setAddService(addService.toRes());
        }
        if (damage != null) {
            builder.setDamage(damage.toRes());
        }
        if (discount != null) {
            builder.setDiscount(discount.toRes());
        }
        if (payInfo != null) {
            builder.addAllPayInfo(payInfo);
        }
        if (refundInfo != null) {
            builder.addAllRefundInfo(refundInfo);
        }
        if (refundFailInfo != null) {
            builder.setRefundFailInfo(refundFailInfo);
        }
        if (feedbackInfo != null) {
            builder.setFeedbackInfo(feedbackInfo.toRes());
        }
		if (oilSubInfo != null) {
			builder.setOilSubInfo(oilSubInfo.toRes());
		}
		if (eleSubInfo != null) {
			builder.setEleSubInfo(eleSubInfo.toRes());
		}
		if (addOilServiceInfo != null) {
			builder.setAddOilServiceInfo(addOilServiceInfo.toRes());
		}
		if (addEleServiceInfo != null) {
			builder.setAddEleServiceInfo(addEleServiceInfo.toRes());
		}
        AccompanyingCardAmount accompanyingCardAmount = AccompanyingCardUtil.returnCarToRes(accompanyingCardItemExemptionRecordList, userAccompanyingCardId);
        if (accompanyingCardAmount != null) {
            builder.setAccompanyingCardAmount(accompanyingCardAmount);
        }

        builder.setSupplyAmount(supplyAmount);
        builder.setContractStatusNew(contractStatusNew);

        return builder.build();
    }
}
