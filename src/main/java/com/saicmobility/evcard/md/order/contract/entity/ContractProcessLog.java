package com.saicmobility.evcard.md.order.contract.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 合同流程操作日志表实体
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_contract_process_log")
public class ContractProcessLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 源状态
     */
    private String fromState;

    /**
     * 目标状态
     */
    private String toState;

    /**
     * 操作结果(SUCCESS/FAILED)
     */
    private String operationResult;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 第三方请求ID
     */
    private String thirdPartyRequestId;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求头
     */
    private String requestHeaders;

    /**
     * 响应状态码
     */
    private Integer responseStatus;

    /**
     * 响应头
     */
    private String responseHeaders;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 状态（0=正常   1=已删除）
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createOperId;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createOperName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateOperId;

    /**
     * 更新人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateOperName;

    /**
     * 创建时间（保留原字段，兼容现有代码）
     * @deprecated 请使用 createTime
     */
    @Deprecated
    private LocalDateTime createdTime;
}
