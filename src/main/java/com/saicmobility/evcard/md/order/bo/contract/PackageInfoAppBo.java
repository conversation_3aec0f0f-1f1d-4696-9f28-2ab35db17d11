package com.saicmobility.evcard.md.order.bo.contract;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;

import com.saicmobility.evcard.md.order.enums.IsEarlyBirdEnum;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import org.apache.commons.lang3.StringUtils;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdactservice.api.PackageConfig;
import com.saicmobility.evcard.md.mdorderservice.api.PackageInfo;
import com.saicmobility.evcard.md.order.bo.external.rentpackage.PackageDetailInfoBo;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;

import lombok.Data;

@Data
public class PackageInfoAppBo {

    //  套餐id
    private Long packageId;
    //  套餐类型 1标准套餐 2套餐 3零散小时
    private Integer packageType;
    //  套餐名称
    private String packageName;
    //  天数，如果packageType=3零散小时，此字段表示小时数
    private Integer daysNumber;
    //  总价
    private String totalPrice;
    //  套餐时间描述
    private String packageDesc;
    // 套餐日均价格  总价/天数，如果packageType=3零散小时，此字段表示小时均价
    private String packageAverageAmount;
    // 是否可用早鸟套餐 1 是 2 否
    private Integer earlyBirdFlag;
    // 使用早鸟套餐后的总价
    private String earlyBirdTotalPrice;
    // 使用早鸟套餐后的日均价格 使用早鸟套餐后的总价/天数
    private String earlyBirdPackageAverageAmount;
    // 早鸟套餐的开始时间，格式：yyyyMMdd
    private String earlyBirdStartDate;
    // 早鸟套餐的结束时间，格式：yyyyMMdd
    private String earlyBirdEndDate;
    // 距早鸟活动结束还剩几天
    private Integer earlyBirdDaysLeft;
    // 早鸟套餐免费取消容时（下单xx分钟后取消早鸟订单，费用不退）
    private Integer earlyBirdCancelTimeTolerance;
    // 升级为早鸟模式，立省xx元
    private String earlyBirdSaveMoney;

    public static PackageInfoAppBo from(PackageDetailInfoBo packageDetailInfoBo) {
        PackageInfoAppBo bo = new PackageInfoAppBo();
        bo.setPackageId(packageDetailInfoBo.getId());
        bo.setPackageType(BusinessConstants.PACKAGE_TYPE_PACK);
        bo.setPackageName(packageDetailInfoBo.getPackageName());
        bo.setDaysNumber(packageDetailInfoBo.getDaysNumber());
        bo.setTotalPrice(packageDetailInfoBo.getTotalPrice());
        bo.setPackageDesc(packageDetailInfoBo.getDesc());
        bo.setPackageAverageAmount(new BigDecimal(packageDetailInfoBo.getTotalPrice()).divide(new BigDecimal(packageDetailInfoBo.getDaysNumber()), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
        bo.setEarlyBirdFlag(packageDetailInfoBo.getEarlyFlag());

        if (packageDetailInfoBo.getEarlyFlag() == IsEarlyBirdEnum.YES.getValue()) {
            bo.setEarlyBirdTotalPrice(packageDetailInfoBo.getEarlyPrice());
            bo.setEarlyBirdPackageAverageAmount(new BigDecimal(packageDetailInfoBo.getEarlyPrice()).divide(new BigDecimal(packageDetailInfoBo.getDaysNumber()), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
            if (StringUtils.isNotBlank(packageDetailInfoBo.getEarlyStartDate())) {
                bo.setEarlyBirdStartDate(LocalDate.parse(packageDetailInfoBo.getEarlyStartDate(), DateUtil.DATE_TYPE1)
                        .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            }
            if (StringUtils.isNotBlank(packageDetailInfoBo.getEarlyEndDate())) {
                bo.setEarlyBirdEndDate(LocalDate.parse(packageDetailInfoBo.getEarlyEndDate(), DateUtil.DATE_TYPE1)
                        .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                LocalDate earlyEndLocalDate = LocalDate.parse(packageDetailInfoBo.getEarlyEndDate(), DateUtil.DATE_TYPE1);
                LocalDate nowLocalDate = LocalDate.now();
                int days = (int) (earlyEndLocalDate.toEpochDay() - nowLocalDate.toEpochDay());
                int earlyBirdDaysLeft = days < 0 ? 0 : (days + 1);
                bo.setEarlyBirdDaysLeft(earlyBirdDaysLeft);

                // 如果早鸟活动已结束，那么设置标志为不能升级为早鸟
                if (earlyBirdDaysLeft == 0) {
                    bo.setEarlyBirdFlag(IsEarlyBirdEnum.NO.getValue());
                }
            }
            bo.setEarlyBirdSaveMoney(new BigDecimal(packageDetailInfoBo.getTotalPrice()).subtract(new BigDecimal(packageDetailInfoBo.getEarlyPrice())).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
        }

        return bo;
    }


    public static PackageInfoAppBo from(PackageConfig packageDetailInfoBo) {
        PackageInfoAppBo bo = new PackageInfoAppBo();
        bo.setPackageId(packageDetailInfoBo.getId());
        bo.setPackageType(BusinessConstants.PACKAGE_TYPE_PACK);
        bo.setPackageName(packageDetailInfoBo.getPackageName());
        bo.setDaysNumber(packageDetailInfoBo.getDaysNumber());
        bo.setTotalPrice(packageDetailInfoBo.getTotalPrice());
        bo.setPackageDesc(packageDetailInfoBo.getUseStartDate() + "-" + packageDetailInfoBo.getUseEndDate());
        return bo;
    }

    public PackageInfo toRes() {
        return PackageInfo.newBuilder()
                .setPackageId(packageId)
                .setPackageType(packageType)
                .setPackageName(packageName)
                .setDaysNumber(daysNumber)
                .setTotalPrice(BigDecimalUtil.toPlainString(totalPrice))
                .setPackageDesc(packageDesc)
                .setEarlyBirdFlag(earlyBirdFlag == null ? 0 : earlyBirdFlag)
                .setEarlyBirdTotalPrice(earlyBirdTotalPrice)
                .setEarlyBirdPackageAverageAmount(earlyBirdPackageAverageAmount)
                .setEarlyBirdStartDate(earlyBirdStartDate)
                .setEarlyBirdEndDate(earlyBirdEndDate)
                .setEarlyBirdDaysLeft(earlyBirdDaysLeft == null ? 0 :earlyBirdDaysLeft)
                .setEarlyBirdSaveMoney(earlyBirdSaveMoney)
                .build();
    }
}
