package com.saicmobility.evcard.md.order.bo.drivingrecord;

import com.saicmobility.evcard.md.mdorderservice.api.PickUpServerInfo;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

/**
 * 上门取车服务相关信息
 */
@Data
public class PickUpServerInfoBo {

    //  上门取车服务费金额
    private String pickUpServerAmount;

    //  服务创建时间
    private String createTime;

    //  取车距离	单位：km
    private String distance;

    //  取消违约金
    private String pickCancelDamageAmount;

    public PickUpServerInfo toRes() {
        return PickUpServerInfo.newBuilder()
                .setPickUpServerAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(pickUpServerAmount))
                .setCreateTime(createTime)
                .setDistance(distance)
                .setPickCancelDamageAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(pickCancelDamageAmount))
                .build();
    }
}
