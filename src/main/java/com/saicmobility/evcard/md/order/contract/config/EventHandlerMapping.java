package com.saicmobility.evcard.md.order.contract.config;

import lombok.Data;

import java.util.*;

/**
 * 事件处理器映射配置
 * 硬编码事件与处理器的映射关系
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
public class EventHandlerMapping {

    /**
     * 事件处理器映射关系
     * Key: 事件类型
     * Value: 处理器配置列表
     */
    private static final Map<String, List<HandlerConfig>> EVENT_HANDLER_MAP = new HashMap<>();

    /**
     * 状态转换映射关系
     * Key: 事件类型_当前状态
     * Value: 下一状态
     */
    private static final Map<String, String> STATE_TRANSITION_MAP = new HashMap<>();

    static {
        initEventHandlerMapping();
        initStateTransitionMapping();
    }

    /**
     * 初始化事件处理器映射关系
     */
    private static void initEventHandlerMapping() {
        // MQ消息接收事件 -> 验车处理器
        EVENT_HANDLER_MAP.put("MQ_MESSAGE_RECEIVED", Arrays.asList(
                new HandlerConfig("VehicleCheckHandler", 1, ExecutionMode.SERIAL, 3, 30)
        ));

        // 验车成功事件 -> 短信通知处理器
        EVENT_HANDLER_MAP.put("VEHICLE_CHECK_SUCCESS", Arrays.asList(
                new HandlerConfig("SmsNotificationHandler", 1, ExecutionMode.SERIAL, 3, 30)
        ));

        // 短信发送成功事件 -> 合同盖章处理器
        EVENT_HANDLER_MAP.put("SMS_SEND_SUCCESS", Arrays.asList(
                new HandlerConfig("ContractSealHandler", 1, ExecutionMode.SERIAL, 3, 30)
        ));

        // 盖章成功事件 -> 流程完成处理器
        EVENT_HANDLER_MAP.put("SEAL_SUCCESS", Arrays.asList(
                new HandlerConfig("ProcessCompleteHandler", 1, ExecutionMode.SERIAL, 3, 30)
        ));

        // 失败事件处理器（可选，用于记录失败信息）
        EVENT_HANDLER_MAP.put("VEHICLE_CHECK_FAILED", Arrays.asList(
                new HandlerConfig("FailureHandler", 1, ExecutionMode.SERIAL, 1, 10)
        ));

        EVENT_HANDLER_MAP.put("SMS_SEND_FAILED", Arrays.asList(
                new HandlerConfig("FailureHandler", 1, ExecutionMode.SERIAL, 1, 10)
        ));

        EVENT_HANDLER_MAP.put("SEAL_FAILED", Arrays.asList(
                new HandlerConfig("FailureHandler", 1, ExecutionMode.SERIAL, 1, 10)
        ));
    }

    /**
     * 初始化状态转换映射关系
     */
    private static void initStateTransitionMapping() {
        // MQ消息接收：INIT -> VEHICLE_CHECKING
        STATE_TRANSITION_MAP.put("MQ_MESSAGE_RECEIVED_INIT", "VEHICLE_CHECKING");

        // 验车成功：VEHICLE_CHECKING -> SMS_SENDING
        STATE_TRANSITION_MAP.put("VEHICLE_CHECK_SUCCESS_VEHICLE_CHECKING", "SMS_SENDING");

        // 验车失败：VEHICLE_CHECKING -> VEHICLE_CHECK_FAILED
        STATE_TRANSITION_MAP.put("VEHICLE_CHECK_FAILED_VEHICLE_CHECKING", "VEHICLE_CHECK_FAILED");

        // 短信发送成功：SMS_SENDING -> SEALING
        STATE_TRANSITION_MAP.put("SMS_SEND_SUCCESS_SMS_SENDING", "SEALING");

        // 短信发送失败：SMS_SENDING -> SMS_SEND_FAILED
        STATE_TRANSITION_MAP.put("SMS_SEND_FAILED_SMS_SENDING", "SMS_SEND_FAILED");

        // 盖章成功：SEALING -> PROCESS_COMPLETED
        STATE_TRANSITION_MAP.put("SEAL_SUCCESS_SEALING", "PROCESS_COMPLETED");

        // 盖章失败：SEALING -> SEAL_FAILED
        STATE_TRANSITION_MAP.put("SEAL_FAILED_SEALING", "SEAL_FAILED");
    }

    /**
     * 获取事件对应的处理器配置列表
     * 
     * @param eventType 事件类型
     * @return 处理器配置列表
     */
    public static List<HandlerConfig> getHandlerConfigs(String eventType) {
        return EVENT_HANDLER_MAP.getOrDefault(eventType, Collections.emptyList());
    }

    /**
     * 获取状态转换的下一状态
     * 
     * @param eventType 事件类型
     * @param currentState 当前状态
     * @return 下一状态，如果没有配置则返回null
     */
    public static String getNextState(String eventType, String currentState) {
        String key = eventType + "_" + currentState;
        return STATE_TRANSITION_MAP.get(key);
    }

    /**
     * 检查事件是否有对应的处理器
     * 
     * @param eventType 事件类型
     * @return 是否有处理器
     */
    public static boolean hasHandlers(String eventType) {
        List<HandlerConfig> configs = EVENT_HANDLER_MAP.get(eventType);
        return configs != null && !configs.isEmpty();
    }

    /**
     * 检查状态转换是否存在
     * 
     * @param eventType 事件类型
     * @param currentState 当前状态
     * @return 是否存在状态转换
     */
    public static boolean hasStateTransition(String eventType, String currentState) {
        String key = eventType + "_" + currentState;
        return STATE_TRANSITION_MAP.containsKey(key);
    }

    /**
     * 获取所有支持的事件类型
     * 
     * @return 事件类型集合
     */
    public static Set<String> getAllEventTypes() {
        return EVENT_HANDLER_MAP.keySet();
    }

    /**
     * 获取所有状态转换规则
     * 
     * @return 状态转换规则映射
     */
    public static Map<String, String> getAllStateTransitions() {
        return new HashMap<>(STATE_TRANSITION_MAP);
    }

    /**
     * 处理器配置
     */
    @Data
    public static class HandlerConfig {
        /**
         * 处理器名称
         */
        private String handlerName;

        /**
         * 执行顺序
         */
        private int executionOrder;

        /**
         * 执行模式
         */
        private ExecutionMode executionMode;

        /**
         * 重试次数
         */
        private int retryCount;

        /**
         * 超时时间(秒)
         */
        private int timeoutSeconds;

        public HandlerConfig(String handlerName, int executionOrder, ExecutionMode executionMode, 
                           int retryCount, int timeoutSeconds) {
            this.handlerName = handlerName;
            this.executionOrder = executionOrder;
            this.executionMode = executionMode;
            this.retryCount = retryCount;
            this.timeoutSeconds = timeoutSeconds;
        }
    }

    /**
     * 执行模式枚举
     */
    public enum ExecutionMode {
        /**
         * 串行执行
         */
        SERIAL,

        /**
         * 并行执行
         */
        PARALLEL
    }
}
