package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.QrCodeFeeAmountRes;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class QrCodeFeeAmountBo {
    private BigDecimal amount; //需要支付的金额
    private int orderStatus = 1; //订单状态 1待支付 2已取消 3已支付
    // 商品订单号
    private String goodsOrderNo = "";

    public QrCodeFeeAmountRes toRes(int orderStatus) {
        QrCodeFeeAmountRes.Builder builder = QrCodeFeeAmountRes.newBuilder().setGoodsOrderNo(getGoodsOrderNo()).setOrderStatus(orderStatus);
        if (amount != null) {
            builder.setAmount(amount.toPlainString());
        }
        return builder.build();
    }
}
