package com.saicmobility.evcard.md.order.bo.calpackagerent;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.saicmobility.evcard.md.order.bo.external.accompanyingcard.CalAccompanyingCardInfoBo;
import com.saicmobility.evcard.md.order.bo.external.billing.CalculateRentAmountBo;
import com.saicmobility.evcard.md.order.bo.external.rentpackage.PackageDetailInfoBo;
import com.saicmobility.evcard.md.order.constants.AmountType;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;
import com.saicmobility.evcard.md.order.support.CalFeeSupport;
import com.saicmobility.evcard.md.order.utils.AppVersionUtil;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import com.saicmobility.evcard.md.order.utils.ThreadLocalUtil;
import org.springframework.util.CollectionUtils;

import com.saicmobility.evcard.md.mdorderservice.api.*;
import com.saicmobility.evcard.md.order.bo.act.ActivityDeductionInfoBo;
import com.saicmobility.evcard.md.order.bo.act.ReduceActivityBo;
import com.saicmobility.evcard.md.order.bo.act.ReductionActivityConfigBo;
import com.saicmobility.evcard.md.order.bo.external.billing.CalculateRenewFeeBo;
import com.saicmobility.evcard.md.order.bo.external.billing.ExemptionAmountBo;
import com.saicmobility.evcard.md.order.bo.external.billing.ServiceAmountBo;
import com.saicmobility.evcard.md.order.bo.fee.CalFeeDetailBo;
import com.saicmobility.evcard.md.order.bo.fee.FeeDetailsBo;

import lombok.Data;
import org.springframework.util.StringUtils;

@Data
public class RenewCalContractAmountUseCardCouponAndServiceBo {

    //  预付款金额(应付租金+服务费-减免)
    private String amount;

    //  选中套餐计费
    private CalPackageRentAmountBo packageRentAmount;

    //  预付款中的租金
    private String rentAmount;

    //  续租随享卡弹窗金额
    private String renewAccompanyingCardTipAmount;

    //  活动卡券抵扣数据
    private ActivityDeductionInfoBo activityDeduction;

    //  费用明细
    private List<CalFeeDetailBo> feeDetail;

    //  标准计费金额(标准租金+服务费)
    private String standardCalAmount;

    // 随享卡信息
    private CalAccompanyingCardInfoBo accompanyingCardInfo;
    // 随享卡费用减免列表
    private List<ExemptionAmountBo> accompanyingCardInfoExemptionAmount;

    // V5.7.2费用明细
    private List<CalFeeDetailBo> newFeeDetail;

    // 实付金额 已减去上一笔要退款的零散部分
    private String realAmount;

    // 自营折扣id，无满足的活动时，返回-1；当上一笔子订单有自营折扣，则这里返回上一笔的；当上一笔子订单没有自营折扣，则本笔计算出最优并返回
    private long selfOperatedDiscountId = -1L;

    public static RenewCalContractAmountUseCardCouponAndServiceBo from(CalculateRenewFeeBo bo, ReduceActivityBo reduceActivityBo,
                                                                       PackageDetailInfoBo packageDetailInfoBo,
                                                                       CalculateRentAmountBo standardCalculateRentAmountBo,
                                                                       Map<Long, String> actMap) {
        RenewCalContractAmountUseCardCouponAndServiceBo res = new RenewCalContractAmountUseCardCouponAndServiceBo();

        // 租金 + 服务费 - 减免
        res.setAmount(bo.getOrderRealAmount().toPlainString());

        CalPackageRentAmountBo calPackageRentAmountBo = CalPackageRentAmountBo.from(bo.getCalRent(), standardCalculateRentAmountBo.getAverageAmount());
        if (packageDetailInfoBo != null) {
            calPackageRentAmountBo.setPackageInfo(PackageInfoBo.from(packageDetailInfoBo));
            calPackageRentAmountBo.setActivityType(BusinessConstants.PACKAGE_TYPE_PACK);
        } else {
            PackageInfoBo packageInfoBo = new PackageInfoBo();
            packageInfoBo.setPackageId(BusinessConstants.PACKAGE_STANDARD_ID);
            packageInfoBo.setPackageType(BusinessConstants.PACKAGE_TYPE_STANDARD);
            packageInfoBo.setPackageName("标准计费");
            packageInfoBo.setDaysNumber(standardCalculateRentAmountBo.getTotalRentDays());
            packageInfoBo.setTotalPrice(standardCalculateRentAmountBo.getTotalRentAmount().toPlainString());
            packageInfoBo.setPackageDesc("标准计费-日租");
            calPackageRentAmountBo.setPackageInfo(packageInfoBo);
            calPackageRentAmountBo.setActivityType(BusinessConstants.PACKAGE_TYPE_STANDARD);
        }
        res.setPackageRentAmount(calPackageRentAmountBo);

        ActivityDeductionInfoBo activityDeductionInfoBo = ActivityDeductionInfoBo.from(bo.getCalCard(),
                bo.getExemptionAmount(), reduceActivityBo);
        res.setActivityDeduction(activityDeductionInfoBo);

        List<CalFeeDetailBo> feeDetail = new ArrayList<>();

        // 租车费
        CalFeeSupport.getRentalFee(feeDetail, calPackageRentAmountBo, packageDetailInfoBo);

        List<ServiceAmountBo> serviceAmount = bo.getServiceAmount();
        if (!CollectionUtils.isEmpty(serviceAmount)) {
            // 燃油费
            CalFeeSupport.getOilFee(feeDetail, serviceAmount);
            // 燃油费差额
            CalFeeSupport.getOilDiffFee(feeDetail, serviceAmount);
            // 基础服务费
            CalFeeSupport.getBaseFee(feeDetail, serviceAmount);
            // 增值服务费
            CalFeeSupport.getAddFee(feeDetail, serviceAmount);
        }

        // 折扣减免
        List<ExemptionAmountBo> exemptionAmount = bo.getExemptionAmount();
        if (!CollectionUtils.isEmpty(exemptionAmount)) {
            CalFeeSupport.getDisCountFee(feeDetail, exemptionAmount);
        }

        res.setFeeDetail(feeDetail);


        // V5.7.2新版本费用明细
        List<CalFeeDetailBo> newFeeDetail = new ArrayList<>();
        // 租车费
        // appv5.12版本，增加设置日均租金、优惠前日均租金、租金明细，并把原来feeType=8抵扣的自营折扣、随享卡抵扣（企业折扣）、优惠券，转移到feeType=1租车费里；
        if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) >= 0) {
            CalFeeSupport.getRentalFeeNew(newFeeDetail, calPackageRentAmountBo, bo, actMap);
        } else {
            CalFeeSupport.getRentalFee(newFeeDetail, calPackageRentAmountBo, packageDetailInfoBo);
        }

        if (!CollectionUtils.isEmpty(serviceAmount)) {
            // 燃油费
            CalFeeSupport.getOilFee(newFeeDetail, serviceAmount);
            // 燃油费差额
            CalFeeSupport.getOilDiffFee(newFeeDetail, serviceAmount);

            // appv5.12，FeeDetails对象中的feeExplanation，需要把各种费用的 几元几天 随享卡抵扣几天，都拼接好，前端直接使用；
            if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) >= 0) {
                // 保障服务费
                CalFeeSupport.getAssureFeeNew(newFeeDetail, serviceAmount, bo.getAccompanyingCardInfoExemptionAmount(), false);
                // 门店手续费
                CalFeeSupport.getStoreProceduresFeeNew(newFeeDetail, serviceAmount, bo.getAccompanyingCardInfoExemptionAmount(), false);
            } else {
                // 保障服务费
                CalFeeSupport.getAssureFee(newFeeDetail, serviceAmount);
                // 门店手续费
                CalFeeSupport.getStoreProceduresFee(newFeeDetail, serviceAmount);
            }
            // 增值服务费
            CalFeeSupport.getAddFee(newFeeDetail, serviceAmount);
        }

        // 小于appv5.12版本，才需要返回折扣减免
        if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) < 0) {
            // 折扣减免
            if (!CollectionUtils.isEmpty(exemptionAmount)) {
                CalFeeSupport.getDisCountFee(newFeeDetail, exemptionAmount);
            }
        }

        res.setNewFeeDetail(newFeeDetail); // 费用明细


        res.setStandardCalAmount(bo.getStandardCalAmount().toPlainString());

        // 纯租金 - 租车费减免
        BigDecimal discountFee = feeDetail.stream().filter(fee -> fee.getFeeType() == 8).map(fee -> new BigDecimal(fee.getFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 随享卡减免租金
        BigDecimal accompanyingCardExemptionRentAMount =  BigDecimal.ZERO;
        if(!CollectionUtils.isEmpty(bo.getAccompanyingCardInfoExemptionAmount())) {
            accompanyingCardExemptionRentAMount = bo.getAccompanyingCardInfoExemptionAmount().stream()
                    .filter(item -> item.getAmountType().equals(AmountType.RENT_AMOUNT_STANDARD))
                    .map(ExemptionAmountBo :: getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        res.setRentAmount(new BigDecimal(calPackageRentAmountBo.getTotalAmount())
                .subtract(discountFee)
                .subtract(accompanyingCardExemptionRentAMount)
                .setScale(2, RoundingMode.HALF_UP)
                .toPlainString());
        res.setRenewAccompanyingCardTipAmount(calPackageRentAmountBo.getTotalAmount());

        res.setAccompanyingCardInfo(bo.getAccompanyingCardInfo());
        res.setAccompanyingCardInfoExemptionAmount(bo.getAccompanyingCardInfoExemptionAmount());

        // 设置自营折扣id
        res.setSelfOperatedDiscountId((bo.getActDiscount() == null || bo.getActDiscount().getActId() == 0) ? -1 : bo.getActDiscount().getActId());
        return res;
    }

    public RenewCalContractAmountUseCardCouponAndServiceRes toRes() {
        CalPackageRentAmountBo packageRentAmountBo = getPackageRentAmount();
        PackageInfoBo packageInfoBo = packageRentAmountBo.getPackageInfo();

        CalPackageRentAmount.Builder builder = CalPackageRentAmount.newBuilder()
                .setPackageId(packageRentAmountBo.getPackageId() == null ? 1L : packageRentAmountBo.getPackageId())
                .setPackageAmount(packageRentAmountBo.getPackageAmount())
                .setPackageDayNum(packageRentAmountBo.getPackageDayNum() == null ? 0 : packageRentAmountBo.getPackageDayNum())
                .setOutStandardAmount(packageRentAmountBo.getOutStandardAmount())
                .setOutStandardDayNum(packageRentAmountBo.getOutStandardDayNum() == null ? 0 : packageRentAmountBo.getOutStandardDayNum())
                .setAverageAmount(packageRentAmountBo.getAverageAmount())
                .setPackageReduceAmount(packageRentAmountBo.getPackageReduceAmount())
                .setRentDay(packageRentAmountBo.getRentDay() == null ? 0 : packageRentAmountBo.getRentDay())
                .setUnitPrice(packageRentAmountBo.getUnitPrice())
                .setActivityType(packageRentAmountBo.getActivityType() == null ? 1 : packageRentAmountBo.getActivityType())
                .setBestMatch(packageRentAmountBo.getBestMatch() == null ? 0 : packageRentAmountBo.getBestMatch())
                .setTotalAmount(packageRentAmountBo.getTotalAmount())
                .setIsUseEarlyBird(packageRentAmountBo.getIsUseEarlyBird() == null ? 0 :packageRentAmountBo.getIsUseEarlyBird());
        if (packageInfoBo != null) {
            builder.setPackageInfo(PackageInfo.newBuilder()
                    .setPackageId(packageInfoBo.getPackageId() == null ? 1L : packageInfoBo.getPackageId())
                    .setPackageType(packageInfoBo.getPackageType() == null ? 1 : packageInfoBo.getPackageType())
                    .setPackageName(packageInfoBo.getPackageName())
                    .setDaysNumber(packageInfoBo.getDaysNumber() == null ? 0 : packageInfoBo.getPackageType())
                    .setTotalPrice(packageInfoBo.getTotalPrice())
                    .setPackageDesc(packageInfoBo.getPackageDesc())
                    .build()
            );
        }

        BigDecimal accompanyingCardExemptionAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(getAccompanyingCardInfoExemptionAmount())) {
            accompanyingCardExemptionAmount = getAccompanyingCardInfoExemptionAmount().stream().map(ExemptionAmountBo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        ActivityDeductionInfo.Builder activityDeduction = null;
        ActivityDeductionInfoBo activityDeductionInfoBo = getActivityDeduction();
        if (activityDeductionInfoBo != null) {
            activityDeduction = ActivityDeductionInfo.newBuilder()
                    .setDiscountTotalAmount(new BigDecimal(activityDeductionInfoBo.getDiscountTotalAmount()).add(accompanyingCardExemptionAmount).toPlainString())
                    .setReduceAmount(activityDeductionInfoBo.getReduceAmount())
                    .setUserCardNo(activityDeductionInfoBo.getUserCardNo() == null ? -1 : activityDeductionInfoBo.getUserCardNo())
                    .setUserCardName(activityDeductionInfoBo.getUserCardName())
                    .setActivityId(activityDeductionInfoBo.getActivityId() == null ? -1 : activityDeductionInfoBo.getActivityId())
                    .setUserCardDeductibleAmount(activityDeductionInfoBo.getUserCardDeductibleAmount())
                    .setRecommendCarActivityDeductibleAmount(activityDeductionInfoBo.getRecommendCarActivityDeductibleAmount())
                    .setUserCardNum(activityDeductionInfoBo.getUserCardNum() == null ? 0 : activityDeductionInfoBo.getUserCardNum())
                    .setUserCardTotalNum(activityDeductionInfoBo.getUserCardTotalNum() == null ? 0 : activityDeductionInfoBo.getUserCardTotalNum())
                    .setDiscountRate(activityDeductionInfoBo.getDiscountRate() == null ? 0 : activityDeductionInfoBo.getDiscountRate())
                    .setCouponDeductibleAmount(activityDeductionInfoBo.getCouponDeductibleAmount())
                    .setCouponSeq(activityDeductionInfoBo.getCouponSeq() == null ? 0 : activityDeductionInfoBo.getCouponSeq())
                    .setCouponNum(activityDeductionInfoBo.getCouponNum() == null ? 0 : activityDeductionInfoBo.getCouponNum())
                    .setCouponTotalNum(activityDeductionInfoBo.getCouponTotalNum() == null ? 0 : activityDeductionInfoBo.getCouponTotalNum())
                    .setCardUseStatus(activityDeductionInfoBo.getCardUseStatus() == null ? 0 : activityDeductionInfoBo.getCardUseStatus());
            ReductionActivityConfigBo reductionActivityConfigBo = activityDeductionInfoBo.getReductionActivityConfig();
            if (reductionActivityConfigBo != null) {
                activityDeduction.setReductionActivityConfig(ReductionActivityConfig.newBuilder()
                        .setTitle(reductionActivityConfigBo.getTitle())
                        .setContent(reductionActivityConfigBo.getContent())
                        .build());
            }
        }

        List<CalFeeDetail> feeDetail = new ArrayList<>();
        List<CalFeeDetailBo> feeDetailBo = getFeeDetail();
        if(!CollectionUtils.isEmpty(feeDetailBo)) {
            for (CalFeeDetailBo calFeeDetailBo : feeDetailBo) {
                List<FeeDetailsBo> detailsBo = calFeeDetailBo.getDetails();

                List<FeeDetails> feeDetails = new ArrayList<>();
                if (!CollectionUtils.isEmpty(detailsBo)) {
                    feeDetails = new ArrayList<>();
                    for (FeeDetailsBo feeDetailsBo : detailsBo) {
                        FeeDetails feeDetails1 = FeeDetails.newBuilder()
                                .setFeeName(feeDetailsBo.getFeeName())
                                .setFee(feeDetailsBo.getFee())
                                .setFeeExplanation(feeDetailsBo.getFeeExplanation())
                                .setFeeType(feeDetailsBo.getFeeType() == null ? 0 : feeDetailsBo.getFeeType())
                                .build();
                        feeDetails.add(feeDetails1);
                    }
                }

                CalFeeDetail build = CalFeeDetail.newBuilder()
                        .setFeeName(calFeeDetailBo.getFeeName())
                        .setFee(calFeeDetailBo.getFee())
                        .setFeeType(calFeeDetailBo.getFeeType() == null ? 0 : calFeeDetailBo.getFeeType())
                        .addAllDetails(feeDetails)
                        .build();
                feeDetail.add(build);
            }
        }

        // 新版本费用明细
        List<CalFeeDetail> newFeeDetail = new ArrayList<>();
        List<CalFeeDetailBo> newFeeDetailBo = getNewFeeDetail();
        if (!CollectionUtils.isEmpty(newFeeDetailBo)) {
            for (CalFeeDetailBo calFeeDetailBo : newFeeDetailBo) {
                List<FeeDetailsBo> detailsBo = calFeeDetailBo.getDetails();

                List<FeeDetails> feeDetails = new ArrayList<>();
                if (!CollectionUtils.isEmpty(detailsBo)) {
                    for (FeeDetailsBo feeDetailsBo : detailsBo) {
                        FeeDetails feeDetails1 = FeeDetails.newBuilder()
                                .setFeeName(feeDetailsBo.getFeeName())
                                .setFee(feeDetailsBo.getFee())
                                .setFeeExplanation(feeDetailsBo.getFeeExplanation())
                                .setFeeType(feeDetailsBo.getFeeType() == null ? 0 : feeDetailsBo.getFeeType())
                                .setOriginFee(feeDetailsBo.getOriginFee())
                                .build();
                        feeDetails.add(feeDetails1);
                    }
                }

                CalFeeDetail build = CalFeeDetail.newBuilder()
                        .setFeeName(calFeeDetailBo.getFeeName())
                        .setFee(calFeeDetailBo.getFee())
                        .setFeeType(calFeeDetailBo.getFeeType() == null ? 0 : calFeeDetailBo.getFeeType())
                        .addAllDetails(feeDetails)
                        .build();
                newFeeDetail.add(build);
            }
        }

        RenewCalContractAmountUseCardCouponAndServiceRes.Builder res = RenewCalContractAmountUseCardCouponAndServiceRes.newBuilder()
                .setPackageRentAmount(builder.build())
                .setRentAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(rentAmount))
                .setStandardCalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(standardCalAmount));

        if (!StringUtils.isEmpty(getAmount())) {
            res.setAmount(new BigDecimal(getAmount()).compareTo(BigDecimal.ZERO) == 0 ? "0" : BigDecimalUtil.toPlainStringNoDecimalPoint(getAmount()));
        }

        if (activityDeduction != null) {
            res.setActivityDeduction(activityDeduction);
        }
        if (!CollectionUtils.isEmpty(feeDetail)) {
            res.addAllFeeDetail(feeDetail);
        }
        if (!CollectionUtils.isEmpty(newFeeDetail)) {
            res.addAllNewFeeDetail(newFeeDetail);
        }

        // 续租随享卡弹窗金额
        res.setRenewAccompanyingCardTipAmount(getRenewAccompanyingCardTipAmount());
        // 随享卡信息
        if (getAccompanyingCardInfo() != null) {
            res.setAccompanyingCardInfo(getAccompanyingCardInfo().toRes());
        }

        // 随享卡减免明细
        if (!CollectionUtils.isEmpty(getAccompanyingCardInfoExemptionAmount())) {
            ExemptionAmountBo nightServiceItemBo = null;

            List<CalExemptionAmount> calExemptionAmountList = new ArrayList<>();
            for (ExemptionAmountBo itemBo : getAccompanyingCardInfoExemptionAmount()) {
                CalExemptionAmount.Builder builder1 = CalExemptionAmount.newBuilder();

                if (itemBo.getAmountType().equals(AmountType.PICK_UP_NIGHT_AMOUNT) || itemBo.getAmountType().equals(AmountType.RETURN_NIGHT_AMOUNT)) {
                    if (nightServiceItemBo == null) {
                        nightServiceItemBo = itemBo;
                    } else {
                        nightServiceItemBo.setNum(nightServiceItemBo.getNum() + 1);
                        nightServiceItemBo.setAmount(nightServiceItemBo.getAmount().add(itemBo.getAmount()));
                    }
                } else if (itemBo.getAmountType().equals(AmountType.RENT_AMOUNT_STANDARD)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle("车辆租金（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.FREE_TRAVEL_SERVICE_FEE)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.EXCLUSIVE_SERVICE_AMOUNT)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.DAILY_RENT_SERVICE_FEE)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.VEH_MAINTENANCE_FEE)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "次）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.ADD_OIL_AMOUNT)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "次）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.ADD_ELE_AMOUNT)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "次）");
                    calExemptionAmountList.add(builder1.build());
                }
            }

            if (nightServiceItemBo != null) {
                CalExemptionAmount.Builder builder1 = CalExemptionAmount.newBuilder();
                builder1.setAmountType(nightServiceItemBo.getAmountType());
                builder1.setAmount(nightServiceItemBo.getAmount().toPlainString());
                builder1.setNum(nightServiceItemBo.getNum());
                builder1.setUnitPrice(nightServiceItemBo.getUnitPrice().toPlainString());
                builder1.setTitle("夜间服务费（" + nightServiceItemBo.getNum() + "次）");
                calExemptionAmountList.add(builder1.build());
            }
            res.addAllAccompanyingCardInfoExemptionAmount(calExemptionAmountList);
        }

        res.setRealAmount(realAmount);
        res.setSelfOperatedDiscountId(selfOperatedDiscountId);
        return res.build();

    }
}
