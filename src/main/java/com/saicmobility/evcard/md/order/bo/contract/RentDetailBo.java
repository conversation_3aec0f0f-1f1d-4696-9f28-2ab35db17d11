package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.RentDetail;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/9
 */
@Data
public class RentDetailBo {
    private String date; // 日期，格式：MM/dd
    private String dayOfWeek; // 周几，例如：周三
    private String amount; // 租金金额，单位元
    private String fragmentHourDesc; // 零散小时描述，如果是一整天，此字段为空，如果是零散小时，例如：2小时
    private String wholeDate; // 完整日期，格式：yyyyMMdd，不返回，只用做排序

    public RentDetail toRes() {
        RentDetail.Builder builder = RentDetail.newBuilder()
                .setDate(date)
                .setDayOfWeek(dayOfWeek)
                .setAmount(amount)
                .setFragmentHourDesc(fragmentHourDesc)
                ;
        return builder.build();
    }
}
