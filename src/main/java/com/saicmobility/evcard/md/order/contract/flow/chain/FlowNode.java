package com.saicmobility.evcard.md.order.contract.flow.chain;

import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.flow.enums.ActionType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.function.Predicate;

/**
 * 流程节点
 * 责任链模式中的节点定义
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@Accessors(chain = true)
public class FlowNode {
    
    /**
     * 当前状态
     */
    private ContractProcessStateEnum currentState;
    
    /**
     * 需要执行的动作
     */
    private ActionType actionType;
    
    /**
     * 动作成功后的目标状态
     */
    private ContractProcessStateEnum successState;
    
    /**
     * 动作失败后的目标状态
     */
    private ContractProcessStateEnum failureState;
    
    /**
     * 下一个节点列表（支持条件分支）
     */
    private List<FlowNode> nextNodes;
    
    /**
     * 执行条件（可选）
     */
    private Predicate<Object> condition;
    
    /**
     * 是否为终止节点
     */
    private boolean terminal;
    
    /**
     * 节点描述
     */
    private String description;
    
    /**
     * 节点优先级
     */
    private int priority;
    
    /**
     * 创建简单节点
     */
    public static FlowNode create(ContractProcessStateEnum currentState, ActionType actionType,
                                  ContractProcessStateEnum successState, ContractProcessStateEnum failureState) {
        return new FlowNode()
                .setCurrentState(currentState)
                .setActionType(actionType)
                .setSuccessState(successState)
                .setFailureState(failureState)
                .setTerminal(false);
    }
    
    /**
     * 创建终止节点
     */
    public static FlowNode createTerminal(ContractProcessStateEnum currentState, String description) {
        return new FlowNode()
                .setCurrentState(currentState)
                .setActionType(ActionType.NONE)
                .setTerminal(true)
                .setDescription(description);
    }
    
    /**
     * 判断是否匹配当前状态
     */
    public boolean matches(ContractProcessStateEnum state) {
        return this.currentState == state;
    }
    
    /**
     * 判断是否满足执行条件
     */
    public boolean satisfiesCondition(Object context) {
        return condition == null || condition.test(context);
    }
}
