package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.SendVehOrderInfo;
import com.saicmobility.evcard.md.order.entity.ContractItemInfo;

import lombok.Data;

@Data
public class SendVehOrderInfoBo {

    private String planPickPointName; // 计划取车点名
    private String planPickPointAddress; // 计划取车点地址
    private String planPickPointLongitude; // 计划取车点经度
    private String planPickPointLatitude; // 计划取车点纬度

    public static SendVehOrderInfoBo parse(ContractItemInfo sendInfo) {
        SendVehOrderInfoBo bo = new SendVehOrderInfoBo();
        bo.setPlanPickPointName(sendInfo.getServerPointName());
        bo.setPlanPickPointAddress(sendInfo.getServerPointAddress());
        bo.setPlanPickPointLongitude(sendInfo.getServerPointLon() + "");
        bo.setPlanPickPointLatitude(sendInfo.getServerPointLat() + "");
        return bo;
    }

    public SendVehOrderInfo toRes() {
        return SendVehOrderInfo.newBuilder()
                .setPlanPickPointName(planPickPointName)
                .setPlanPickPointAddress(planPickPointAddress)
                .setPlanPickPointLongitude(planPickPointLongitude)
                .setPlanPickPointLatitude(planPickPointLatitude)
                .build();
    }
}
