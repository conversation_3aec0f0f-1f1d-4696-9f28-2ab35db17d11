package com.saicmobility.evcard.md.order.bo.contract;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
public class BatchGetDiscountResItemBo {
    private long storeVehicleModelId; // 门店车型id
    private String selfOperatedDiscountAmount; // 自营折扣抵扣金额
    private String selfOperatedDiscountName; // 自营折扣活动名称
    private String couponDeductionAmount; // 券抵扣金额
    private String averageRent; // 日均租金
    private String orgAverageRent;  // 原日均租金
    private String beforeTotalAmount; // 原价(优惠前)
    private String afterTotalAmount; // 现价（优惠后）
}
