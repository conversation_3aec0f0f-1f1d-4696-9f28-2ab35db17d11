package com.saicmobility.evcard.md.order.contract.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 状态转换配置表实体
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_state_transition_config")
public class StateTransitionConfig {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 当前状态
     */
    private String currentState;

    /**
     * 下一状态
     */
    private String nextState;

    /**
     * 条件表达式
     */
    private String conditionExpression;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态（0=正常   1=已删除）
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createOperId;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createOperName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateOperId;

    /**
     * 更新人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateOperName;
}
