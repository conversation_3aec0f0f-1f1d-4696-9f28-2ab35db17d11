package com.saicmobility.evcard.md.order.contract.handler;

import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.core.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 验车处理器
 * 处理验车相关的业务逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class VehicleCheckHandler extends AbstractEventHandler {

    @Override
    public String getHandlerName() {
        return "VehicleCheckHandler";
    }

    @Override
    protected EventDrivenStateMachine.ProcessResult doHandle(ProcessContext context) {
        log.info("开始执行验车处理: contractId={}, triggerType={}", 
                context.getContractId(), context.getTriggerType());
        
        try {
            // 1. 准备验车数据
            VehicleCheckRequest request = prepareVehicleCheckRequest(context);
            
            // 2. 调用验车接口
            VehicleCheckResponse response = callVehicleCheckService(request);
            
            // 3. 处理响应结果
            if (response.isSuccess()) {
                // 验车成功，设置第三方请求ID用于后续回调匹配
                context.setAttribute("thirdPartyRequestId", response.getRequestId());
                context.setAttribute("vehicleCheckResult", response);
                
                log.info("验车接口调用成功: contractId={}, requestId={}", 
                        context.getContractId(), response.getRequestId());
                
                return EventDrivenStateMachine.ProcessResult.success("验车接口调用成功")
                        .setData(response);
            } else {
                log.error("验车接口调用失败: contractId={}, error={}", 
                        context.getContractId(), response.getErrorMessage());
                
                return EventDrivenStateMachine.ProcessResult.failure("VEHICLE_CHECK_FAILED", 
                        "验车接口调用失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("验车处理异常: contractId={}", context.getContractId(), e);
            return EventDrivenStateMachine.ProcessResult.failure("VEHICLE_CHECK_ERROR", 
                    "验车处理异常: " + e.getMessage());
        }
    }

    /**
     * 准备验车请求数据
     */
    private VehicleCheckRequest prepareVehicleCheckRequest(ProcessContext context) {
        VehicleCheckRequest request = new VehicleCheckRequest();
        request.setContractId(context.getContractId());
        request.setProcessInstanceId(context.getProcessInstanceId());
        request.setTriggerType(context.getTriggerType());
        
        // 根据触发类型设置验车类型
        if ("START_RENTAL".equals(context.getTriggerType())) {
            request.setCheckType("START_CHECK");
            request.setDescription("发车验车");
        } else if ("END_RENTAL".equals(context.getTriggerType())) {
            request.setCheckType("END_CHECK");
            request.setDescription("收车验车");
        } else {
            request.setCheckType("STANDARD_CHECK");
            request.setDescription("标准验车");
        }
        
        // 从事件数据中获取车辆信息
        Object eventData = context.getEventData();
        if (eventData instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) eventData;
            request.setVehicleId((String) dataMap.get("vehicleId"));
            request.setPlateNumber((String) dataMap.get("plateNumber"));
            request.setLocation((String) dataMap.get("location"));
        }
        
        return request;
    }

    /**
     * 调用验车服务
     */
    private VehicleCheckResponse callVehicleCheckService(VehicleCheckRequest request) {
        log.info("调用验车服务: contractId={}, checkType={}", request.getContractId(), request.getCheckType());
        
        try {
            // 模拟调用第三方验车服务
            // 实际实现中这里应该调用真实的第三方接口
            
            // 生成请求ID
            String requestId = generateRequestId(request.getContractId());
            
            // 模拟异步调用
            simulateAsyncVehicleCheck(request, requestId);
            
            // 返回成功响应
            VehicleCheckResponse response = new VehicleCheckResponse();
            response.setSuccess(true);
            response.setRequestId(requestId);
            response.setMessage("验车请求已提交，等待异步回调");
            
            return response;
            
        } catch (Exception e) {
            log.error("调用验车服务异常: contractId={}", request.getContractId(), e);
            
            VehicleCheckResponse response = new VehicleCheckResponse();
            response.setSuccess(false);
            response.setErrorMessage("验车服务调用异常: " + e.getMessage());
            
            return response;
        }
    }

    /**
     * 模拟异步验车处理
     */
    private void simulateAsyncVehicleCheck(VehicleCheckRequest request, String requestId) {
        // 在实际实现中，这里会调用第三方接口
        // 第三方会异步处理验车请求，并通过回调通知结果
        
        log.info("模拟异步验车处理: contractId={}, requestId={}", request.getContractId(), requestId);
        
        // 这里可以发送HTTP请求到第三方验车系统
        // 第三方系统会在验车完成后调用我们的回调接口
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId(String contractId) {
        return "VCR_" + contractId + "_" + System.currentTimeMillis();
    }

    /**
     * 验车请求
     */
    public static class VehicleCheckRequest {
        private String contractId;
        private String processInstanceId;
        private String triggerType;
        private String checkType;
        private String vehicleId;
        private String plateNumber;
        private String location;
        private String description;

        // Getters and Setters
        public String getContractId() { return contractId; }
        public void setContractId(String contractId) { this.contractId = contractId; }
        
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public String getTriggerType() { return triggerType; }
        public void setTriggerType(String triggerType) { this.triggerType = triggerType; }
        
        public String getCheckType() { return checkType; }
        public void setCheckType(String checkType) { this.checkType = checkType; }
        
        public String getVehicleId() { return vehicleId; }
        public void setVehicleId(String vehicleId) { this.vehicleId = vehicleId; }
        
        public String getPlateNumber() { return plateNumber; }
        public void setPlateNumber(String plateNumber) { this.plateNumber = plateNumber; }
        
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    /**
     * 验车响应
     */
    public static class VehicleCheckResponse {
        private boolean success;
        private String requestId;
        private String message;
        private String errorMessage;
        private Object data;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }
}
