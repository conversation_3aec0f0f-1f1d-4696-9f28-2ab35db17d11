package com.saicmobility.evcard.md.order.bo.contractitem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class ContractItemInfoBo {

    // 用户id
    private String mid;

    // 合同项编号
    private String contractItemId;

    // 租车合同号
    private String contractId;

    // 商品订单号
    private String orderNo;

    private String rerentOrderId;

    private String sourceRerentOrderId;

    // 明细总金额
    private BigDecimal totalAmount;

    // 合同项类型 1门店订单(预付/续租) 2送车上门 3上门取车 4购卡
    private Integer itemType;

    // 合同项状态 1进行中 2已取消  3已完成
    private Integer itemStatus;

    // 续租开始时间
    private LocalDateTime startTime;

    // 续租结束时间
    private LocalDateTime endTime;

    // 最晚可还车时间
    private LocalDateTime lastEndTime;

    //  送取车服务门店id
    private Long serverStoreId;

    // 送取车点名称
    private String serverPointName;

    // 送取车点地址
    private String serverPointAddress;

    // 送取车点经度
    private Double serverPointLon;

    // 送取车点纬度
    private Double serverPointLat;

    // 送取车点距离
    private BigDecimal serverPointDistance;

    // 送取车服务租车天数
    private Integer serverRentDays;

    // 送取车服务计划时间
    private LocalDateTime serverPlanTime;

    // 服务创建来源 1下单 2订单进行中 3续租
    private Integer serverOrigin;

    //上门送取服务圈id
    private Integer deliveryServiceId;


    private Date createTime;

}
