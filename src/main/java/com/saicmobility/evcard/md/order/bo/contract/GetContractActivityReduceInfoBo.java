package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.GetContractActivityReduceInfoRes;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/6/28 18:57
 */
@Data
public class GetContractActivityReduceInfoBo {

    //  用户id
    private String mid;
    //  合同参与的立减活动id
    private long reduceActivityId;
    //  取车门店id
    private long pickStoreId;
    //  此合同活动立减金额
    private BigDecimal reduceAmount;

    public GetContractActivityReduceInfoRes toRes() {
        return GetContractActivityReduceInfoRes.newBuilder()
                .setMid(mid)
                .setReduceActivityId(reduceActivityId)
                .setPickStoreId(pickStoreId)
                .setReduceAmount(Optional.ofNullable(reduceAmount).map(BigDecimal::toPlainString).orElse("0"))
                .build();
    }
}
