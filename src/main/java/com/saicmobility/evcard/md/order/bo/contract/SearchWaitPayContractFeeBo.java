//package com.saicmobility.evcard.md.order.bo.contract;
//
//import com.saicmobility.evcard.md.order.bo.external.billing.DamageAmountBo;
//import com.saicmobility.evcard.md.order.bo.external.billing.ServiceAmountBo;
//import lombok.Data;
//
//@Data
//public class SearchWaitPayContractFeeBo {
//
//    //  结算订单号
//    private String settlementOrderNo;
//    //-----费用明细-------
//    //  租车费
//    private RentAmountBo rent;
//    //  燃油费
//    private OilAmountBo oil;
//    //  服务费
//    private WaitPayServiceAmountBo service;
//    // 违约金
//    private DamageAmountBo damage;
//    // 折扣减免
//    private DiscountAmountBo discount;
//    //  门店总减免金额
//    private String storeDiscountTotalAmount;
//    //  客服总减免金额
//    private String customerDiscountTotalAmount;
//
//    //-----待支付卡片上订单数据-----
//    // 会员卡使用情况
//    private CardUseConditionBo card;
//    // 优惠券使用情况
//    private CouponUseConditionBo coupon;
//    //  总时长	单位：分钟
//    private Integer costTime;
//    //  总里程
//    private Integer mileage;
//
//    //-----支付相关-----
//    //  需要实付金额
//    private String realPayAmount;
//    //  可用E币
//    private String rentMins;
//    //  E币抵扣金额
//    private String coinDeductibleAmount;
//    //  是否满足企业支付条件 1是 2否(不选择企业支付默认为2)
//    private Integer isEnableEnterprisePay;
//    //  不满足原因
//    private String reason;
//    //  预付款金额
//    private String prePayAmount;
//    //  预付款抵扣金额
//    private String prePayDeductibleAmount;
//    //  是否免密支付 1免密 2非免密
//    private Integer densityFree;
//}
