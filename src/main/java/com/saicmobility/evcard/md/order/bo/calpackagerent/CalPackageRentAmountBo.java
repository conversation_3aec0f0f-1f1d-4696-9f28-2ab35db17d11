package com.saicmobility.evcard.md.order.bo.calpackagerent;

import java.math.BigDecimal;
import java.util.Optional;

import com.alipay.api.domain.McardNotifyMessage;
import com.saicmobility.evcard.md.mdorderservice.api.CalPackageRentAmount;
import com.saicmobility.evcard.md.mdorderservice.api.PackageInfo;
import com.saicmobility.evcard.md.order.bo.external.billing.CalculateRentAmountBo;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;

import lombok.Data;

/**
 * 计算套餐费用
 */
@Data
public class CalPackageRentAmountBo {

    private Long packageId;

    /** 套餐内价格 */
    private String packageAmount;

    /** 套餐内天数 */
    private Integer packageDayNum;

    /** 超出套餐的标准计费价格 */
    private String outStandardAmount;

    /** 超出套餐的标准计费天数 */
    private Integer outStandardDayNum;

    /** 日均价格(套餐价格+超出套餐的标准价格)/总天数 */
    private String averageAmount;

    /** 套餐优惠金额 */
    private String packageReduceAmount;

    /** 总天数 */
    private Integer rentDay;

    /** 标准单价 */
    private String unitPrice;

    /** 活动类型	1 标准 2 套餐 */
    private Integer activityType;

    /** 是否为最优匹配套餐	1是 2否 */
    private Integer bestMatch;

    /** 总租车费用 */
    private String totalAmount = "0";

    /** 是否能升级为早鸟套餐：1-是、2-否 */
    private Integer canUpgradeEarlyBird;

    /** 早鸟套餐的价格 */
    private String earlyBirdPackageAmount;

    /** 日均价格(早鸟套餐价格+超出套餐的标准价格)/总天数 */
    private String earlyBirdAverageAmount;

    /** 早鸟套餐优惠金额 */
    private String earlyBirdPackageReduceAmount;

    /** 使用早鸟套餐后的总费用 */
    private String earlyBirdTotalAmount;

    /** 是否使用了早鸟套餐：1-是、2-否 */
    private Integer isUseEarlyBird;

    private PackageInfoBo packageInfo;

    // 零散小时总费用
    private BigDecimal fragmentHourAmount = BigDecimal.ZERO;
    // 零散小时时长（例：0.2,1.5）
    private BigDecimal fragmentHour = BigDecimal.ZERO;

    /**
     * @param calRent
     * @param standardUnitPrice
     * @return
     */
    public static CalPackageRentAmountBo from (CalculateRentAmountBo calRent, BigDecimal standardUnitPrice){
        CalPackageRentAmountBo bo = new CalPackageRentAmountBo();
        bo.setPackageId(calRent.getPackageId() == null ? BusinessConstants.PACKAGE_STANDARD_ID : calRent.getPackageId());
        bo.setPackageAmount(calRent.getPackageAmount() == null ? "0" : calRent.getPackageAmount().toPlainString());
        bo.setPackageDayNum(calRent.getPackageDayNum() == null ? 0 : calRent.getPackageDayNum());
        bo.setOutStandardAmount(calRent.getOutPackageStandardAmount() == null ? "" : calRent.getOutPackageStandardAmount().toPlainString());
        bo.setOutStandardDayNum(calRent.getOutPackageStandardDayNum() == null ? 0 : calRent.getOutPackageStandardDayNum());
        bo.setAverageAmount(calRent.getAverageAmount() == null ? "0" : calRent.getAverageAmount().toPlainString());
        bo.setRentDay(calRent.getTotalRentDays());
        bo.setUnitPrice(Optional.ofNullable(standardUnitPrice).map(BigDecimal :: toPlainString).orElse(""));
        bo.setActivityType(calRent.getPackageType());
        bo.setTotalAmount(calRent.getTotalRentAmount() == null ? "0" : calRent.getTotalRentAmount().toPlainString());
        bo.setIsUseEarlyBird(calRent.getIsEarlyBird());

        bo.setFragmentHourAmount(calRent.getFragmentHourAmount());
        bo.setFragmentHour(calRent.getFragmentHour());
        return bo;
    }

    public CalPackageRentAmount toRes(){
        PackageInfoBo packageInfoBo = getPackageInfo();
        PackageInfo packageInfo= packageInfoBo.toRes();
        return CalPackageRentAmount.newBuilder()
                .setPackageId(getPackageId() == 0 ? 1 : getPackageId())
                .setPackageAmount(getPackageAmount())
                .setPackageDayNum(getPackageDayNum() == null ? 0 : getPackageDayNum())
                .setOutStandardAmount(getOutStandardAmount())
                .setOutStandardDayNum(getOutStandardDayNum() == null ? 0 : getOutStandardDayNum())
                .setAverageAmount(getAverageAmount())
                .setPackageReduceAmount(getPackageReduceAmount())
                .setRentDay(getRentDay() == null ? 0 : getRentDay())
                .setUnitPrice(getUnitPrice())
                .setActivityType(getActivityType() == null ? BusinessConstants.PACKAGE_TYPE_STANDARD : getActivityType())
                .setBestMatch(getBestMatch() == null ? BusinessConstants.DIS_ENABLE : getBestMatch())
                .setTotalAmount(getTotalAmount())
                .setCanUpgradeEarlyBird(getCanUpgradeEarlyBird() == null ? 0 : getCanUpgradeEarlyBird())
                .setEarlyBirdPackageAmount(getEarlyBirdPackageAmount())
                .setEarlyBirdAverageAmount(getEarlyBirdAverageAmount())
                .setEarlyBirdPackageReduceAmount(getEarlyBirdPackageReduceAmount())
                .setEarlyBirdTotalAmount(getEarlyBirdTotalAmount())
                .setIsUseEarlyBird(getIsUseEarlyBird() == null ? 0 : getIsUseEarlyBird())
                .setPackageInfo(packageInfo)
                .build();
    }
}
