package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.SearchSettlementOrderByOrderIdRes;
import com.saicmobility.evcard.md.mdorderservice.api.SettlementOrderInfo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Data
public class SearchSettlementOrderByOrderIdBo {
    private String vehicleNo; // 车牌号
    private String company; // 结算公司
    private String beginDate; // 结算开始日期（取车日期） yyyyMMddHHmmss
    private String endDate; // 结算结算日期（还车日期） yyyyMMddHHmmss
    private String taxCategories; // 税种（默认“普票”）
    private String taxRate; // 税率（默认13%）
    private String settlementType; // 结算单类型（默认“还车结算”）
    private String settlementManager; // 结算经理
    private String channelName; // 渠道名称
    private String channelKey; // 渠道key
    private String vin; // 车架号
    private long storeId; // 门店id
    private String storeName; // 门店名称
    private String storeCode; // 门店编码
    private String qlContractId; // 擎路订单号

    public SearchSettlementOrderByOrderIdRes toRes() {
        return SearchSettlementOrderByOrderIdRes.newBuilder()
                .setVehicleNo(Optional.ofNullable(vehicleNo).orElse(StringUtils.EMPTY))
                .setCompany(Optional.ofNullable(company).orElse(StringUtils.EMPTY))
                .setBeginDate(Optional.ofNullable(beginDate).orElse(StringUtils.EMPTY))
                .setEndDate(Optional.ofNullable(endDate).orElse(StringUtils.EMPTY))
                .setTaxCategories(Optional.ofNullable(taxCategories).orElse(StringUtils.EMPTY))
                .setTaxRate(Optional.ofNullable(taxRate).orElse(StringUtils.EMPTY))
                .setSettlementType(Optional.ofNullable(settlementType).orElse(StringUtils.EMPTY))
                .setSettlementManager(Optional.ofNullable(settlementManager).orElse(StringUtils.EMPTY))
                .setChannelKey(Optional.ofNullable(channelKey).orElse(StringUtils.EMPTY))
                .setChannelName(Optional.ofNullable(channelName).orElse(StringUtils.EMPTY))
                .setVin(Optional.ofNullable(vin).orElse(StringUtils.EMPTY))
                .setStoreId(storeId)
                .setStoreName(Optional.ofNullable(storeName).orElse(StringUtils.EMPTY))
                .setStoreCode(Optional.ofNullable(storeCode).orElse(StringUtils.EMPTY))
                .setQlContractId(Optional.ofNullable(qlContractId).orElse(StringUtils.EMPTY))
                .build();
    }
}
