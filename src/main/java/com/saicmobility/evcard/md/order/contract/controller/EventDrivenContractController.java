package com.saicmobility.evcard.md.order.contract.controller;

import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.service.EventDrivenContractProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 事件驱动合同流程控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@RestController
@RequestMapping("/api/v2/contract/event-driven")
@Api(tags = "事件驱动合同流程接口")
public class EventDrivenContractController {

    @Autowired
    private EventDrivenContractProcessService processService;

    /**
     * 启动合同流程
     */
    @PostMapping("/start")
    @ApiOperation("启动合同流程")
    public ApiResponse<String> startProcess(@RequestBody StartProcessRequest request) {
        log.info("接收到启动流程请求: contractId={}, triggerType={}", 
                request.getContractId(), request.getTriggerType());
        
        try {
            // 参数验证
            if (request.getContractId() == null || request.getContractId().trim().isEmpty()) {
                return ApiResponse.failure("INVALID_PARAM", "合同ID不能为空");
            }
            
            if (request.getTriggerType() == null || request.getTriggerType().trim().isEmpty()) {
                return ApiResponse.failure("INVALID_PARAM", "触发类型不能为空");
            }
            
            // 启动流程
            String processInstanceId = processService.startProcess(
                    request.getContractId(), 
                    request.getTriggerType(), 
                    request.getEventData()
            );
            
            return ApiResponse.success("流程启动成功", processInstanceId);
            
        } catch (Exception e) {
            log.error("启动流程异常: contractId={}", request.getContractId(), e);
            return ApiResponse.failure("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 处理验车回调
     */
    @PostMapping("/callback/vehicle-check")
    @ApiOperation("处理验车回调")
    public ApiResponse<String> handleVehicleCheckCallback(@RequestBody CallbackRequest request) {
        log.info("接收到验车回调: processInstanceId={}, success={}", 
                request.getProcessInstanceId(), request.isSuccess());
        
        try {
            boolean result;
            if (request.isSuccess()) {
                result = processService.handleVehicleCheckSuccess(
                        request.getProcessInstanceId(), request.getData());
            } else {
                result = processService.handleVehicleCheckFailed(
                        request.getProcessInstanceId(), request.getData());
            }
            
            if (result) {
                return ApiResponse.success("验车回调处理成功");
            } else {
                return ApiResponse.failure("CALLBACK_PROCESS_FAILED", "验车回调处理失败");
            }
            
        } catch (Exception e) {
            log.error("处理验车回调异常: processInstanceId={}", request.getProcessInstanceId(), e);
            return ApiResponse.failure("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 处理短信回调
     */
    @PostMapping("/callback/sms")
    @ApiOperation("处理短信回调")
    public ApiResponse<String> handleSmsCallback(@RequestBody CallbackRequest request) {
        log.info("接收到短信回调: processInstanceId={}, success={}", 
                request.getProcessInstanceId(), request.isSuccess());
        
        try {
            boolean result;
            if (request.isSuccess()) {
                result = processService.handleSmsSuccess(
                        request.getProcessInstanceId(), request.getData());
            } else {
                result = processService.handleSmsFailed(
                        request.getProcessInstanceId(), request.getData());
            }
            
            if (result) {
                return ApiResponse.success("短信回调处理成功");
            } else {
                return ApiResponse.failure("CALLBACK_PROCESS_FAILED", "短信回调处理失败");
            }
            
        } catch (Exception e) {
            log.error("处理短信回调异常: processInstanceId={}", request.getProcessInstanceId(), e);
            return ApiResponse.failure("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 处理盖章回调
     */
    @PostMapping("/callback/seal")
    @ApiOperation("处理盖章回调")
    public ApiResponse<String> handleSealCallback(@RequestBody CallbackRequest request) {
        log.info("接收到盖章回调: processInstanceId={}, success={}", 
                request.getProcessInstanceId(), request.isSuccess());
        
        try {
            boolean result;
            if (request.isSuccess()) {
                result = processService.handleSealSuccess(
                        request.getProcessInstanceId(), request.getData());
            } else {
                result = processService.handleSealFailed(
                        request.getProcessInstanceId(), request.getData());
            }
            
            if (result) {
                return ApiResponse.success("盖章回调处理成功");
            } else {
                return ApiResponse.failure("CALLBACK_PROCESS_FAILED", "盖章回调处理失败");
            }
            
        } catch (Exception e) {
            log.error("处理盖章回调异常: processInstanceId={}", request.getProcessInstanceId(), e);
            return ApiResponse.failure("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 重试流程
     */
    @PostMapping("/retry/{processInstanceId}")
    @ApiOperation("重试流程")
    public ApiResponse<String> retryProcess(@PathVariable String processInstanceId) {
        log.info("接收到重试请求: processInstanceId={}", processInstanceId);
        
        try {
            boolean result = processService.retryProcess(processInstanceId);
            
            if (result) {
                return ApiResponse.success("重试成功");
            } else {
                return ApiResponse.failure("RETRY_FAILED", "重试失败");
            }
            
        } catch (Exception e) {
            log.error("重试异常: processInstanceId={}", processInstanceId, e);
            return ApiResponse.failure("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 查询流程状态
     */
    @GetMapping("/status/{processInstanceId}")
    @ApiOperation("查询流程状态")
    public ApiResponse<ContractProcessState> getProcessStatus(@PathVariable String processInstanceId) {
        log.info("查询流程状态: processInstanceId={}", processInstanceId);
        
        try {
            ContractProcessState processState = processService.getProcessState(processInstanceId);
            
            if (processState != null) {
                return ApiResponse.success("查询成功", processState);
            } else {
                return ApiResponse.failure("PROCESS_NOT_FOUND", "流程不存在");
            }
            
        } catch (Exception e) {
            log.error("查询状态异常: processInstanceId={}", processInstanceId, e);
            return ApiResponse.failure("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public ApiResponse<String> healthCheck() {
        return ApiResponse.success("服务正常");
    }

    // ==================== 请求响应对象 ====================

    /**
     * 启动流程请求
     */
    public static class StartProcessRequest {
        private String contractId;
        private String triggerType;
        private Map<String, Object> eventData = new HashMap<>();

        // Getters and Setters
        public String getContractId() { return contractId; }
        public void setContractId(String contractId) { this.contractId = contractId; }
        
        public String getTriggerType() { return triggerType; }
        public void setTriggerType(String triggerType) { this.triggerType = triggerType; }
        
        public Map<String, Object> getEventData() { return eventData; }
        public void setEventData(Map<String, Object> eventData) { this.eventData = eventData; }
    }

    /**
     * 回调请求
     */
    public static class CallbackRequest {
        private String processInstanceId;
        private boolean success;
        private String message;
        private Map<String, Object> data = new HashMap<>();

        // Getters and Setters
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public Map<String, Object> getData() { return data; }
        public void setData(Map<String, Object> data) { this.data = data; }
    }

    /**
     * API响应
     */
    public static class ApiResponse<T> {
        private boolean success;
        private String code;
        private String message;
        private T data;

        public static <T> ApiResponse<T> success(String message) {
            ApiResponse<T> response = new ApiResponse<>();
            response.success = true;
            response.code = "SUCCESS";
            response.message = message;
            return response;
        }

        public static <T> ApiResponse<T> success(String message, T data) {
            ApiResponse<T> response = success(message);
            response.data = data;
            return response;
        }

        public static <T> ApiResponse<T> failure(String code, String message) {
            ApiResponse<T> response = new ApiResponse<>();
            response.success = false;
            response.code = code;
            response.message = message;
            return response;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }
}
