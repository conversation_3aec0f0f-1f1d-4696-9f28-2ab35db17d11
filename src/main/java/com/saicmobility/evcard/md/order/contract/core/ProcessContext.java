package com.saicmobility.evcard.md.order.contract.core;

import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 流程处理上下文
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Data
public class ProcessContext {

    /**
     * 流程状态
     */
    private ContractProcessState processState;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件数据
     */
    private Object eventData;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 当前状态
     */
    private String currentState;

    /**
     * 触发类型
     */
    private String triggerType;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 构造方法
     */
    public ProcessContext() {
        this.createTime = LocalDateTime.now();
    }

    public ProcessContext(ContractProcessState processState, String eventType, Object eventData) {
        this();
        this.processState = processState;
        this.eventType = eventType;
        this.eventData = eventData;
        
        if (processState != null) {
            this.contractId = processState.getContractId();
            this.processInstanceId = processState.getProcessInstanceId();
            this.currentState = processState.getCurrentState();
            this.triggerType = processState.getTriggerType();
        }
    }

    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    /**
     * 获取属性（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        Object value = attributes.get(key);
        return value != null ? (T) value : defaultValue;
    }

    /**
     * 检查是否包含属性
     */
    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }

    /**
     * 移除属性
     */
    public void removeAttribute(String key) {
        attributes.remove(key);
    }

    /**
     * 清空属性
     */
    public void clearAttributes() {
        attributes.clear();
    }

    /**
     * 获取所有属性
     */
    public Map<String, Object> getAllAttributes() {
        return new HashMap<>(attributes);
    }
}
