package com.saicmobility.evcard.md.order.bo.drivingrecord;

import java.util.ArrayList;
import java.util.List;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import org.springframework.util.CollectionUtils;

import com.saicmobility.evcard.md.mdorderservice.api.PreCouponInfo;
import com.saicmobility.evcard.md.order.bo.act.CouponViewBo;

import lombok.Data;

/**
 * 预付选择的优惠券信息
 */
@Data
public class PreCouponInfoBo {

    private String couponSeq; //  优惠券编号
    private String couponName; //  优惠券名称
    private String expireDateFromTo; //  有效期
    private String couponAmount; //  优惠券面额	,如果是打折券 该值表示最大抵扣值
    private String minAmount; //  门槛使用金额
    private Integer couponType; //  优惠券类型	 1:直扣 2：折扣
    private Integer serviceType; //  业务类型	 0不限 1分时 2短租 3长租 11分时-预约送车
    private Integer discountRate; //  折扣率	直扣券该值为100，打折券该值的单位是%
    private Integer hasLimitCondition;  //  是否有限制	0 否 1 是
    private List<String> couponTags;  //  优惠券标签	限制条件
    private String remainTimeTag;  //  剩余时间显示标签	剩余时间大于3天时，返回空字符串
    private Integer couponStatus;  //  优惠券状态	0：未使用 1：已使用 2：已作废 3: 已过期
    private String space1;  //  左上角描述
    private List<String> space2;  //  金额下方描述
    private List<String> space3;  //  优惠券名下方描述
    private String rentMethodGroup;  //  租车模式类别：1时租 2日租 空表示通用，多个以逗号分隔

    public static PreCouponInfoBo from(CouponViewBo couponViewBo) {
        PreCouponInfoBo bo = new PreCouponInfoBo();
        bo.setCouponSeq(couponViewBo.getCouponSeq() == null ? "" : couponViewBo.getCouponSeq() + "");
        bo.setCouponName(couponViewBo.getCouponOrigin());
        bo.setExpireDateFromTo(couponViewBo.getExpiresDate());
        bo.setCouponAmount(couponViewBo.getCouponValue());
        bo.setMinAmount(couponViewBo.getMinAmount());
        bo.setCouponType(couponViewBo.getCouponType());
        bo.setServiceType(couponViewBo.getServiceType());
        bo.setDiscountRate(couponViewBo.getDiscountRate());
        bo.setHasLimitCondition(0);
        bo.setCouponTags(couponViewBo.getTags());
        if (!CollectionUtils.isEmpty(bo.getCouponTags())) {
            bo.setHasLimitCondition(1);
        }
        bo.setRemainTimeTag(couponViewBo.getRemainTimeTag());
        bo.setCouponStatus(couponViewBo.getStatus());
        bo.setSpace1(couponViewBo.getSpace1());
        bo.setSpace2(couponViewBo.getSpace2());
        bo.setSpace3(couponViewBo.getSpace3());
        bo.setRentMethodGroup(couponViewBo.getRentMethodGroup());
        return bo;
    }

    public PreCouponInfo toRes() {
        return PreCouponInfo.newBuilder()
                .setCouponSeq(couponSeq)
                .setCouponName(couponName)
                .setExpireDateFromTo(expireDateFromTo)
                .setCouponAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(couponAmount))
                .setMinAmount(minAmount)
                .setCouponType(couponType == null ? 0 : couponType)
                .setServiceType(serviceType == null ? 0 : serviceType)
                .setDiscountRate(discountRate == null ? 0 : discountRate)
                .setHasLimitCondition(hasLimitCondition == null ? 0 : hasLimitCondition)
                .addAllCouponTags(couponTags == null ? new ArrayList<>() : couponTags)
                .setRemainTimeTag(remainTimeTag)
                .setCouponStatus(couponStatus == null ? 0 : couponStatus)
                .setSpace1(space1)
                .addAllSpace2(space2 == null ? new ArrayList<>() : space2)
                .addAllSpace3(space3 == null ? new ArrayList<>() : space3)
                .setRentMethodGroup(rentMethodGroup)
                .build();
    }
}
