package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.SearchContractByIdRes;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Data
public class SearchContractDetailBo {

    private String mid;

    private String contractId;

    // 合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消
    private int contractStatus;

    // 计划取车机构
    private String pickUpOrgCode;

    // 计划取车门店
    private long pickUpStoreId;

    // 计划取车虚拟门店
    private long pickUpShopSeq;

    // 计划取车门店名
    private String pickUpStoreName;

    // 计划还车机构
    private String returnOrgCode;

    // 计划还车门店
    private long returnStoreId;

    // 计划还车虚拟门店
    private long returnShopSeq;

    // 计划还车门店名
    private String returnStoreName;

    // 计划取车时间 yyyyMMddHHmmss
    private String pickUpDateTime;

    // 实际取车时间 yyyyMMddHHmmss
    private String realPickUpTime;

    // 计划还车时间 yyyyMMddHHmmss
    private String returnDateTime;

    // 实际还车时间 yyyyMMddHHmmss
    private String realReturnTime;

    private long goodsModelId;

    private String channelVehicleId;//渠道车型

    //  商品车型名称
    private String goodsModelName;

    private String vin;

    // 上门取车服务金额
    private String pickUpAmount;

    // 上门取车服务创建时间 yyyyMMddHHmmss
    private String pickUpCreateTime;

    // 可续租开始时间 yyyyMMddHHmmss
    private String canReRentStartTime;

    // 最晚还车时间 yyyyMMddHHmmss
    private String latestReturnTime;

    // 订单创建时间 yyyyMMddHHmmss
    private String contractCreateTime;

    // 计费开始时间 yyyyMMddHHmmss
    private String billingStartTime;

    // 计费截止时间 yyyyMMddHHmmss
    private String billingEndTime;

    // 支付时间 yyyyMMddHHmmss
    private String payTime;

    // 实际用车时长  单位：分钟
    private Integer costTime;

    // 取消时间 yyyyMMddHHmmss
    private String cancelTime;

    // 押金状态 0:未缴纳押金 , 1:押金 ，2:免押 ，3:预授权 ，4:芝麻信用 , 5:支付宝共享汽车, 6:飞猪租车
    private Integer depositState;
    
    // 取消方式 1用户取消  2自动取消 3平台取消
    private Integer cancelWay;
    
    // 最大用车时长
    private Integer maxUseDays;
    
    //订单所属机构
    private String orgCode;
    
    //实际取车门店 
    private long realPickUpStoreId;

    // 下单城市id
    private Long cityId;

    // 下单渠道（分包渠道）
    private String orderChannel;

    //实际还车门店
    private Long realReturnStoreId;

    // 是否选择送车上门服务 1是 2否
    private Integer chooseSendService;

    // 是否选择上门取车服务 1是 2否
    private Integer choosePickUpService;

    // 应收总金额
    private BigDecimal amount;

    // 实付总金额
    private BigDecimal realAmount;

    //应用ID
    private String appKey;

    // 租金配置id(续租才有)
    private long priceId;

    private long vehicleModelId;

    private String depositType;//押金类型 1:芝麻免押 2：到店支付

    private String appVersion; // 订单版本

    private long renHourConfigId; // 零散小时规则配置

    private String externalContractId;  //  第三方订单号
    private String qlContractId;  //  擎路订单号
    private Integer qlContractStatus;  //  擎路订单状态 1已确认、2已排车、3已取车、4已还车、5已取消
    private Integer billingType=2;  //  计费方式 1Evcard、2擎路
    private String secondAppKey;  //
    private String taxMainCompany;  //税务主体
    private long platformId;  //平台id

    private String longRentContractId;  //长租合同号
    private Integer transferContractType; // 代客下单转移合同类型：1-evcard个人用车、2-企业用车、3-员工用车、4-vip用车
    private Integer transferContractPayMode; // 代客下单转移合同支付方式：1-个人支付、2-企业支付、3-个人后付
    // 订单创建时间 yyyy-MM-dd HH:mm:ss
    private String createTime;

    private Integer pickupAddrType; //取车方式 3-免费接送
    private Integer returnAddrType; //取车方式 3-免费接送
    private String pickupAddr; //取车免费接送地址
    private String returnAddr; //还车免费接送地址

    private int noWorriedOrder=1;//1:一口价订单

    private int illegalDepositAmount;//违章押金,携程一口价使用
    private int rentDepositAmount;//车辆押金，携程一口价使用



    private int pickUpDeliveryServiceId;//上门取车服务圈id
    private int sendDeliveryServiceId;//送车上门服务圈id

    private int businessFree;//企业支付标识 1:个人支付  2:企业支付

    private int defaultingParty;//违约承担方 1=企业承担 2=个人承担

    private int employeeNewFlag;//员工用车单据标识：1=新订单 2=老订单

    private int isHideOrderFlag;//是否隐藏订单： 0：默认值，否；1：是

    private int separateAccountsFlag; // 是否分账：1=分账 2=非分账

    private int tenmaFlag; // 是否天满：1=天满 2=非天满

    private Integer businessReviewStatus; //企业用车审核状态：1=无须审核 2=待生成审核（支付完成后生成） 3=审核中 4=通过 5=不通过 6=取消审核（取消订单）

    //是否自助取还订单 0：默认值，否；1：是,3:自助取还转非自助取还
    private int selfProrder;
    private int anxinOrder;


    //@ApiModelProperty(value = "下单人和用车人是否一致。1：一致，2：不一致")
    private Integer isSeparate;
    private String userMid; //用车人mid

    public SearchContractByIdRes toRes() {
        return SearchContractByIdRes.newBuilder()
                .setMid(getMid())
                .setContractId(getContractId())
                .setContractStatus(getContractStatus())
                .setPickUpOrgCode(getPickUpOrgCode())
                .setPickUpStoreId(getPickUpStoreId())
                .setPickUpShopSeq(getPickUpShopSeq())
                .setPickUpStoreName(getPickUpStoreName())
                .setReturnOrgCode(getReturnOrgCode())
                .setReturnStoreId(getReturnStoreId())
                .setReturnShopSeq(getReturnShopSeq())
                .setReturnStoreName(getReturnStoreName())
                .setPickUpDateTime(getPickUpDateTime())
                .setReturnDateTime(getReturnDateTime())
                .setRealPickUpTime(getRealPickUpTime())
                .setRealReturnTime(getRealReturnTime())
                .setGoodsModelId(getGoodsModelId())
                .setGoodsModelName(getGoodsModelName())
                .setVin(getVin())
                .setDepositState(getDepositState() == null ? 0 : getDepositState())
                .setCancelWay(getCancelWay() == null ? 0 : getCancelWay())
                .setCanReRentStartTime(getCanReRentStartTime())
                .setLatestReturnTime(getLatestReturnTime())
                .setContractCreateTime(getContractCreateTime())
                .setBillingStartTime(getBillingStartTime())
                .setBillingEndTime(getBillingEndTime())
                .setPayTime(getPayTime())
                .setCancelTime(getCancelTime())
                .setMaxUseDays(getMaxUseDays() == null ? 0 : getMaxUseDays())
                .setCityId(getCityId() == null ? 0 : getCityId())
                .setOrderChannel(getOrderChannel())
                .setRealPickUpStoreId(getRealPickUpStoreId())
                .setRealReturnStoreId(getRealReturnStoreId() == null ? 0 : getRealReturnStoreId())
                .setChooseSendService(getChooseSendService() == null ? 2 : getChooseSendService())
                .setChoosePickUpService(getChoosePickUpService() == null ? 2 : getChoosePickUpService())
                .setPlanPickUpStoreId(getPickUpStoreId())
                .setPlanReturnStoreId(getReturnStoreId())
                .setOrderOrgCode(getOrgCode())
                .setPayTime(getPayTime())
                .setCancelTime(getCancelTime())
                .setCostTime(getCostTime() == null? 0:getCostTime())
                .setAmount(getAmount()== null?"0":getAmount().toString())
                .setRealAmount(getRealAmount()== null?"0":getRealAmount().toString())
                .setPriceId(getPriceId())
                .setAppKey(getAppKey())
                .setDepositType(getDepositType())
                .setVehicleModelId(getVehicleModelId())
                .setAppVersion(getAppVersion())
                .setRenHourConfigId(getRenHourConfigId())
                .setExternalContractId(getExternalContractId())
                .setQlContractId(getQlContractId())
                .setQlContractStatus(getQlContractStatus()!=null ? getQlContractStatus():0 )
                .setBillingType(getBillingType())
                .setSecondAppKey(getSecondAppKey())
                .setTaxMainCompany(getTaxMainCompany())
                .setPlatformId(getPlatformId())
                .setLongRentContractId(getLongRentContractId() == null ? "" : getLongRentContractId())
                .setTransferContractType(getTransferContractType() == null ? 0 : getTransferContractType())
                .setTransferContractPayMode(getTransferContractPayMode() == null ? 0 : getTransferContractPayMode())
                .setCreateTime(getCreateTime() == null ? "" : getCreateTime())
                .setPickupAddr(getPickupAddr())
                .setReturnAddr(getReturnAddr())
                .setPickupAddrType(getPickupAddrType() == null ? 0 : getPickupAddrType())
                .setReturnAddrType(getReturnAddrType() == null ? 0 : getReturnAddrType())
                .setSendDeliveryServiceId(sendDeliveryServiceId)
                .setPickUpDeliveryServiceId(pickUpDeliveryServiceId)
                .setNoWorriedOrder(getNoWorriedOrder())
                .setIllegalDepositAmount(getIllegalDepositAmount())
                .setRentDepositAmount(getRentDepositAmount())
                .setBusinessFree(getBusinessFree())
                .setDefaultingParty(defaultingParty)
                .setEmployeeNewFlag(employeeNewFlag)
                .setSelfProrder(selfProrder)
                .setSeparateAccountsFlag(separateAccountsFlag)
                .setTenmaFlag(tenmaFlag)
                .setBusinessReviewStatus(getBusinessReviewStatus() == null ? 0 : getBusinessReviewStatus())
                .build();
    }

    public SearchContractByIdRes toOfcRes() {
        int noWorriedOrder = 0;
        if (noWorriedOrder == 1 || anxinOrder ==1){
            noWorriedOrder =1 ;
        }
        return SearchContractByIdRes.newBuilder()
                .setMid(getMid())
                .setContractId(getContractId())
                .setContractStatus(getContractStatus())
                .setPickUpOrgCode(getPickUpOrgCode())
                .setPickUpStoreId(getPickUpStoreId())
                .setPickUpShopSeq(getPickUpShopSeq())
                .setPickUpStoreName(getPickUpStoreName())
                .setReturnOrgCode(getReturnOrgCode())
                .setReturnStoreId(getReturnStoreId())
                .setReturnShopSeq(getReturnShopSeq())
                .setReturnStoreName(getReturnStoreName())
                .setPickUpDateTime(getPickUpDateTime())
                .setReturnDateTime(getReturnDateTime())
                .setRealPickUpTime(getRealPickUpTime())
                .setRealReturnTime(getRealReturnTime())
                .setGoodsModelId(getGoodsModelId())
                .setGoodsModelName(getGoodsModelName())
                .setVin(getVin())
                .setDepositState(getDepositState() == null ? 0 : getDepositState())
                .setCancelWay(getCancelWay() == null ? 0 : getCancelWay())
                .setCanReRentStartTime(getCanReRentStartTime())
                .setLatestReturnTime(getLatestReturnTime())
                .setContractCreateTime(getContractCreateTime())
                .setBillingStartTime(getBillingStartTime())
                .setBillingEndTime(getBillingEndTime())
                .setPayTime(getPayTime())
                .setCancelTime(getCancelTime())
                .setMaxUseDays(getMaxUseDays() == null ? 0 : getMaxUseDays())
                .setCityId(getCityId() == null ? 0 : getCityId())
                .setOrderChannel(getOrderChannel())
                .setRealPickUpStoreId(getRealPickUpStoreId())
                .setRealReturnStoreId(getRealReturnStoreId() == null ? 0 : getRealReturnStoreId())
                .setChooseSendService(getChooseSendService() == null ? 2 : getChooseSendService())
                .setChoosePickUpService(getChoosePickUpService() == null ? 2 : getChoosePickUpService())
                .setPlanPickUpStoreId(getPickUpStoreId())
                .setPlanReturnStoreId(getReturnStoreId())
                .setOrderOrgCode(getOrgCode())
                .setPayTime(getPayTime())
                .setCancelTime(getCancelTime())
                .setCostTime(getCostTime() == null? 0:getCostTime())
                .setAmount(getAmount()== null?"0":getAmount().toString())
                .setRealAmount(getRealAmount()== null?"0":getRealAmount().toString())
                .setPriceId(getPriceId())
                .setAppKey(getAppKey())
                .setDepositType(getDepositType())
                .setVehicleModelId(getVehicleModelId())
                .setAppVersion(getAppVersion())
                .setRenHourConfigId(getRenHourConfigId())
                .setExternalContractId(getExternalContractId())
                .setQlContractId(getQlContractId())
                .setQlContractStatus(getQlContractStatus()!=null ? getQlContractStatus():0 )
                .setBillingType(getBillingType())
                .setSecondAppKey(getSecondAppKey())
                .setTaxMainCompany(getTaxMainCompany())
                .setPlatformId(getPlatformId())
                .setLongRentContractId(getLongRentContractId() == null ? "" : getLongRentContractId())
                .setTransferContractType(getTransferContractType() == null ? 0 : getTransferContractType())
                .setTransferContractPayMode(getTransferContractPayMode() == null ? 0 : getTransferContractPayMode())
                .setCreateTime(getCreateTime() == null ? "" : getCreateTime())
                .setPickupAddr(getPickupAddr())
                .setReturnAddr(getReturnAddr())
                .setPickupAddrType(getPickupAddrType() == null ? 0 : getPickupAddrType())
                .setReturnAddrType(getReturnAddrType() == null ? 0 : getReturnAddrType())
                .setSendDeliveryServiceId(sendDeliveryServiceId)
                .setPickUpDeliveryServiceId(pickUpDeliveryServiceId)
                .setNoWorriedOrder(noWorriedOrder)
                .setIllegalDepositAmount(getIllegalDepositAmount())
                .setRentDepositAmount(getRentDepositAmount())
                .setBusinessFree(getBusinessFree())
                .setDefaultingParty(defaultingParty)
                .setEmployeeNewFlag(employeeNewFlag)
                .setSelfProrder(selfProrder)
                .setSeparateAccountsFlag(separateAccountsFlag)
                .setTenmaFlag(tenmaFlag)
                .setIsSeparate(isSeparate == null ? 0 : isSeparate)
                .setUserMid(StringUtils.isEmpty(userMid)? "" : userMid)
                .setBusinessReviewStatus(getBusinessReviewStatus() == null ? 0 : getBusinessReviewStatus())
                .build();
    }
}
