package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.BatchGetSettlementOrderByOrderIdsRes;
import com.saicmobility.evcard.md.mdorderservice.api.SettlementOrderInfo;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class BatchGetSettlementOrderByOrderIdsBo {

    List<SettlementOrderInfoBo> info;

    public BatchGetSettlementOrderByOrderIdsRes toRes() {
        if (CollectionUtils.isEmpty(info)) {
            return BatchGetSettlementOrderByOrderIdsRes.ok();
        }
        List<SettlementOrderInfo> list = info.stream().map(o -> o.toRes()).collect(Collectors.toList());
        return BatchGetSettlementOrderByOrderIdsRes.newBuilder()
                .addAllInfo(list)
                .build();
    }
}
