package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.ContractOperationLog;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class ContractOperationLogBo {

    private Integer type; // 1：预约成功 2：取车成功 3：换车成功 4：续租成功
    private String operationTime; // 操作时间 yyyymmmddhhmmss
    private String curOil; // 当前油量
    private String continueContractTime; // 续约到时间 yyyymmmddhhmmss
    private String vehicleNo; // 车牌号
    private Integer riskTag; // 是否有风险 1：有风险 2：无风险
    private String statusDesc = ""; // 状态描述 (取还车油量差额0升)
    private String desc = ""; // 描述


    public ContractOperationLog toRes() {
        ContractOperationLog.Builder builder = ContractOperationLog.newBuilder()
                .setType(type == null ? 0 : type)
                .setOperationTime(operationTime)
                .setCurOil(curOil)
                .setOperationTime(operationTime)
                .setVehicleNo(vehicleNo)
                .setRiskTag(riskTag == null ? 2 : riskTag)
                .setStatusDesc(statusDesc)
                .setDesc(desc);
        if (StringUtils.isNotEmpty(statusDesc)) {
            if (statusDesc.startsWith("\n")) {
                statusDesc = statusDesc.substring(1);
            }
        }
        builder.setStatusDesc(statusDesc);
        return builder.build();
    }
}
