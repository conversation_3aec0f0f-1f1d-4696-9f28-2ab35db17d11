package com.saicmobility.evcard.md.order.contract.core;

import com.saicmobility.evcard.md.order.contract.entity.EventHandlerConfig;

/**
 * 事件处理器接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
public interface EventHandler {

    /**
     * 处理事件
     * 
     * @param context 处理上下文
     * @return 处理结果
     */
    EventDrivenStateMachine.ProcessResult handle(ProcessContext context);

    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    String getHandlerName();

    /**
     * 设置处理器配置
     * 
     * @param config 配置信息
     */
    void setConfig(EventHandlerConfig config);

    /**
     * 获取处理器配置
     * 
     * @return 配置信息
     */
    EventHandlerConfig getConfig();
}
