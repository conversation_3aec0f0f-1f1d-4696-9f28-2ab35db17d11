package com.saicmobility.evcard.md.order.bo.external.accompanyingcard;

import com.saicmobility.evcard.md.mdbillingservice.api.CalAccompanyingCardInfo;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Data
public class CalAccompanyingCardInfoBo {

    // 购卡id(0:没有推荐购卡)
    private long buyAccompanyingCardActivityId;

    // 用户卡id(0:用户没有卡)
    private long userAccompanyingCardId;

    // 卡名称
    private String name;

    // 随享卡总天数
    private int totalDays;

    // 剩余可用天数
    private int availableDays;

    // 本次使用天数
    private int useDays;

    // 划线价
    private BigDecimal price;

    // 购买价
    private BigDecimal buyPrice;

    // 本单可减金额
    private BigDecimal discountPrice;

    // 本单可减金额 - 购买价
    private BigDecimal reducePrice;

    // 比最优套餐优惠金额
    private BigDecimal reduceToBestMatchAmount;

    // 减免租金
    private BigDecimal reduceRentAmount;

    // 减免服务费
    private BigDecimal reduceServiceAmount;

    // 减免前租金
    private BigDecimal rentAmount;

    // 随享卡基础id cardBaseId
    private long cardId;

    public static CalAccompanyingCardInfoBo parse(CalAccompanyingCardInfo calAccompanyingCardInfo) {
        CalAccompanyingCardInfoBo bo = new CalAccompanyingCardInfoBo();

        bo.setBuyAccompanyingCardActivityId(calAccompanyingCardInfo.getBuyAccompanyingCardActivityId());
        bo.setUserAccompanyingCardId(calAccompanyingCardInfo.getUserAccompanyingCardId());
        bo.setName(calAccompanyingCardInfo.getName());
        bo.setTotalDays(calAccompanyingCardInfo.getTotalDays());
        bo.setAvailableDays(calAccompanyingCardInfo.getAvailableDays());
        bo.setUseDays(calAccompanyingCardInfo.getUseDays());
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getPrice())) {
            bo.setPrice(new BigDecimal(calAccompanyingCardInfo.getPrice()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getBuyPrice())) {
            bo.setBuyPrice(new BigDecimal(calAccompanyingCardInfo.getBuyPrice()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getDiscountPrice())) {
            bo.setDiscountPrice(new BigDecimal(calAccompanyingCardInfo.getDiscountPrice()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getReducePrice())) {
            bo.setReducePrice(new BigDecimal(calAccompanyingCardInfo.getReducePrice()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getReduceToBestMatchAmount())) {
            bo.setReduceToBestMatchAmount(new BigDecimal(calAccompanyingCardInfo.getReduceToBestMatchAmount()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getReduceRentAmount())) {
            bo.setReduceRentAmount(new BigDecimal(calAccompanyingCardInfo.getReduceRentAmount()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getReduceServiceAmount())) {
            bo.setReduceServiceAmount(new BigDecimal(calAccompanyingCardInfo.getReduceServiceAmount()));
        }
        if (StringUtils.isNotEmpty(calAccompanyingCardInfo.getRentAmount())) {
            bo.setRentAmount(new BigDecimal(calAccompanyingCardInfo.getRentAmount()));
        }
        bo.setCardId(calAccompanyingCardInfo.getCardId());
        return bo;
    }

    public com.saicmobility.evcard.md.mdorderservice.api.CalAccompanyingCardInfo toRes() {
        com.saicmobility.evcard.md.mdorderservice.api.CalAccompanyingCardInfo.Builder builder = com.saicmobility.evcard.md.mdorderservice.api.CalAccompanyingCardInfo.newBuilder();
        builder.setBuyAccompanyingCardActivityId(buyAccompanyingCardActivityId);
        builder.setUserAccompanyingCardId(userAccompanyingCardId);
        builder.setName(name);
        builder.setTotalDays(totalDays);
        builder.setAvailableDays(availableDays);
        builder.setUseDays(useDays);
        builder.setPrice(BigDecimalUtil.toPlainStringNoDecimalPoint(price.toPlainString()));
        builder.setBuyPrice(BigDecimalUtil.toPlainStringNoDecimalPoint(buyPrice.toPlainString()));
        builder.setDiscountPrice(BigDecimalUtil.toPlainStringNoDecimalPoint(discountPrice.toPlainString()));
        builder.setReducePrice(BigDecimalUtil.toPlainStringNoDecimalPoint(reducePrice.toPlainString()));
        builder.setCardId(cardId);
        if (reduceToBestMatchAmount != null) {
            builder.setReduceToBestMatchAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(reduceToBestMatchAmount.toPlainString()));
        }
        if (reduceRentAmount != null) {
            builder.setReduceRentAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(reduceRentAmount.toPlainString()));
        }
        if (reduceServiceAmount != null) {
            builder.setReduceServiceAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(reduceServiceAmount.toPlainString()));
        }
        if (rentAmount != null) {
            builder.setRentAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(rentAmount.toPlainString()));
        }
        return builder.build();
    }
}
