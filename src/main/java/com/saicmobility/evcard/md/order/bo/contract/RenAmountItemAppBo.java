package com.saicmobility.evcard.md.order.bo.contract;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RenAmountItemAppBo {
    // 费用名称 (标准计费)
    private String feeName;
    //  费用说明 (1天)
    private String feeDesc;
    // 费用金额 (672.00)
    private BigDecimal amount;
    // 套餐id 零散小时是-1，标准是1，>1为套餐
    private long packageId;
    // 套餐配置信息
    private PackageInfoAppBo packageInfo;
    // 是否使用了早鸟套餐：1-是、2-否
    private int isUseEarlyBird;
    // 费用类型，枚举值参照本文档最上面amountName
    private int feeType;
    // 租金明细，当feeType=1租车费才有此字段
    private List<RentDetailBo> rentDetail;

    private int order; // 用来排序用，订单费用明细排序规则租车费>随享卡>自营活动>优惠券>企业折扣

}
