package com.saicmobility.evcard.md.order.contract.enums;

/**
 * 合同处理流程事件枚举
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
public enum ContractProcessEventEnum {
    
    /**
     * 开始处理 - 触发流程开始
     */
    START_PROCESS("START_PROCESS", "开始处理"),
    
    /**
     * 开始验车 - 触发验车流程
     */
    START_VEHICLE_CHECK("START_VEHICLE_CHECK", "开始验车"),
    
    /**
     * 验车成功 - 验车回调成功
     */
    VEHICLE_CHECK_SUCCESS("VEHICLE_CHECK_SUCCESS", "验车成功"),
    
    /**
     * 验车失败 - 验车回调失败
     */
    VEHICLE_CHECK_FAILED("VEHICLE_CHECK_FAILED", "验车失败"),
    
    /**
     * 开始发送短信 - 触发短信发送流程
     */
    START_SMS_SEND("START_SMS_SEND", "开始发送短信"),
    
    /**
     * 短信发送成功 - 短信发送回调成功
     */
    SMS_SEND_SUCCESS("SMS_SEND_SUCCESS", "短信发送成功"),
    
    /**
     * 短信发送失败 - 短信发送回调失败
     */
    SMS_SEND_FAILED("SMS_SEND_FAILED", "短信发送失败"),
    
    /**
     * 开始盖章 - 触发盖章流程
     */
    START_SEAL("START_SEAL", "开始盖章"),
    
    /**
     * 盖章成功 - 盖章回调成功
     */
    SEAL_SUCCESS("SEAL_SUCCESS", "盖章成功"),
    
    /**
     * 盖章失败 - 盖章回调失败
     */
    SEAL_FAILED("SEAL_FAILED", "盖章失败"),
    
    /**
     * 流程完成 - 整个流程完成
     */
    PROCESS_COMPLETE("PROCESS_COMPLETE", "流程完成"),
    
    /**
     * 重试 - 重试失败的步骤
     */
    RETRY("RETRY", "重试"),
    
    /**
     * 人工干预 - 转为人工处理
     */
    MANUAL_INTERVENTION("MANUAL_INTERVENTION", "人工干预"),
    
    /**
     * 恢复处理 - 从失败状态恢复
     */
    RECOVER("RECOVER", "恢复处理"),
    
    /**
     * 超时 - 操作超时
     */
    TIMEOUT("TIMEOUT", "超时"),
    
    /**
     * 异常 - 系统异常
     */
    ERROR("ERROR", "异常");
    
    private final String code;
    private final String description;
    
    ContractProcessEventEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取事件
     */
    public static ContractProcessEventEnum fromCode(String code) {
        for (ContractProcessEventEnum event : values()) {
            if (event.getCode().equals(code)) {
                return event;
            }
        }
        throw new IllegalArgumentException("Unknown event code: " + code);
    }
    
    /**
     * 判断是否为成功事件
     */
    public boolean isSuccessEvent() {
        return this == VEHICLE_CHECK_SUCCESS || 
               this == SMS_SEND_SUCCESS || 
               this == SEAL_SUCCESS || 
               this == PROCESS_COMPLETE;
    }
    
    /**
     * 判断是否为失败事件
     */
    public boolean isFailureEvent() {
        return this == VEHICLE_CHECK_FAILED || 
               this == SMS_SEND_FAILED || 
               this == SEAL_FAILED || 
               this == TIMEOUT || 
               this == ERROR;
    }
    
    /**
     * 判断是否为开始事件
     */
    public boolean isStartEvent() {
        return this == START_PROCESS || 
               this == START_VEHICLE_CHECK || 
               this == START_SMS_SEND || 
               this == START_SEAL;
    }
}
