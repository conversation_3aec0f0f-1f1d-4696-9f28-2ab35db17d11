package com.saicmobility.evcard.md.order.bo.drivingrecord;

import java.math.BigDecimal;

import com.saicmobility.evcard.md.mdorderservice.api.OilAmount;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

/**
 * 燃油费
 */
@Data
public class OilAmountBo {

    // 燃油费
    private BigDecimal oilAmount;

    // 油费单价
    private BigDecimal oilUnitPrice;

    // 燃油费差值 (还 - 取)
    private Integer oilDiff;

    //  取车时油量
    private Integer pickUpOil;

    //  还车时油量
    private Integer returnOil;

    //  燃油费类型 1应收取的燃油费	2应转为E币退还的燃油费
    private Integer oilAmountType;

    public OilAmount toRes() {
        return OilAmount.newBuilder()
                .setOilAmount(oilAmount == null ? null : BigDecimalUtil.toPlainStringNoDecimalPoint(oilAmount.toPlainString()))
                .setOilUnitPrice(oilUnitPrice == null ? null : oilUnitPrice.stripTrailingZeros().toPlainString())
                .setOilDiff(oilDiff == null ? 0 : oilDiff)
                .setPickUpOil(pickUpOil == null ? "0" : pickUpOil.toString())
                .setReturnOil(returnOil == null ? "0" : returnOil.toString())
                .setOilAmountType(oilAmountType == null ? 1 : oilAmountType)
                .build();
    }
}
