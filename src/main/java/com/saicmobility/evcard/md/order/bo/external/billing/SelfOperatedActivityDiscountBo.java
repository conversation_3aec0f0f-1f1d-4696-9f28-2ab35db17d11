package com.saicmobility.evcard.md.order.bo.external.billing;

import com.saicmobility.evcard.md.mdbillingservice.api.SelfOperatedActivityDiscount;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Data
public class SelfOperatedActivityDiscountBo {
    private long storeVehicleModelId; // 门店车型ID
    private long actId; // 自营活动id
    private ActivityRulerBo ruler; // 自营活动 命中規則
    private String actTotalDiscount; // 门店车型对应的 可用自营活动 优惠金额，整笔订单优惠金额
    private String actRealDiscount; // 门店车型对应的 可用自营活动 优惠金额，考虑之前合同项，本合同项优惠金额； 小于等于actTotalDiscount；
    private int activityType; // 活动类型：1-满减、2-打折
    private int activityDay; //


    public static SelfOperatedActivityDiscountBo from(SelfOperatedActivityDiscount activityInfo) {
        SelfOperatedActivityDiscountBo bo = new SelfOperatedActivityDiscountBo();
        bo.setStoreVehicleModelId(activityInfo.getStoreVehicleModelId());
        bo.setActId(activityInfo.getActId());
        if (activityInfo.getRuler() != null) {
            bo.setRuler(ActivityRulerBo.from(activityInfo.getRuler()));
        }
        bo.setActTotalDiscount(activityInfo.getActTotalDiscount());
        bo.setActRealDiscount(activityInfo.getActRealDiscount());
        bo.setActivityType(activityInfo.getActivityType());
        bo.setActivityDay(activityInfo.getActivityDay());
        return bo;
    }

    public SelfOperatedActivityDiscount toReq() {
        return SelfOperatedActivityDiscount.newBuilder()
                .setStoreVehicleModelId(storeVehicleModelId)
                .setActId(actId)
                .setRuler(ruler == null ? null : ruler.toReq())
                .setActTotalDiscount(actTotalDiscount)
                .setActRealDiscount(actRealDiscount)
                .setActivityType(activityType)
                .build();
    }
}
