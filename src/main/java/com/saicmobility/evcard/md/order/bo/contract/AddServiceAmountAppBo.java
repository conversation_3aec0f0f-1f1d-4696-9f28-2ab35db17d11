package com.saicmobility.evcard.md.order.bo.contract;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.mdorderservice.api.AddServiceAmount;
import com.saicmobility.evcard.md.mdorderservice.api.AddServiceAmountItem;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

@Data
public class AddServiceAmountAppBo {


     // 总增值服务费
    private BigDecimal addServiceAmount;

    // 总增值服务费
    private BigDecimal serviceAmount;

     // 增值服务费明细
    private List<AddServiceAmountItemAppBo> item;

    public AddServiceAmount toRes() {
        return AddServiceAmount.newBuilder()
                .setAddServiceAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(addServiceAmount.toPlainString()))
                .setServiceAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(addServiceAmount.toPlainString()))
                .addAllItem(getAllItem(item))
                .build();
    }

    private List<AddServiceAmountItem> getAllItem(List<AddServiceAmountItemAppBo> itemList) {
        return itemList.stream().map(item -> AddServiceAmountItem.newBuilder()
                .setTitle(item.getTitle())
                .setAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(item.getAmount().toPlainString()))
                .setFeeName(item.getFeeName())
                .setUnitPrice(item.getUnitPrice().stripTrailingZeros().toPlainString())
                .setNum(item.getNum())
                .setAmountType(item.getAmountType())
                .build()).collect(Collectors.toList());
    }
}
