package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.WaitPayInfoRes;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WaitPayInfoBo {

    private String mid;

    private String contractId;

    // 需要支付的金额
    private BigDecimal amount = BigDecimal.ZERO;

    // 商品订单号
    private String goodsOrderNo = "";

    // 支付订单号
    private String payOrderNo = "";

    // 费用类型 1、预付款支付 2、修改订单支付 3、车型变更支付 4、加购服务费支付 5、押金担保支付 6、订单续租 7、还车支付 （默认值0代表没支付）
    private int type;

    //企业支付标识 1:个人支付  2:企业支付
    private int businessFree;

    private String prePayDeductibleAmount = "";  //  预付款抵扣金额
    private String oilRefundAmount = ""; // 燃油费退款
    private String eleRefundAmount = ""; // 电费退款
    private String oilDeductionAmount = ""; // 油费抵扣
    private String eleDeductionAmount = ""; // 电费抵扣

    private String taxMainCompany; // 税务主体

    public WaitPayInfoRes toRes() {
        return WaitPayInfoRes.newBuilder()
                .setAmount(amount.toPlainString())
                .setType(type)
                .setGoodsOrderNo(goodsOrderNo)
                .setPayOrderNo(payOrderNo)
                .setMid(mid)
                .setContractId(contractId)
                .setBusinessFree(businessFree)
                .setPrePayDeductibleAmount(prePayDeductibleAmount)
                .setOilRefundAmount(oilRefundAmount)
                .setEleRefundAmount(eleRefundAmount)
                .setOilDeductionAmount(oilDeductionAmount)
                .setEleDeductionAmount(eleDeductionAmount)
                .setTaxMainCompany(taxMainCompany)
                .build();
    }

    public static WaitPayInfoBo build(BigDecimal amount, int type) {
        WaitPayInfoBo waitPayInfoBo = new WaitPayInfoBo();
        waitPayInfoBo.setAmount(amount);
        waitPayInfoBo.setType(type);
        return waitPayInfoBo;
    }
}
