package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.ContractInfoByMidRes;
import lombok.Data;

@Data
public class ContractInfoByMidBo {

    // 合同id
    private String contractId;

    private String mid;

    // 预付金额
    private String praPayAmount;

    // 预付款支付状态 1：已支付 2：未支付
    private String prePayStatus = "";

    // 剩余支付时间
    private String remainPayTime;

    // 车型名称
    private String goodsModelId;

    // 车型名称
    private String goodsModelName;

    // 车型图片
    private String goodsModelPic;

    // 计划取车时间 yyyy-MM-dd HH:mm:ss
    private String planPickupTime;

    // 计划还车时间 yyyy-MM-dd HH:mm:ss
    private String planReturnTime;

    // 用车天数
    private int useDays;

    // 计划取车门店
    private String planPickupStoreId;

    // 计划取车门店
    private String planPickupStoreName;

    // 计划还车门店
    private String planReturnStoreId;

    // 计划还车门店
    private String planReturnStoreName;

    // 营业时间开始（hhmmss)
    private String businessStartTime;

    // 营业时间结束（hhmmss)
    private String businessEndTime;

    // 支付订单号
    private String payOrderNo;

    // 商品订单号
    private String goodsOrderNo = "";

    //企业支付标识 1:个人支付  2:企业支付
    private int businessFree;

    private int code;

    private String msg;

    // 订单创建时间 yyyy-MM-dd HH:mm:ss
    private String createTime;

    private long vehicleModelId;

    private long planPickUpShopSeq;// 计划取车虚拟门店id
    private long planReturnShopSeq;// 计划还车虚拟门店id

    private int pickupAddrType; //取车方式 3-免费接送
    private int returnAddrType; //取车方式 3-免费接送

    private String pickupAddr; //取车免费接送地址
    private String returnAddr; //还车免费接送地址

    //@ApiModelProperty(value = "下单人和用车人是否一致。1：一致，2：不一致")
    private Integer isSeparate;
    private String userMid; //用车人mid



    public ContractInfoByMidRes toRes() {
        return ContractInfoByMidRes.newBuilder()
                .setContractId(contractId)
                .setPraPayAmount(praPayAmount)
                .setPrePayStatus(prePayStatus)
                .setRemainPayTime(remainPayTime)
                .setGoodsModelId(goodsModelId)
                .setGoodsModelName(goodsModelName)
                .setGoodsModelPic(goodsModelPic)
                .setPlanPickupTime(planPickupTime)
                .setPlanReturnTime(planReturnTime)
                .setUseDays(useDays)
                .setPlanPickupStoreId(planPickupStoreId)
                .setPlanPickupStoreName(planPickupStoreName)
                .setPlanReturnStoreId(planReturnStoreId)
                .setPlanReturnStoreName(planReturnStoreName)
                .setBusinessStartTime(businessStartTime)
                .setBusinessEndTime(businessEndTime)
                .setPayOrderNo(payOrderNo)
                .setGoodsOrderNo(goodsOrderNo)
                .setBusinessFree(businessFree)
                .setRetCode(code)
                .setRetMsg(msg)
                .setCreateTime(createTime)
                .setVehicleModelId(vehicleModelId)
                .setMid(mid)
                .setPlanPickUpShopSeq(planPickUpShopSeq)
                .setPlanReturnShopSeq(planReturnShopSeq)
                .setPickupAddr(pickupAddr)
                .setReturnAddr(returnAddr)
                .setPickupAddrType(pickupAddrType)
                .setReturnAddrType(returnAddrType)
                .setIsSeparate(isSeparate==null?0:isSeparate)
                .setUserMid(userMid)
                .build();
    }

}
