package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.order.entity.CarRentalContractExemptionItem;
import com.saicmobility.evcard.md.order.entity.CarRentalContractFeeItem;
import lombok.Data;

import java.util.List;

@Data
public class ContractFeeDetailBo {

    private List<CarRentalContractFeeItem> feeItems;
    private List<CarRentalContractExemptionItem> exemptionItems;

    public ContractFeeDetailBo(List<CarRentalContractFeeItem> feeItems, List<CarRentalContractExemptionItem> exemptionItems) {
        this.feeItems = feeItems;
        this.exemptionItems = exemptionItems;
    }
}
