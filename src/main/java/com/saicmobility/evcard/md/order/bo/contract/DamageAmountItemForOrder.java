package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.DamageAmountItem;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

@Data
public class DamageAmountItemForOrder {

    private String feeName; // 费用名称

    private String unitPrice; // 费用单价

    private Integer amountType; // 费用类型

	private String desc; // 描述

    public DamageAmountItem toRes() {
        return DamageAmountItem.newBuilder()
                .setFeeName(feeName)
                .setUnitPrice(BigDecimalUtil.toPlainStringNoDecimalPoint(unitPrice))
                .setAmountType(amountType)
				.setDesc(desc)
                .build();
    }
}
