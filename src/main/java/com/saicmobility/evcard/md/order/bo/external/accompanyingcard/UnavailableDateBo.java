package com.saicmobility.evcard.md.order.bo.external.accompanyingcard;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mduserservice.api.UnavailableDate;
import lombok.Data;

import java.time.LocalDate;

/**
 * 随享卡不可用时间
 */
@Data
public class UnavailableDateBo {

    // 开始时间
    private LocalDate startDate;

    // 结束时间
    private LocalDate endDate;

    public static UnavailableDateBo parse(UnavailableDate date) {
        UnavailableDateBo bo = new UnavailableDateBo();
        bo.setStartDate(DateUtil.getLocalDateFromStr(date.getStartDate(), DateUtil.DATE_TYPE8));
        bo.setEndDate(DateUtil.getLocalDateFromStr(date.getEndDate(), DateUtil.DATE_TYPE8));
        return bo;
    }
}
