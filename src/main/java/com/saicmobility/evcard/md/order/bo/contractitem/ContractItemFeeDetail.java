package com.saicmobility.evcard.md.order.bo.contractitem;

import com.saicmobility.evcard.md.order.entity.ContractItemExemptionRecord;
import com.saicmobility.evcard.md.order.entity.ContractItemFeeDetails;
import lombok.Data;

import java.util.List;

@Data
public class ContractItemFeeDetail {
    private List<ContractItemFeeDetails> contractItemFeeDetails;//订单费用明细
    private  List<ContractItemExemptionRecord> exemptionList;//// 查询收车前减免信息


}
