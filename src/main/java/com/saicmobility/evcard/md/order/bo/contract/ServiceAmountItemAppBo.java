package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.ServiceAmountItem;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ServiceAmountItemAppBo {

    // 费用名称
    private String feeName;
    //  费用单价
    private BigDecimal unitPrice;
    //  数量
    private BigDecimal num;
    // 费用金额
    private BigDecimal amount;
    // 费用类型  1租车费-套餐计费 2租车费-标准计费 3燃油费 4畅行服务费 5日租服务费 6车行手续费
    // 7跨门店服务费(异点) 8跨门店服务费(异地) 9送车上门费 10上门取车费 11订单取消违约金  12上门取车取消违约金 13超时还车违约金 14燃油差额(需要退E币)
    private Integer amountType;

    // 描述
    private String desc;

    public ServiceAmountItem toRes() {
        return ServiceAmountItem.newBuilder()
                .setFeeName(feeName)
                .setUnitPrice(unitPrice == null ? "0" : unitPrice.stripTrailingZeros().toPlainString())
                .setNum(num == null ? 0 : num.intValue())
                .setAmount(amount == null ? "0" : BigDecimalUtil.toPlainStringNoDecimalPoint(amount.toPlainString()))
                .setAmountType(amountType == null ? 0 : amountType)
                .setDesc(desc)
                .build();
    }
}
