package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.GetCancelContractConditionRes;
import com.saicmobility.evcard.md.order.bo.external.patroloverdue.InspectorDelayUserRightBo;

import lombok.Data;

/**
 * 取消订单赔偿
 */
@Data
public class GetCancelContractConditionReBo {

    private Integer remainCount; // 剩余取消次数

    private String damageAmount; // 取消违约金 为空无违约金

    private InspectorDelayUserRightBo inspectorDelayUserRight; // 巡检迟到后用户权益信息

    private int isConfigFreeCancel; // 是否配置免费取消容时：1-是、2-否

    private int isUseEarlyBird; // 是否使用了早鸟套餐：1-是、2-否

    public static GetCancelContractConditionReBo buildRes(int remainCount, String damageAmount,
                                                          InspectorDelayUserRightBo inspectorDelayUserRight,
                                                          int isConfigFreeCancel, int isUseEarlyBird) {
        GetCancelContractConditionReBo bo = new GetCancelContractConditionReBo();
        bo.setRemainCount(remainCount);
        bo.setDamageAmount(damageAmount);
        bo.setInspectorDelayUserRight(inspectorDelayUserRight);
        bo.setIsConfigFreeCancel(isConfigFreeCancel);
        bo.setIsUseEarlyBird(isUseEarlyBird);
        return bo;
    }

    public GetCancelContractConditionRes toRes() {
        GetCancelContractConditionRes.Builder builder = GetCancelContractConditionRes.newBuilder()
                .setRemainCount(remainCount == null ? 0 : remainCount)
                .setDamageAmount(damageAmount)
                .setIsConfigFreeCancel(isConfigFreeCancel)
                .setIsUseEarlyBird(isUseEarlyBird);
        if (inspectorDelayUserRight != null) {
            builder.setInspectorDelayUserRight(inspectorDelayUserRight.toRes());
        }
        return builder.build();
    }
}
