package com.saicmobility.evcard.md.order.bo.contract;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ContractConfigBo {

   // 租车合同号
    private String contractId;

   // 花呗分期期数
	private Integer stagingNum = 0;

   // 预付时选择的会员卡id
    private Long preCardNo;

   // 预付时选择的优惠券id
    private Long preCouponSeq;

   // 预付款优惠券抵扣金额
    private BigDecimal couponDeductionAmount;

   // 优惠券使用状态 1未选择优惠券 2选择了但不满足使用条件 3选择了且满足使用条件 (还车后更新)
    private Integer couponUseStatus;

   // 会员卡抵扣金额
    private BigDecimal cardDeductionAmount;

   // 会员卡使用状态 1未选择会员卡 2选择了但不满足使用条件 3选择了且满足使用条件 (还车后更新)
    private Integer cardUseStatus;

    // 会员卡折扣率
    private Integer cardDiscount;

    // 订单间隔时间（分钟）（计划取车门店配置）
    private Integer orderIntervalTime;

    // 随享卡id(还车后更新)
    private Long accompanyingCardId = 0L;

    // 使用随享卡天数(还车后更新)
    private Integer accompanyingCardDays = 0;

    //车生活使用  组合数据json
    private String ext ;

    // 押金担保配置id
    private long depositConfigId;

    // 门店车型价格是否相同1：默认相同 2：不相同
    private Integer mdModelIsDuplicate = 1;

    //长租拆分比例id
    private Long longRentSplitId= 0L;

    //企业用车审核状态：1=无须审核 2=待生成审核（支付完成后生成） 3=审核中 4=通过 5=不通过 6=取消审核（取消订单）
    private Integer businessReviewStatus;

    // "携程标准费用标签 0：默认值，否；1：是"
    private int ctripStandardFeeLab;
   //携程费用标准化零散小时规则
    private String ctripStandardFeeConfig;


    //@ApiModelProperty(value = "充电无忧配置信息")
    private String hassleFreeChargingConfig ;

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public Integer getStagingNum() {
        return stagingNum;
    }

    public void setStagingNum(Integer stagingNum) {
        this.stagingNum = stagingNum;
    }

    public Long getPreCardNo() {
        return preCardNo;
    }

    public void setPreCardNo(Long preCardNo) {
        this.preCardNo = preCardNo;
    }

    public Long getPreCouponSeq() {
        return preCouponSeq;
    }

    public void setPreCouponSeq(Long preCouponSeq) {
        this.preCouponSeq = preCouponSeq;
    }

    public BigDecimal getCouponDeductionAmount() {
        return couponDeductionAmount;
    }

    public void setCouponDeductionAmount(BigDecimal couponDeductionAmount) {
        this.couponDeductionAmount = couponDeductionAmount;
    }

    public Integer getCouponUseStatus() {
        return couponUseStatus;
    }

    public void setCouponUseStatus(Integer couponUseStatus) {
        this.couponUseStatus = couponUseStatus;
    }

    public BigDecimal getCardDeductionAmount() {
        return cardDeductionAmount;
    }

    public void setCardDeductionAmount(BigDecimal cardDeductionAmount) {
        this.cardDeductionAmount = cardDeductionAmount;
    }

    public Integer getCardUseStatus() {
        return cardUseStatus;
    }

    public void setCardUseStatus(Integer cardUseStatus) {
        this.cardUseStatus = cardUseStatus;
    }

    public Integer getCardDiscount() {
        return cardDiscount;
    }

    public void setCardDiscount(Integer cardDiscount) {
        this.cardDiscount = cardDiscount;
    }

    public Integer getOrderIntervalTime() {
        return orderIntervalTime;
    }

    public void setOrderIntervalTime(Integer orderIntervalTime) {
        this.orderIntervalTime = orderIntervalTime;
    }

    public Long getAccompanyingCardId() {
        return accompanyingCardId;
    }

    public void setAccompanyingCardId(Long accompanyingCardId) {
        this.accompanyingCardId = accompanyingCardId;
    }

    public Integer getAccompanyingCardDays() {
        return accompanyingCardDays;
    }

    public void setAccompanyingCardDays(Integer accompanyingCardDays) {
        this.accompanyingCardDays = accompanyingCardDays;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public long getDepositConfigId() {
        return depositConfigId;
    }

    public void setDepositConfigId(long depositConfigId) {
        this.depositConfigId = depositConfigId;
    }

    public Integer getMdModelIsDuplicate() {
        return mdModelIsDuplicate;
    }

    public void setMdModelIsDuplicate(Integer mdModelIsDuplicate) {
        this.mdModelIsDuplicate = mdModelIsDuplicate;
    }

    public Long getLongRentSplitId() {
        return longRentSplitId;
    }

    public void setLongRentSplitId(Long longRentSplitId) {
        this.longRentSplitId = longRentSplitId;
    }

    public Integer getBusinessReviewStatus() {
        return businessReviewStatus;
    }

    public void setBusinessReviewStatus(Integer businessReviewStatus) {
        this.businessReviewStatus = businessReviewStatus;
    }

    public int getCtripStandardFeeLab() {
        return ctripStandardFeeLab;
    }

    public void setCtripStandardFeeLab(int ctripStandardFeeLab) {
        this.ctripStandardFeeLab = ctripStandardFeeLab;
    }

    public String getCtripStandardFeeConfig() {
        return ctripStandardFeeConfig;
    }

    public void setCtripStandardFeeConfig(String ctripStandardFeeConfig) {
        this.ctripStandardFeeConfig = ctripStandardFeeConfig;
    }

    public String getHassleFreeChargingConfig() {
        return hassleFreeChargingConfig;
    }

    public void setHassleFreeChargingConfig(String hassleFreeChargingConfig) {
        this.hassleFreeChargingConfig = hassleFreeChargingConfig;
    }

    public ContractConfigBo() {
    }

    public ContractConfigBo(String contractId, Integer stagingNum, Long preCardNo, Long preCouponSeq, BigDecimal couponDeductionAmount, Integer couponUseStatus, BigDecimal cardDeductionAmount, Integer cardUseStatus, Integer cardDiscount, Integer orderIntervalTime, Long accompanyingCardId, Integer accompanyingCardDays, String ext, long depositConfigId, Integer mdModelIsDuplicate, Long longRentSplitId, Integer businessReviewStatus, int ctripStandardFeeLab, String ctripStandardFeeConfig, String hassleFreeChargingConfig) {
        this.contractId = contractId;
        this.stagingNum = stagingNum;
        this.preCardNo = preCardNo;
        this.preCouponSeq = preCouponSeq;
        this.couponDeductionAmount = couponDeductionAmount;
        this.couponUseStatus = couponUseStatus;
        this.cardDeductionAmount = cardDeductionAmount;
        this.cardUseStatus = cardUseStatus;
        this.cardDiscount = cardDiscount;
        this.orderIntervalTime = orderIntervalTime;
        this.accompanyingCardId = accompanyingCardId;
        this.accompanyingCardDays = accompanyingCardDays;
        this.ext = ext;
        this.depositConfigId = depositConfigId;
        this.mdModelIsDuplicate = mdModelIsDuplicate;
        this.longRentSplitId = longRentSplitId;
        this.businessReviewStatus = businessReviewStatus;
        this.ctripStandardFeeLab = ctripStandardFeeLab;
        this.ctripStandardFeeConfig = ctripStandardFeeConfig;
        this.hassleFreeChargingConfig = hassleFreeChargingConfig;
    }
}
