package com.saicmobility.evcard.md.order.bo.contract;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.mdorderservice.api.RentDetail;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import org.springframework.util.CollectionUtils;

import com.saicmobility.evcard.md.mdorderservice.api.PackageInfo;
import com.saicmobility.evcard.md.mdorderservice.api.RenAmountItem;
import com.saicmobility.evcard.md.mdorderservice.api.RentAmount;

import lombok.Data;

@Data
public class RentAmountAppBo {

    // 总租车费
    private BigDecimal rentAmount;
    // 租车费明细列表
    private List<RenAmountItemAppBo> rentAmountItem;
    // 日均租金
    private BigDecimal averageRent;
    // 原日均租金
    private BigDecimal orgAverageRent;

    public RentAmount toRes() {
        RentAmount.Builder builder = RentAmount.newBuilder();
        builder.setRentAmount(Optional.ofNullable(rentAmount).map(BigDecimalUtil::toPlainStringNoDecimalPoint).orElse(null));
        List<RenAmountItem> allRentAmountItem = getAllRentAmountItem(rentAmountItem);
        if (!CollectionUtils.isEmpty(allRentAmountItem)) {
			builder.addAllRentAmountItem(allRentAmountItem);
        }
        builder.setAverageRent(Optional.ofNullable(averageRent).map(BigDecimalUtil::toPlainStringNoDecimalPoint).orElse(null));
        builder.setOrgAverageRent(Optional.ofNullable(orgAverageRent).map(BigDecimalUtil::toPlainStringNoDecimalPoint).orElse(null));
        return builder.build();
    }

    private List<RenAmountItem> getAllRentAmountItem(List<RenAmountItemAppBo> rentAmountItem) {
        return rentAmountItem.stream().map(rent -> RenAmountItem.newBuilder()
                .setFeeName(rent.getFeeName())
                .setFeeDesc(rent.getFeeDesc())
                .setAmount(Optional.ofNullable(rent.getAmount()).map(BigDecimalUtil::toPlainStringNoDecimalPoint).orElse(null))
                .setPackageId((int)rent.getPackageId())
                .setPackageInfo(Optional.ofNullable(rent.getPackageInfo()).map(PackageInfoAppBo::toRes)
                        .orElse(PackageInfo.newBuilder().build()))
                .setIsUseEarlyBird(rent.getIsUseEarlyBird())
                .setFeeType(rent.getFeeType())
                .addAllRentDetail(getAllRentDetail(rent.getRentDetail()))
                .build()).collect(Collectors.toList());
    }

    private Iterable<? extends RentDetail> getAllRentDetail(List<RentDetailBo> rentDetailBoList) {
        List<RentDetail> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rentDetailBoList)) {
            for (RentDetailBo bo : rentDetailBoList) {
                result.add(RentDetail.newBuilder()
                        .setDate(bo.getDate())
                        .setDayOfWeek(bo.getDayOfWeek())
                        .setAmount(bo.getAmount())
                        .setFragmentHourDesc(bo.getFragmentHourDesc())
                        .build());
            }
        }
        return result;
    }
}
