package com.saicmobility.evcard.md.order.contract.handler;

import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.core.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 短信通知处理器
 * 处理短信发送相关的业务逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class SmsNotificationHandler extends AbstractEventHandler {

    @Override
    public String getHandlerName() {
        return "SmsNotificationHandler";
    }

    @Override
    protected EventDrivenStateMachine.ProcessResult doHandle(ProcessContext context) {
        log.info("开始执行短信通知处理: contractId={}, triggerType={}", 
                context.getContractId(), context.getTriggerType());
        
        try {
            // 1. 准备短信发送数据
            SmsRequest request = prepareSmsRequest(context);
            
            // 2. 调用短信发送接口
            SmsResponse response = callSmsService(request);
            
            // 3. 处理响应结果
            if (response.isSuccess()) {
                // 短信发送成功，设置第三方请求ID用于后续回调匹配
                context.setAttribute("smsRequestId", response.getRequestId());
                context.setAttribute("smsResult", response);
                
                log.info("短信发送接口调用成功: contractId={}, requestId={}", 
                        context.getContractId(), response.getRequestId());
                
                return EventDrivenStateMachine.ProcessResult.success("短信发送接口调用成功")
                        .setData(response);
            } else {
                log.error("短信发送接口调用失败: contractId={}, error={}", 
                        context.getContractId(), response.getErrorMessage());
                
                return EventDrivenStateMachine.ProcessResult.failure("SMS_SEND_FAILED", 
                        "短信发送接口调用失败: " + response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("短信通知处理异常: contractId={}", context.getContractId(), e);
            return EventDrivenStateMachine.ProcessResult.failure("SMS_NOTIFICATION_ERROR", 
                    "短信通知处理异常: " + e.getMessage());
        }
    }

    /**
     * 准备短信发送请求数据
     */
    private SmsRequest prepareSmsRequest(ProcessContext context) {
        SmsRequest request = new SmsRequest();
        request.setContractId(context.getContractId());
        request.setProcessInstanceId(context.getProcessInstanceId());
        request.setTriggerType(context.getTriggerType());
        
        // 根据触发类型设置短信模板
        if ("START_RENTAL".equals(context.getTriggerType())) {
            request.setTemplateId("START_RENTAL_SMS_TEMPLATE");
            request.setContent("您的车辆已准备就绪，合同编号：" + context.getContractId() + "，请及时取车。");
        } else if ("END_RENTAL".equals(context.getTriggerType())) {
            request.setTemplateId("END_RENTAL_SMS_TEMPLATE");
            request.setContent("您的车辆已成功归还，合同编号：" + context.getContractId() + "，感谢使用。");
        } else {
            request.setTemplateId("DEFAULT_SMS_TEMPLATE");
            request.setContent("合同处理通知，合同编号：" + context.getContractId());
        }
        
        // 从事件数据中获取用户信息
        Object eventData = context.getEventData();
        if (eventData instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) eventData;
            request.setPhoneNumber((String) dataMap.get("phoneNumber"));
            request.setUserId((String) dataMap.get("userId"));
            request.setUserName((String) dataMap.get("userName"));
        }
        
        // 设置短信参数
        java.util.Map<String, String> params = new java.util.HashMap<>();
        params.put("contractId", context.getContractId());
        params.put("triggerType", context.getTriggerType());
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        request.setParams(params);
        
        return request;
    }

    /**
     * 调用短信发送服务
     */
    private SmsResponse callSmsService(SmsRequest request) {
        log.info("调用短信发送服务: contractId={}, phoneNumber={}", 
                request.getContractId(), maskPhoneNumber(request.getPhoneNumber()));
        
        try {
            // 模拟调用第三方短信服务
            // 实际实现中这里应该调用真实的第三方接口
            
            // 生成请求ID
            String requestId = generateRequestId(request.getContractId());
            
            // 模拟异步发送
            simulateAsyncSmsSend(request, requestId);
            
            // 返回成功响应
            SmsResponse response = new SmsResponse();
            response.setSuccess(true);
            response.setRequestId(requestId);
            response.setMessage("短信发送请求已提交，等待异步回调");
            
            return response;
            
        } catch (Exception e) {
            log.error("调用短信发送服务异常: contractId={}", request.getContractId(), e);
            
            SmsResponse response = new SmsResponse();
            response.setSuccess(false);
            response.setErrorMessage("短信发送服务调用异常: " + e.getMessage());
            
            return response;
        }
    }

    /**
     * 模拟异步短信发送处理
     */
    private void simulateAsyncSmsSend(SmsRequest request, String requestId) {
        // 在实际实现中，这里会调用第三方接口
        // 第三方会异步处理短信发送请求，并通过回调通知结果
        
        log.info("模拟异步短信发送处理: contractId={}, requestId={}", request.getContractId(), requestId);
        
        // 这里可以发送HTTP请求到第三方短信系统
        // 第三方系统会在短信发送完成后调用我们的回调接口
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId(String contractId) {
        return "SMS_" + contractId + "_" + System.currentTimeMillis();
    }

    /**
     * 手机号脱敏
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }

    /**
     * 短信发送请求
     */
    public static class SmsRequest {
        private String contractId;
        private String processInstanceId;
        private String triggerType;
        private String templateId;
        private String phoneNumber;
        private String userId;
        private String userName;
        private String content;
        private java.util.Map<String, String> params;

        // Getters and Setters
        public String getContractId() { return contractId; }
        public void setContractId(String contractId) { this.contractId = contractId; }
        
        public String getProcessInstanceId() { return processInstanceId; }
        public void setProcessInstanceId(String processInstanceId) { this.processInstanceId = processInstanceId; }
        
        public String getTriggerType() { return triggerType; }
        public void setTriggerType(String triggerType) { this.triggerType = triggerType; }
        
        public String getTemplateId() { return templateId; }
        public void setTemplateId(String templateId) { this.templateId = templateId; }
        
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public java.util.Map<String, String> getParams() { return params; }
        public void setParams(java.util.Map<String, String> params) { this.params = params; }
    }

    /**
     * 短信发送响应
     */
    public static class SmsResponse {
        private boolean success;
        private String requestId;
        private String message;
        private String errorMessage;
        private Object data;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
    }
}
