package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.GetCancelPickUpServiceDamageTimeRes;

import lombok.Data;

/**
 * 从xx开始取消上门取车服务收费
 */
@Data
public class CancelPickUpServiceDamageTimeBo {

    private String damageTime; // 取消上门取车服务开始赔偿时间 yyyymmmddhhmmss

    private String damageAmount; // 赔偿金额

    public GetCancelPickUpServiceDamageTimeRes toRes() {
        return GetCancelPickUpServiceDamageTimeRes.newBuilder()
                .setDamageTime(damageTime)
                .setDamageAmount(damageAmount)
                .build();
    }
}
