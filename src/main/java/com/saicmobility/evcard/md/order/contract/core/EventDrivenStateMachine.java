package com.saicmobility.evcard.md.order.contract.core;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.entity.EventHandlerConfig;
import com.saicmobility.evcard.md.order.contract.entity.StateTransitionConfig;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessStateMapper;
import com.saicmobility.evcard.md.order.contract.mapper.EventHandlerConfigMapper;
import com.saicmobility.evcard.md.order.contract.mapper.StateTransitionConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 事件驱动状态机核心组件
 * 简化设计：事件 → 动作 + 下一状态
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class EventDrivenStateMachine {

    @Autowired
    private ContractProcessStateMapper stateMapper;

    @Autowired
    private EventHandlerConfigMapper handlerConfigMapper;

    @Autowired
    private StateTransitionConfigMapper transitionConfigMapper;

    @Autowired
    private EventHandlerRegistry handlerRegistry;

    @Autowired
    private ProcessContextManager contextManager;

    // 并行执行器
    private final Executor parallelExecutor = Executors.newFixedThreadPool(10);

    /**
     * 处理事件的核心方法
     * 
     * @param eventType 事件类型
     * @param processInstanceId 流程实例ID
     * @param eventData 事件数据
     * @return 处理结果
     */
    public ProcessResult processEvent(String eventType, String processInstanceId, Object eventData) {
        log.info("开始处理事件: eventType={}, processInstanceId={}", eventType, processInstanceId);
        
        try {
            // 1. 获取当前流程状态
            ContractProcessState currentState = getCurrentState(processInstanceId);
            if (currentState == null) {
                return ProcessResult.failure("PROCESS_NOT_FOUND", "流程实例不存在: " + processInstanceId);
            }

            // 2. 获取事件对应的处理器配置
            List<EventHandlerConfig> handlerConfigs = getEventHandlers(eventType);
            if (handlerConfigs.isEmpty()) {
                log.warn("未找到事件处理器配置: eventType={}", eventType);
                return ProcessResult.failure("NO_HANDLER_FOUND", "未找到事件处理器: " + eventType);
            }

            // 3. 创建处理上下文
            ProcessContext context = contextManager.createContext(currentState, eventType, eventData);

            // 4. 执行处理器
            ProcessResult handlerResult = executeHandlers(handlerConfigs, context);
            if (!handlerResult.isSuccess()) {
                return handlerResult;
            }

            // 5. 状态转换
            ProcessResult transitionResult = performStateTransition(eventType, currentState, context);
            if (!transitionResult.isSuccess()) {
                return transitionResult;
            }

            log.info("事件处理完成: eventType={}, processInstanceId={}", eventType, processInstanceId);
            return ProcessResult.success("事件处理成功");

        } catch (Exception e) {
            log.error("事件处理异常: eventType={}, processInstanceId={}", eventType, processInstanceId, e);
            return ProcessResult.failure("PROCESS_ERROR", "事件处理异常: " + e.getMessage());
        }
    }

    /**
     * 获取当前流程状态
     */
    private ContractProcessState getCurrentState(String processInstanceId) {
        return stateMapper.selectOne(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getProcessInstanceId, processInstanceId)
                        .orderByDesc(ContractProcessState::getCreateTime)
                        .last("LIMIT 1")
        );
    }

    /**
     * 获取事件处理器配置
     */
    private List<EventHandlerConfig> getEventHandlers(String eventType) {
        return handlerConfigMapper.selectList(
                new LambdaQueryWrapper<EventHandlerConfig>()
                        .eq(EventHandlerConfig::getEventType, eventType)
                        .eq(EventHandlerConfig::getIsEnabled, true)
                        .orderByAsc(EventHandlerConfig::getExecutionOrder)
        );
    }

    /**
     * 执行处理器
     */
    private ProcessResult executeHandlers(List<EventHandlerConfig> handlerConfigs, ProcessContext context) {
        log.info("开始执行处理器, 数量: {}", handlerConfigs.size());

        // 按执行模式分组执行
        String currentExecutionMode = null;
        List<EventHandlerConfig> currentGroup = new java.util.ArrayList<>();

        for (EventHandlerConfig config : handlerConfigs) {
            if (currentExecutionMode == null || !currentExecutionMode.equals(config.getExecutionMode())) {
                // 执行当前组
                if (!currentGroup.isEmpty()) {
                    ProcessResult groupResult = executeHandlerGroup(currentGroup, context, currentExecutionMode);
                    if (!groupResult.isSuccess()) {
                        return groupResult;
                    }
                    currentGroup.clear();
                }
                currentExecutionMode = config.getExecutionMode();
            }
            currentGroup.add(config);
        }

        // 执行最后一组
        if (!currentGroup.isEmpty()) {
            return executeHandlerGroup(currentGroup, context, currentExecutionMode);
        }

        return ProcessResult.success("所有处理器执行完成");
    }

    /**
     * 执行处理器组
     */
    private ProcessResult executeHandlerGroup(List<EventHandlerConfig> configs, ProcessContext context, String executionMode) {
        if ("PARALLEL".equals(executionMode)) {
            return executeHandlersInParallel(configs, context);
        } else {
            return executeHandlersInSerial(configs, context);
        }
    }

    /**
     * 串行执行处理器
     */
    private ProcessResult executeHandlersInSerial(List<EventHandlerConfig> configs, ProcessContext context) {
        log.info("串行执行处理器, 数量: {}", configs.size());

        for (EventHandlerConfig config : configs) {
            ProcessResult result = executeHandler(config, context);
            if (!result.isSuccess()) {
                log.error("处理器执行失败: handlerName={}, error={}", config.getHandlerName(), result.getErrorMessage());
                return result;
            }
        }

        return ProcessResult.success("串行执行完成");
    }

    /**
     * 并行执行处理器
     */
    private ProcessResult executeHandlersInParallel(List<EventHandlerConfig> configs, ProcessContext context) {
        log.info("并行执行处理器, 数量: {}", configs.size());

        try {
            // 创建并行任务
            CompletableFuture<ProcessResult>[] futures = configs.stream()
                    .map(config -> CompletableFuture.supplyAsync(() -> executeHandler(config, context), parallelExecutor))
                    .toArray(CompletableFuture[]::new);

            // 等待所有任务完成
            CompletableFuture.allOf(futures).join();

            // 检查结果
            for (CompletableFuture<ProcessResult> future : futures) {
                ProcessResult result = future.get();
                if (!result.isSuccess()) {
                    log.error("并行处理器执行失败: error={}", result.getErrorMessage());
                    return result;
                }
            }

            return ProcessResult.success("并行执行完成");

        } catch (Exception e) {
            log.error("并行执行处理器异常", e);
            return ProcessResult.failure("PARALLEL_EXECUTION_ERROR", "并行执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行单个处理器
     */
    private ProcessResult executeHandler(EventHandlerConfig config, ProcessContext context) {
        log.info("执行处理器: handlerName={}, handlerClass={}", config.getHandlerName(), config.getHandlerClass());

        try {
            // 从注册表获取处理器实例
            EventHandler handler = handlerRegistry.getHandler(config.getHandlerName());
            if (handler == null) {
                return ProcessResult.failure("HANDLER_NOT_FOUND", "处理器未找到: " + config.getHandlerName());
            }

            // 设置处理器配置
            handler.setConfig(config);

            // 执行处理器
            long startTime = System.currentTimeMillis();
            ProcessResult result = handler.handle(context);
            long executionTime = System.currentTimeMillis() - startTime;

            log.info("处理器执行完成: handlerName={}, executionTime={}ms, success={}", 
                    config.getHandlerName(), executionTime, result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("处理器执行异常: handlerName={}", config.getHandlerName(), e);
            return ProcessResult.failure("HANDLER_EXECUTION_ERROR", "处理器执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行状态转换
     */
    private ProcessResult performStateTransition(String eventType, ContractProcessState currentState, ProcessContext context) {
        log.info("开始状态转换: eventType={}, currentState={}", eventType, currentState.getCurrentState());

        try {
            // 获取状态转换配置
            StateTransitionConfig transitionConfig = getStateTransition(eventType, currentState.getCurrentState());
            if (transitionConfig == null) {
                log.warn("未找到状态转换配置: eventType={}, currentState={}", eventType, currentState.getCurrentState());
                return ProcessResult.success("无需状态转换");
            }

            // 检查转换条件
            if (!checkTransitionCondition(transitionConfig, context)) {
                log.info("状态转换条件不满足: eventType={}, currentState={}", eventType, currentState.getCurrentState());
                return ProcessResult.success("状态转换条件不满足");
            }

            // 执行状态转换
            String nextState = transitionConfig.getNextState();
            updateProcessState(currentState, nextState, context);

            log.info("状态转换完成: {} -> {}", currentState.getCurrentState(), nextState);
            return ProcessResult.success("状态转换成功");

        } catch (Exception e) {
            log.error("状态转换异常: eventType={}, currentState={}", eventType, currentState.getCurrentState(), e);
            return ProcessResult.failure("STATE_TRANSITION_ERROR", "状态转换异常: " + e.getMessage());
        }
    }

    /**
     * 获取状态转换配置
     */
    private StateTransitionConfig getStateTransition(String eventType, String currentState) {
        return transitionConfigMapper.selectOne(
                new LambdaQueryWrapper<StateTransitionConfig>()
                        .eq(StateTransitionConfig::getEventType, eventType)
                        .eq(StateTransitionConfig::getCurrentState, currentState)
                        .eq(StateTransitionConfig::getIsEnabled, true)
                        .orderByDesc(StateTransitionConfig::getPriority)
                        .last("LIMIT 1")
        );
    }

    /**
     * 检查转换条件
     */
    private boolean checkTransitionCondition(StateTransitionConfig config, ProcessContext context) {
        String conditionExpression = config.getConditionExpression();
        if (conditionExpression == null || conditionExpression.trim().isEmpty()) {
            return true; // 无条件，直接转换
        }

        // TODO: 实现条件表达式解析和执行
        // 这里可以集成SpEL或其他表达式引擎
        log.debug("检查转换条件: {}", conditionExpression);
        return true;
    }

    /**
     * 更新流程状态
     */
    private void updateProcessState(ContractProcessState currentState, String nextState, ProcessContext context) {
        currentState.setPreviousState(currentState.getCurrentState())
                   .setCurrentState(nextState)
                   .setProcessData(JSON.toJSONString(context.getEventData()))
                   .setUpdateTime(LocalDateTime.now());

        stateMapper.updateById(currentState);
    }

    /**
     * 处理结果
     */
    public static class ProcessResult {
        private boolean success;
        private String errorCode;
        private String errorMessage;
        private Object data;

        public static ProcessResult success(String message) {
            ProcessResult result = new ProcessResult();
            result.success = true;
            result.errorMessage = message;
            return result;
        }

        public static ProcessResult failure(String errorCode, String errorMessage) {
            ProcessResult result = new ProcessResult();
            result.success = false;
            result.errorCode = errorCode;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
        public Object getData() { return data; }

        public ProcessResult setData(Object data) {
            this.data = data;
            return this;
        }
    }
}
