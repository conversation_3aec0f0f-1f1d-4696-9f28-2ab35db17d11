package com.saicmobility.evcard.md.order.bo.calpackagerent;

import com.saicmobility.evcard.md.mdorderservice.api.PackageInfo;
import com.saicmobility.evcard.md.mdorderservice.api.RenewCalPackageRentAmount;

import lombok.Data;

/**
 * 计算续约套餐
 */
@Data
public class RenewCalPackageRentAmountBo {

    private Long packageId;

    /** 套餐内价格 */
    private String packageAmount;

    /** 套餐内天数 */
    private Integer packageDayNum;

    /** 超出套餐的标准计费价格 */
    private String outStandardAmount;

    /** 超出套餐的标准计费天数 */
    private Integer outStandardDayNum;

    /** 日均价格(套餐价格+超出套餐的标准价格)/总天数 */
    private String averageAmount;

    /** 套餐优惠金额 */
    private String packageReduceAmount;

    /** 总天数 */
    private Integer rentDay;

    /** 标准单价 */
    private String unitPrice;

    /** 活动类型	1 标准 2 套餐 */
    private Integer activityType;

    /** 是否为最优匹配套餐	1是 2否 */
    private Integer bestMatch;

    /** 总费用 */
    private String totalAmount;

    /** 是否能升级为早鸟套餐：1-是、2-否 */
    private Integer canUpgradeEarlyBird;

    /** 早鸟套餐的价格 */
    private String earlyBirdPackageAmount;

    /** 日均价格(早鸟套餐价格+超出套餐的标准价格)/总天数 */
    private String earlyBirdAverageAmount;

    /** 早鸟套餐优惠金额 */
    private String earlyBirdPackageReduceAmount;

    /** 使用早鸟套餐后的总费用 */
    private String earlyBirdTotalAmount;

    private PackageInfoBo packageInfo;

    public RenewCalPackageRentAmount toRes() {
        PackageInfoBo packageInfoBo = getPackageInfo();
        PackageInfo packageInfo= packageInfoBo.toRes();
        RenewCalPackageRentAmount.Builder builder = RenewCalPackageRentAmount.newBuilder()
                .setPackageId(packageId == null ? 1L : packageId)
                .setPackageAmount(packageAmount)
                .setPackageDayNum(packageDayNum == null ? 0 : packageDayNum)
                .setOutStandardAmount(outStandardAmount)
                .setOutStandardDayNum(outStandardDayNum == null ? 0 : outStandardDayNum)
                .setAverageAmount(averageAmount)
                .setPackageReduceAmount(packageReduceAmount)
                .setRentDay(rentDay == null ? 0 : rentDay)
                .setUnitPrice(unitPrice)
                .setActivityType(activityType == null ? 1 : activityType)
                .setBestMatch(bestMatch == null ? 2 : bestMatch)
                .setTotalAmount(totalAmount)
                .setCanUpgradeEarlyBird(getCanUpgradeEarlyBird() == null ? 0 : getCanUpgradeEarlyBird())
                .setEarlyBirdPackageAmount(getEarlyBirdPackageAmount())
                .setEarlyBirdAverageAmount(getEarlyBirdAverageAmount())
                .setEarlyBirdPackageReduceAmount(getEarlyBirdPackageReduceAmount())
                .setEarlyBirdTotalAmount(getEarlyBirdTotalAmount());
        if (packageInfo != null) {
            builder.setPackageInfo(packageInfo);
        }
        return builder.build();
    }
}
