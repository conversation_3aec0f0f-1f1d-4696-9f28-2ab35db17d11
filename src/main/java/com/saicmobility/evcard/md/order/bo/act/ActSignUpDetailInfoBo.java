package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.ActSignUpDetailInfo;
import com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing;
import com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing;
import com.saicmobility.evcard.md.mdactservice.api.FullMinusFlexiblePricing;
import com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing;
import com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing;
import com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing;
import com.saicmobility.evcard.md.mdactservice.api.TimeRange;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class ActSignUpDetailInfoBo {
    private String activityName; // 活动名称
    private String activityTag; // 活动标签
    private List<String> orgCodes; // 活动机构列表
    private List<Long> vehicleModelIds; //车型id列表
    private int activityType; // 活动类型：1-满减、2-打折
    private int pricingType; // 定价类型：1-灵活定价、2-规范定价
    private int discountLatitude; // 优惠纬度：1-车辆租金
    private List<FullMinusStandardPricingBo> fullMinusStandardPricing; // 满减规范定价
    private List<FullMinusFlexiblePricingBo> fullMinusFlexiblePricing; // 报名前满减灵活定价
    private List<DiscountStandardPricingBo> discountStandardPricing; // 打折规范定价
    private List<DiscountFlexiblePricingBo> discountFlexiblePricing; // 报名前打折灵活定价
    private int minRentDays; // 最小租期
    private int availableOnHolidays; // 节假日是否可用：1-可用、2-不可用
    private String signUpStartDate; // 报名开始时间 yyyyMMdd
    private String signUpEndDate; // 报名结束时间 yyyyMMdd
    private String pickUpDate; // 取车时间 yyyyMMdd
    private String returnDate; // 还车时间 yyyyMMdd
    private String activityStartDate; // 活动开始时间 yyyyMMdd
    private String activityEndDate; // 活动结束时间 yyyyMMdd
    private List<TimeRangeBo> unavailableDateRanges; // 不可用时间范围

    private long activityId; // 活动id
    private long id; // 报名id
    private List<SignupFullMinusFlexiblePricingBo> signupFullMinusFlexiblePricing; // 报名后满减灵活定价 (报名后会有值)
    private List<SignupDiscountFlexiblePricingBo> signupDiscountFlexiblePricing; // 报名后打折灵活定价 (报名后会有值)
    private String signupOrgCode; // 报名机构
    private List<Long> signupVehicleModelIds; //报名车型id列表
    private int allOrgCodes = 28;//全部机构  1-全部  2-不是全部
    private int allModelIds = 29;//全部车型  1-全部  2-不是全部
    private int maxRentDays;//最大租期 0为没有最大租期
    private int sameDayUseFlag;//仅限下单当日取车  0-非当日使用 1-当日使用

    public static ActSignUpDetailInfoBo from(ActSignUpDetailInfo res){
        ActSignUpDetailInfoBo bo = new ActSignUpDetailInfoBo();
        bo.setActivityName(res.getActivityName());
        bo.setActivityTag(res.getActivityTag());
        bo.setOrgCodes(res.getOrgCodesList());
        bo.setVehicleModelIds(res.getVehicleModelIdsList());
        bo.setActivityType(res.getActivityType());
        bo.setPricingType(res.getPricingType());
        bo.setDiscountLatitude(res.getDiscountLatitude());

        if (CollectionUtils.isNotEmpty(res.getFullMinusStandardPricingList())) {
            List<FullMinusStandardPricingBo> fullMinusStandardPricing = new ArrayList<>();
            for (FullMinusStandardPricing item : res.getFullMinusStandardPricingList()) {
                fullMinusStandardPricing.add(FullMinusStandardPricingBo.from(item));
            }
            bo.setFullMinusStandardPricing(fullMinusStandardPricing);
        }

        if (CollectionUtils.isNotEmpty(res.getFullMinusFlexiblePricingList())) {
            List<FullMinusFlexiblePricingBo> fullMinusFlexiblePricing = new ArrayList<>();
            for (FullMinusFlexiblePricing item : res.getFullMinusFlexiblePricingList()) {
                fullMinusFlexiblePricing.add(FullMinusFlexiblePricingBo.from(item));
            }
            bo.setFullMinusFlexiblePricing(fullMinusFlexiblePricing);
        }

        if (CollectionUtils.isNotEmpty(res.getDiscountStandardPricingList())) {
            List<DiscountStandardPricingBo> discountStandardPricing = new ArrayList<>();
            for (DiscountStandardPricing item : res.getDiscountStandardPricingList()) {
                discountStandardPricing.add(DiscountStandardPricingBo.from(item));
            }
            bo.setDiscountStandardPricing(discountStandardPricing);
        }

        if (CollectionUtils.isNotEmpty(res.getDiscountFlexiblePricingList())) {
            List<DiscountFlexiblePricingBo> discountFlexiblePricing = new ArrayList<>();
            for (DiscountFlexiblePricing item : res.getDiscountFlexiblePricingList()) {
                discountFlexiblePricing.add(DiscountFlexiblePricingBo.from(item));
            }
            bo.setDiscountFlexiblePricing(discountFlexiblePricing);
        }

        bo.setMinRentDays(res.getMinRentDays());
        bo.setAvailableOnHolidays(res.getAvailableOnHolidays());
        bo.setSignUpStartDate(res.getSignUpStartDate());
        bo.setSignUpEndDate(res.getSignUpEndDate());
        bo.setPickUpDate(res.getPickUpDate());
        bo.setReturnDate(res.getReturnDate());
        bo.setActivityStartDate(res.getActivityStartDate());
        bo.setActivityEndDate(res.getActivityEndDate());

        if (CollectionUtils.isNotEmpty(res.getUnavailableDateRangesList())) {
            List<TimeRangeBo> unavailableDateRanges = new ArrayList<>();
            for (TimeRange item : res.getUnavailableDateRangesList()) {
                unavailableDateRanges.add(TimeRangeBo.from(item));
            }
            bo.setUnavailableDateRanges(unavailableDateRanges);
        }

        bo.setActivityId(res.getActivityId());
        bo.setId(res.getId());

        if (CollectionUtils.isNotEmpty(res.getSignupFullMinusFlexiblePricingList())) {
            List<SignupFullMinusFlexiblePricingBo> signupFullMinusFlexiblePricing = new ArrayList<>();
            for (SignupFullMinusFlexiblePricing item : res.getSignupFullMinusFlexiblePricingList()) {
                signupFullMinusFlexiblePricing.add(SignupFullMinusFlexiblePricingBo.from(item));
            }
            bo.setSignupFullMinusFlexiblePricing(signupFullMinusFlexiblePricing);
        }

        if (CollectionUtils.isNotEmpty(res.getSignupDiscountFlexiblePricingList())) {
            List<SignupDiscountFlexiblePricingBo> signupDiscountFlexiblePricing = new ArrayList<>();
            for (SignupDiscountFlexiblePricing item : res.getSignupDiscountFlexiblePricingList()) {
                signupDiscountFlexiblePricing.add(SignupDiscountFlexiblePricingBo.from(item));
            }
            bo.setSignupDiscountFlexiblePricing(signupDiscountFlexiblePricing);
        }

        bo.setSignupOrgCode(res.getSignupOrgCode());
        bo.setSignupVehicleModelIds(res.getSignupVehicleModelIdsList());
        bo.setAllOrgCodes(res.getAllOrgCodes());
        bo.setAllModelIds(res.getAllModelIds());
        bo.setMaxRentDays(res.getMaxRentDays());
        bo.setSameDayUseFlag(res.getSameDayUseFlag());

        return bo;
    }
}
