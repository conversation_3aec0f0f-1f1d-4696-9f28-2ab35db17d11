package com.saicmobility.evcard.md.order.bo.external.billing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 
* @Desc
* <AUTHOR>
* @Date 2022年6月21日 下午8:45:09
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberCardFeeBo {
	private Integer cardUseStatus;//会员卡使用状态 1-正常使用，2-异常使用
	private String deductionAmount;//抵扣费用
	private Integer feeType;//费用类型  
}

