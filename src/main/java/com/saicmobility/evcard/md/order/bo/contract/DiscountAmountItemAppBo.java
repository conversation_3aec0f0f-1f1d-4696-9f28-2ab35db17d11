package com.saicmobility.evcard.md.order.bo.contract;

import lombok.Data;

@Data
public class DiscountAmountItemAppBo {

    // 费用名称
    private String feeName;
    //  费用单价
    private String unitPrice;
    // 费用类型  1租车费-套餐计费 2租车费-标准计费 3燃油费 4畅行服务费 5日租服务费 6车行手续费 7跨门店服务费(异点) 8跨门店服务费(异地) 9送车上门费 10上门取车费 11订单取消违约金  12上门取车取消违约金 13超时还车违约金 14燃油差额(需要退E币)
    // 减免类型  15活动立减 16会员卡抵扣 17优惠券抵扣
    private Integer amountType;
}
