package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.NightServiceAmountItemInfo;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class NightServiceAmountItemInfoBo {

    // 金额
    private BigDecimal amount;

    // 费用类型
    private int amountType;

    public NightServiceAmountItemInfo toRes() {
        NightServiceAmountItemInfo.Builder builder = NightServiceAmountItemInfo.newBuilder();
        if (amount != null) {
            builder.setAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(amount.toPlainString()));
        }
        builder.setAmountType(amountType);
        return builder.build();
    }
}
