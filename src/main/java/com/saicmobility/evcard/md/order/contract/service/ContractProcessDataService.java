package com.saicmobility.evcard.md.order.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessLog;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessLogMapper;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessStateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 合同处理数据访问服务
 * 使用MyBatis Plus风格的数据访问
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Service
public class ContractProcessDataService {

    @Autowired
    private ContractProcessStateMapper contractProcessStateMapper;

    @Autowired
    private ContractProcessLogMapper contractProcessLogMapper;

    // ==================== ContractProcessState 相关查询 ====================

    /**
     * 根据合同ID查询状态
     */
    public ContractProcessState selectByContractId(String contractId) {
        return contractProcessStateMapper.selectOne(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getContractId, contractId)
        );
    }

    /**
     * 根据合同ID和触发类型查询状态
     */
    public ContractProcessState selectByContractIdAndTriggerType(String contractId, String triggerType) {
        return contractProcessStateMapper.selectOne(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getContractId, contractId)
                        .eq(ContractProcessState::getTriggerType, triggerType)
        );
    }

    /**
     * 根据流程实例ID查询状态
     */
    public ContractProcessState selectByProcessInstanceId(String processInstanceId) {
        return contractProcessStateMapper.selectOne(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getProcessInstanceId, processInstanceId)
        );
    }

    /**
     * 查询需要重试的记录
     */
    public List<ContractProcessState> selectRetryableRecords(LocalDateTime now) {
        return contractProcessStateMapper.selectList(
                new LambdaQueryWrapper<ContractProcessState>()
                        .le(ContractProcessState::getNextRetryTime, now)
                        .apply("retry_count < max_retry_count")
                        .eq(ContractProcessState::getIsManualIntervention, false)
        );
    }

    /**
     * 查询超时的记录
     */
    public List<ContractProcessState> selectTimeoutRecords(LocalDateTime timeoutTime) {
        List<String> timeoutStates = Arrays.asList(
                "VEHICLE_CHECK_COMPLETED", 
                "SMS_SEND_COMPLETED", 
                "SEAL_COMPLETED"
        );
        
        return contractProcessStateMapper.selectList(
                new LambdaQueryWrapper<ContractProcessState>()
                        .in(ContractProcessState::getCurrentState, timeoutStates)
                        .le(ContractProcessState::getUpdatedTime, timeoutTime)
        );
    }

    /**
     * 查询处理中的记录
     */
    public List<ContractProcessState> selectProcessingRecords() {
        List<String> processingStates = Arrays.asList(
                "VEHICLE_CHECKING", 
                "SMS_SENDING", 
                "SEALING"
        );
        
        return contractProcessStateMapper.selectList(
                new LambdaQueryWrapper<ContractProcessState>()
                        .in(ContractProcessState::getCurrentState, processingStates)
        );
    }

    /**
     * 查询需要人工干预的记录
     */
    public List<ContractProcessState> selectManualInterventionRecords() {
        return contractProcessStateMapper.selectList(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getIsManualIntervention, true)
        );
    }

    /**
     * 根据用户ID查询处理状态列表（已移除userId字段，此方法已废弃）
     * @deprecated userId字段已移除，请使用其他查询方法
     */
    @Deprecated
    public List<ContractProcessState> selectByUserId(String userId) {
        log.warn("selectByUserId方法已废弃，userId字段已从实体中移除");
        return new java.util.ArrayList<>();
    }

    /**
     * 根据状态查询记录
     */
    public List<ContractProcessState> selectByCurrentState(String currentState) {
        return contractProcessStateMapper.selectList(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getCurrentState, currentState)
        );
    }

    // ==================== ContractProcessLog 相关查询 ====================

    /**
     * 根据合同ID查询日志
     */
    public List<ContractProcessLog> selectLogsByContractId(String contractId) {
        return contractProcessLogMapper.selectList(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .eq(ContractProcessLog::getContractId, contractId)
                        .orderByDesc(ContractProcessLog::getCreatedTime)
        );
    }

    /**
     * 根据流程实例ID查询日志
     */
    public List<ContractProcessLog> selectLogsByProcessInstanceId(String processInstanceId) {
        return contractProcessLogMapper.selectList(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .eq(ContractProcessLog::getProcessInstanceId, processInstanceId)
                        .orderByDesc(ContractProcessLog::getCreatedTime)
        );
    }

    /**
     * 根据操作类型查询日志
     */
    public List<ContractProcessLog> selectLogsByOperationType(String operationType, Integer limit) {
        LambdaQueryWrapper<ContractProcessLog> wrapper = new LambdaQueryWrapper<ContractProcessLog>()
                .eq(ContractProcessLog::getOperationType, operationType)
                .orderByDesc(ContractProcessLog::getCreatedTime);
        
        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }
        
        return contractProcessLogMapper.selectList(wrapper);
    }

    /**
     * 根据第三方请求ID查询日志
     */
    public List<ContractProcessLog> selectLogsByThirdPartyRequestId(String thirdPartyRequestId) {
        return contractProcessLogMapper.selectList(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .eq(ContractProcessLog::getThirdPartyRequestId, thirdPartyRequestId)
                        .orderByDesc(ContractProcessLog::getCreatedTime)
        );
    }

    /**
     * 根据操作结果查询日志
     */
    public List<ContractProcessLog> selectLogsByOperationResult(String operationResult) {
        return contractProcessLogMapper.selectList(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .eq(ContractProcessLog::getOperationResult, operationResult)
                        .orderByDesc(ContractProcessLog::getCreatedTime)
        );
    }

    /**
     * 根据时间范围查询日志
     */
    public List<ContractProcessLog> selectLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return contractProcessLogMapper.selectList(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .ge(ContractProcessLog::getCreatedTime, startTime)
                        .le(ContractProcessLog::getCreatedTime, endTime)
                        .orderByDesc(ContractProcessLog::getCreatedTime)
        );
    }

    /**
     * 查询失败的操作日志
     */
    public List<ContractProcessLog> selectFailedLogs(LocalDateTime since) {
        return contractProcessLogMapper.selectList(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .eq(ContractProcessLog::getOperationResult, "FAILED")
                        .ge(ContractProcessLog::getCreatedTime, since)
                        .orderByDesc(ContractProcessLog::getCreatedTime)
        );
    }

    // ==================== 统计查询 ====================

    /**
     * 统计指定状态的记录数量
     */
    public Long countByCurrentState(String currentState) {
        return Long.valueOf(contractProcessStateMapper.selectCount(
                new LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getCurrentState, currentState)
        ));
    }

    /**
     * 统计指定用户的处理记录数量（已移除userId字段，此方法已废弃）
     * @deprecated userId字段已移除，请使用其他统计方法
     */
    @Deprecated
    public Long countByUserId(String userId) {
        log.warn("countByUserId方法已废弃，userId字段已从实体中移除");
        return 0L;
    }

    /**
     * 统计指定时间范围内的日志数量
     */
    public Long countLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return Long.valueOf(contractProcessLogMapper.selectCount(
                new LambdaQueryWrapper<ContractProcessLog>()
                        .ge(ContractProcessLog::getCreatedTime, startTime)
                        .le(ContractProcessLog::getCreatedTime, endTime)
        ));
    }
}
