package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.SignupFullMinusFlexiblePricing;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class SignupFullMinusFlexiblePricingBo {
    private String days; //满多少天  天数
    private String discountAmount; // 优惠金额
    private String minDiscountAmount; //最小优惠金额
    private String maxDiscountAmount; //最小优惠金额

    public static SignupFullMinusFlexiblePricingBo from(SignupFullMinusFlexiblePricing item) {
        SignupFullMinusFlexiblePricingBo bo = new SignupFullMinusFlexiblePricingBo();
        bo.setDays(item.getDays());
        bo.setDiscountAmount(item.getDiscountAmount());
        bo.setMinDiscountAmount(item.getMinDiscountAmount());
        bo.setMaxDiscountAmount(item.getMaxDiscountAmount());
        return bo;
    }
}
