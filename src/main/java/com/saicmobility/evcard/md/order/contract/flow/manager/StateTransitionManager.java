package com.saicmobility.evcard.md.order.contract.flow.manager;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.order.contract.dto.ContractProcessContext;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
// import com.saicmobility.evcard.md.order.contract.enums.ContractProcessEvent;
import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.flow.chain.FlowChain;
import com.saicmobility.evcard.md.order.contract.flow.dto.ActionResult;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessStateMapper;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessDataService;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.statemachine.StateMachine;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 状态转换管理器
 * 负责管理合同处理流程的状态转换逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Component
public class StateTransitionManager {
    
    @Autowired
    private ContractProcessDataService dataService;

    @Autowired
    private ContractProcessStateMapper contractProcessStateMapper;

    @Autowired
    private ContractProcessLockService lockService;
    
    /**
     * 转换到目标状态
     * 
     * @param contractId 合同ID
     * @param targetState 目标状态
     * @param context 流程上下文
     * @return 是否成功
     */
    public boolean transitionTo(String contractId, ContractProcessStateEnum targetState,
                               ContractProcessContext context) {
        return transitionToWithRetry(contractId, targetState, context, 3);
    }

    /**
     * 转换到目标状态（带重试机制和并发控制）
     */
    public boolean transitionToWithRetry(String contractId, ContractProcessStateEnum targetState,
                                        ContractProcessContext context, int maxRetries) {
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 使用合同级别的锁确保并发安全
                return lockService.executeWithContractLock(contractId, () -> {
                    return doStateTransition(contractId, targetState, context);
                });

            } catch (Exception e) {
                retryCount++;
                log.warn("状态转换失败，第{}次重试, contractId: {}, targetState: {}, error: {}",
                        retryCount, contractId, targetState, e.getMessage());

                if (retryCount >= maxRetries) {
                    log.error("状态转换重试次数已达上限, contractId: {}, targetState: {}", contractId, targetState, e);
                    return false;
                }

                // 短暂等待后重试
                try {
                    Thread.sleep(100 * retryCount); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }

        return false;
    }

    /**
     * 执行实际的状态转换
     */
    private boolean doStateTransition(String contractId, ContractProcessStateEnum targetState,
                                     ContractProcessContext context) throws Exception {
        // 重新查询最新状态（避免并发问题）
        ContractProcessState processState;
        if (context.getProcessInstanceId() != null) {
            processState = dataService.selectByProcessInstanceId(context.getProcessInstanceId());
        } else {
            processState = dataService.selectByContractId(contractId);
        }

        if (processState == null) {
            log.warn("未找到合同处理状态, contractId: {}", contractId);
            return false;
        }

        ContractProcessStateEnum currentState =
                ContractProcessStateEnum.fromCode(processState.getCurrentState());

        log.info("状态转换: {} -> {}, contractId: {}, processInstanceId: {}",
                currentState, targetState, contractId, context.getProcessInstanceId());

        // 检查状态转换是否合法
        if (!canTransition(currentState, targetState)) {
            log.warn("不允许的状态转换: {} -> {}", currentState, targetState);
            return false;
        }

        // 执行状态转换（使用乐观锁）
        boolean updateResult = updateProcessStateWithOptimisticLock(processState, currentState, targetState, context);

        if (updateResult) {
            log.info("状态转换成功: {} -> {}, contractId: {}, version: {}",
                    currentState, targetState, contractId, processState.getVersion());
        } else {
            log.warn("状态转换失败（乐观锁冲突）: {} -> {}, contractId: {}",
                    currentState, targetState, contractId);
        }

        return updateResult;
    }
    
    /**
     * 根据动作结果获取下一个状态
     * 
     * @param currentState 当前状态
     * @param actionResult 动作结果
     * @param flowChain 流程链
     * @return 下一个状态
     */
    public ContractProcessStateEnum getNextState(
            ContractProcessStateEnum currentState,
            ActionResult actionResult, 
            FlowChain flowChain) {
        
        if (flowChain != null) {
            return flowChain.getNextState(currentState, actionResult.isSuccess());
        }
        
        // 默认的状态转换逻辑
        return getDefaultNextState(currentState, actionResult.isSuccess());
    }
    
    /**
     * 检查是否可以进行状态转换
     * 
     * @param fromState 源状态
     * @param toState 目标状态
     * @return 是否可以转换
     */
    public boolean canTransition(ContractProcessStateEnum fromState,
                                 ContractProcessStateEnum toState) {
        
        // 如果是相同状态，允许转换（用于更新数据）
        if (fromState == toState) {
            return true;
        }
        
        // 从失败状态可以转换到任何处理状态（用于重试）
        if (fromState.isFailedState() && toState.isProcessingState()) {
            return true;
        }
        
        // 从人工干预状态可以转换到任何状态
        if (fromState == ContractProcessStateEnum.MANUAL_INTERVENTION) {
            return true;
        }
        
        // 定义允许的状态转换
        switch (fromState) {
            case INIT:
                return toState == ContractProcessStateEnum.VEHICLE_CHECKING;
                
            case VEHICLE_CHECKING:
                return toState == ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED ||
                       toState == ContractProcessStateEnum.VEHICLE_CHECK_FAILED;
                
            case VEHICLE_CHECK_COMPLETED:
                return toState == ContractProcessStateEnum.SMS_SENDING;
                
            case SMS_SENDING:
                return toState == ContractProcessStateEnum.SMS_SEND_COMPLETED ||
                       toState == ContractProcessStateEnum.SMS_SEND_FAILED;
                
            case SMS_SEND_COMPLETED:
                return toState == ContractProcessStateEnum.SEALING;
                
            case SEALING:
                return toState == ContractProcessStateEnum.SEAL_COMPLETED ||
                       toState == ContractProcessStateEnum.SEAL_FAILED;
                
            case SEAL_COMPLETED:
                return toState == ContractProcessStateEnum.PROCESS_COMPLETED;
                
            default:
                return false;
        }
    }
    
    /**
     * 获取默认的下一个状态
     */
    private ContractProcessStateEnum getDefaultNextState(
            ContractProcessStateEnum currentState, boolean success) {
        
        if (!success) {
            // 失败时的状态转换
            switch (currentState) {
                case VEHICLE_CHECKING:
                    return ContractProcessStateEnum.VEHICLE_CHECK_FAILED;
                case SMS_SENDING:
                    return ContractProcessStateEnum.SMS_SEND_FAILED;
                case SEALING:
                    return ContractProcessStateEnum.SEAL_FAILED;
                default:
                    return ContractProcessStateEnum.PROCESS_FAILED;
            }
        }
        
        // 成功时的状态转换
        switch (currentState) {
            case INIT:
                return ContractProcessStateEnum.VEHICLE_CHECKING;
            case VEHICLE_CHECKING:
                return ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED;
            case VEHICLE_CHECK_COMPLETED:
                return ContractProcessStateEnum.SMS_SENDING;
            case SMS_SENDING:
                return ContractProcessStateEnum.SMS_SEND_COMPLETED;
            case SMS_SEND_COMPLETED:
                return ContractProcessStateEnum.SEALING;
            case SEALING:
                return ContractProcessStateEnum.SEAL_COMPLETED;
            case SEAL_COMPLETED:
                return ContractProcessStateEnum.PROCESS_COMPLETED;
            default:
                return currentState; // 保持当前状态
        }
    }
    
    /**
     * 更新流程状态
     */
    private void updateProcessState(ContractProcessState processState, 
                                   ContractProcessStateEnum currentState,
                                   ContractProcessStateEnum targetState,
                                   ContractProcessContext context) {
        
        processState.setPreviousState(currentState.getCode())
                   .setCurrentState(targetState.getCode())
                   .setUpdateTime(LocalDateTime.now());
        
        if (context != null) {
            processState.setProcessData(JSON.toJSONString(context))
                       .setUpdateOperName(context.getOperator());
        }
        
        contractProcessStateMapper.updateById(processState);
    }

    /**
     * 使用乐观锁更新流程状态
     */
    private boolean updateProcessStateWithOptimisticLock(ContractProcessState processState,
                                                        ContractProcessStateEnum currentState,
                                                        ContractProcessStateEnum targetState,
                                                        ContractProcessContext context) {

        // 保存原始版本号
        Integer originalVersion = processState.getVersion();

        // 更新状态信息
        processState.setPreviousState(currentState.getCode())
                   .setCurrentState(targetState.getCode())
                   .setUpdateTime(LocalDateTime.now())
                   .setUpdateOperName(context.getOperator());

        // 如果是失败状态，设置错误信息
        if (targetState.isFailedState()) {
            processState.setErrorMessage(context.getErrorMessage());
        } else {
            processState.setErrorMessage(null);
        }

        // 更新流程数据
        if (context != null) {
            processState.setProcessData(JSON.toJSONString(context));
        }

        // 使用MyBatis Plus的乐观锁机制更新
        // MyBatis Plus会自动检查version字段，如果版本不匹配则更新失败
        int updateCount = contractProcessStateMapper.updateById(processState);

        if (updateCount > 0) {
            log.debug("乐观锁更新成功, contractId: {}, version: {} -> {}",
                    processState.getContractId(), originalVersion, processState.getVersion());
            return true;
        } else {
            log.warn("乐观锁更新失败（版本冲突）, contractId: {}, version: {}",
                    processState.getContractId(), originalVersion);
            return false;
        }
    }
}
