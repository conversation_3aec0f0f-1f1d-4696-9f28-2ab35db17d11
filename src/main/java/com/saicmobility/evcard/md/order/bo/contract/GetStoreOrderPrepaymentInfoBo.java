package com.saicmobility.evcard.md.order.bo.contract;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.AccompanyingCardAmount;
import com.saicmobility.evcard.md.mdorderservice.api.GetStoreOrderPrepaymentInfoRes;
import com.saicmobility.evcard.md.mdorderservice.api.GoodsModelAmountInfo;
import com.saicmobility.evcard.md.mdorderservice.api.ServiceAmount;
import com.saicmobility.evcard.md.order.bo.fee.RenewPayPriceDetailBo;
import com.saicmobility.evcard.md.order.bo.fee.RenewPayPriceDetailNewBo;
import com.saicmobility.evcard.md.order.entity.ContractItemExemptionRecord;
import com.saicmobility.evcard.md.order.utils.AccompanyingCardUtil;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Data
public class GetStoreOrderPrepaymentInfoBo {

    //  用户id
    private String mid;
    //  租车合同号
    private String contractId;
    //  取车门店id
    private Long pickUpStoreId;
    //  取车门店名称
    private String pickUpStoreName;
    //  还车门店id
    private Long returnStoreId;
    //  还车门店名称
    private String returnStoreName;
    //  取车时间
    private LocalDateTime pickUpDateTime;
    //  还车时间
    private LocalDateTime returnDateTime;
    // 用车时长(天)
    private Integer useVehicleDays;
    // 剩余支付时间 mmss
    private String remainPayTime;
    // 支付截至时间 yyyyMMddHHmmss
    private LocalDateTime stopPayTime;

    // E币金额
    private BigDecimal eBalance;
    // E币抵消金额
    private BigDecimal eCoinOffset;
    // 支付订单号
    private String payOrderNo;
    // 总金额
    private BigDecimal totalAmount;
    // 实付金额
    private BigDecimal realAmount;
    // 已优惠金额
    private BigDecimal discountAmount;
    // 购买折扣卡金额
    private BigDecimal cardAmount;
    // 预付款费用是否超过限制 1：超过 2：不超过
    private Integer amountLimit;
    // 是否使用套餐 1：使用 2：不使用
    private Integer usePackage;
    // 购买会员卡记录
    private Long purchaseId;

    // 续租时服务费明细
    private RenewPayPriceDetailBo renewPayPriceDetail;
    // 续租要展示夜间服务费
    private NightServiceFeeBo nightServiceFeeBo;
    // 加购服务费明细
    private ServiceAmountAppBo serviceAmountAppBo;

    // 续租费用明细 V5.10.0
    private RenewPayPriceDetailNewBo renewPayPriceDetailNewBo;

    // 续租时默认展示之前已选花呗分期数
    private Integer stagingNum;

    private Long pickUpServiceId; // 上门取车服务id
    private String pickUpBaiduLongitude; // 上门取车经度
    private String pickUpBaiduLatitude; // 上门取车维度
    private String pickUpPointName;  // 上门取车取车点名称
    private String pickUpPointAddress;  // 上门取车取车点地址
    private String  planPickUpOrgCode ;  // 计划取车机构id
    private Long  planPickUpStoreId;   // 计划取车门店id
    private Long  planPickUpShopSeq;   // 计划上门取车虚拟门店id

    private String mileage; // 计划上门取车距离

	// -------------企业支付--------------
	private int isEnableEnterprisePay; // 是否满足企业支付条件 1是 2否
	private int reasonType; // 不满足原因 1: 不在时间段 2:余额不足 3：超单笔限额 4: 续租不是下单时企业会员，需补足预付款 5: 无限额 6：有限额、但充足
							// 7：预付时不是企业会员  8：使用随享卡不可使用企业支付 9：员工用车选择的支付方式为个人支付
	private String remainQuota; // 剩余额度 -1:无限制
	private String singleQuota; // 单笔最大额度 -1:无限制
	private String supplyPayAmount; // 需补缴金额
	private String huabeiDesc = "分期支付，用车更轻松"; // 花呗展示文案 分期支付，用车更轻松
	private String reason; // 不满足原因
    private String mergeAmount; // 合计金额

    // -----------------随享卡减免明细------------------
    // 随享卡减免明细
    private List<ContractItemExemptionRecord> accompanyingCardItemExemptionRecordList;
    // 随享卡id
    private long userAccompanyingCardId;

    // 修改前
    private GoodsModelAmountInfoBo original;

    // 修改后
    private GoodsModelAmountInfoBo current;

    // 配置的取消倒计时（分钟）
    private int cancelConfigTime;

    // 加购升级尊享服务，升级前的优享服务明细
    private ServiceAmountAppBo ofcBuyUpgrade;

    private String renewTime;//


    public GetStoreOrderPrepaymentInfoRes toRes() {
        GetStoreOrderPrepaymentInfoRes.Builder builder = GetStoreOrderPrepaymentInfoRes.newBuilder();
        builder.setMid(mid)
                .setContractId(contractId)
                .setPickUpStoreId(pickUpStoreId)
                .setPickUpStoreName(pickUpStoreName)
                .setReturnStoreId(returnStoreId)
                .setReturnStoreName(returnStoreName)
                .setPickUpDateTime(DateUtil.dateToString(pickUpDateTime, DateUtil.DATE_TYPE4))
                .setReturnDateTime(DateUtil.dateToString(returnDateTime, DateUtil.DATE_TYPE4))
                .setUseVehicleDays(useVehicleDays)
                .setRemainPayTime(remainPayTime)
                .setStopPayTime(DateUtil.dateToString(stopPayTime, DateUtil.DATE_TYPE4))
                .setEBalance(Optional.ofNullable(eBalance).map(BigDecimal::toPlainString).orElse(""))
                .setPayOrderNo(payOrderNo)
                .setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(totalAmount))
                .setRealAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(realAmount))
                .setDiscountAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(discountAmount))
                .setCardAmount(Optional.ofNullable(cardAmount).map(BigDecimalUtil::toPlainStringNoDecimalPoint).orElse(""))
                .setAmountLimit(amountLimit)
                .setUsePackage(usePackage)
                .setPurchaseId(Optional.ofNullable(purchaseId).orElse(0L))
                .setStagingNum(Optional.ofNullable(stagingNum).orElse(0))
                .setPickUpServiceId(Optional.ofNullable(pickUpServiceId).orElse(0L))
                .setPickUpBaiduLongitude(Optional.ofNullable(pickUpBaiduLongitude).orElse(""))
                .setPickUpBaiduLatitude(Optional.ofNullable(pickUpBaiduLatitude).orElse(""))
                .setPickUpPointName(Optional.ofNullable(pickUpPointName).orElse(""))
                .setPickUpPointAddress(Optional.ofNullable(pickUpPointAddress).orElse(""))
                .setPlanPickUpOrgCode(Optional.ofNullable(planPickUpOrgCode).orElse(""))
                .setPickUpStoreId(Optional.ofNullable(pickUpStoreId).orElse(0L))
                .setPlanPickUpStoreId(Optional.ofNullable(planPickUpStoreId).orElse(0L))
                .setPlanPickUpShopSeq(Optional.ofNullable(planPickUpShopSeq).orElse(0L))
				.setMileage(mileage).setIsEnableEnterprisePay(isEnableEnterprisePay).setReasonType(reasonType)
				.setRemainQuota(BigDecimalUtil.toPlainString(remainQuota))
                .setSingleQuota(singleQuota)
                .setSupplyPayAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(supplyPayAmount))
                .setMergeAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(mergeAmount))
				.setHuabeiDesc(huabeiDesc)
                .setReason(reason);
        if (eCoinOffset != null) {
            builder.setECoinOffset(eCoinOffset.stripTrailingZeros().toPlainString());
        } else {
            builder.setECoinOffset("");
        }

        if (eCoinOffset != null &&
                eCoinOffset.subtract(eCoinOffset.setScale(0, BigDecimal.ROUND_DOWN)).compareTo(BigDecimal.ZERO) != 0) {
            builder.setRealAmount(realAmount.stripTrailingZeros().toPlainString());
        }

        if (renewPayPriceDetail != null) {
            builder.setRenewPayPriceDetail(renewPayPriceDetail.toRes());
        }
        if (nightServiceFeeBo != null) {
            builder.setNightServiceFee(nightServiceFeeBo.toRes());
        }
        if (serviceAmountAppBo != null) {
            ServiceAmount serviceAmount = serviceAmountAppBo.toRes();
            builder.setServiceAmount(serviceAmount);
        }
        // 续租费用明细 V5.10.0
        if (renewPayPriceDetailNewBo != null) {
            builder.setRenewPayPriceDetailNew(renewPayPriceDetailNewBo.toRes());
        }
        // 加购升级尊享服务，升级前的优享服务明细
        if (ofcBuyUpgrade != null) {
            ServiceAmount serviceAmount = ofcBuyUpgrade.toRes();
            builder.setOfcBuyUpgrade(serviceAmount);
        }
        AccompanyingCardAmount accompanyingCardAmount = AccompanyingCardUtil.toRes(accompanyingCardItemExemptionRecordList, userAccompanyingCardId);
        if (accompanyingCardAmount != null) {
            builder.setAccompanyingCardAmount(accompanyingCardAmount);
        }
        if (original != null) {
            GoodsModelAmountInfo build = GoodsModelAmountInfo.newBuilder()
                    .setGoodsModelName(original.getGoodsModelName())
                    .setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(original.getTotalAmount().toPlainString()))
                    .build();
            builder.setOriginal(build);
        }
        if (current != null) {
            GoodsModelAmountInfo build = GoodsModelAmountInfo.newBuilder()
                    .setGoodsModelName(current.getGoodsModelName())
                    .setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(current.getTotalAmount().toPlainString()))
                    .build();
            builder.setCurrent(build);
        }
        builder.setCancelConfigTime(cancelConfigTime);
        builder.setRenewTime(renewTime);
        return builder.build();
    }
}
