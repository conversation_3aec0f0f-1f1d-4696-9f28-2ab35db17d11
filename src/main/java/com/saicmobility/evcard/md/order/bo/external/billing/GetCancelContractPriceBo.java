package com.saicmobility.evcard.md.order.bo.external.billing;

import com.saicmobility.evcard.md.mdbillingservice.api.EstimatedCancelOrderDamagesRes;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Data
public class GetCancelContractPriceBo {

    //  取消违约金
    private BigDecimal cancelDamagesAmount;

    public static GetCancelContractPriceBo from(EstimatedCancelOrderDamagesRes res) {
        GetCancelContractPriceBo getCancelContractPriceBo = new GetCancelContractPriceBo();
        if (StringUtils.isBlank(res.getCancelDamagesAmount())) {
            getCancelContractPriceBo.setCancelDamagesAmount(BigDecimal.ZERO);
        } else {
            getCancelContractPriceBo.setCancelDamagesAmount(new BigDecimal(res.getCancelDamagesAmount()));
        }
        return getCancelContractPriceBo;
    }

    public boolean isMemberBreach() {
        return false;
    }

    public boolean isNeedRefund() {
        return false;
    }

}
