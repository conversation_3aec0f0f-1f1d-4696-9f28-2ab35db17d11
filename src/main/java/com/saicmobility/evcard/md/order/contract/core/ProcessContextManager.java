package com.saicmobility.evcard.md.order.contract.core;

import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 流程上下文管理器
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class ProcessContextManager {

    /**
     * 创建处理上下文
     * 
     * @param processState 流程状态
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return 处理上下文
     */
    public ProcessContext createContext(ContractProcessState processState, String eventType, Object eventData) {
        log.debug("创建处理上下文: eventType={}, processInstanceId={}", 
                eventType, processState.getProcessInstanceId());
        
        ProcessContext context = new ProcessContext(processState, eventType, eventData);
        
        // 设置一些通用属性
        context.setAttribute("startTime", System.currentTimeMillis());
        context.setAttribute("eventType", eventType);
        
        return context;
    }

    /**
     * 销毁处理上下文
     * 
     * @param context 处理上下文
     */
    public void destroyContext(ProcessContext context) {
        if (context != null) {
            log.debug("销毁处理上下文: eventType={}, processInstanceId={}", 
                    context.getEventType(), context.getProcessInstanceId());
            
            // 清理上下文资源
            context.clearAttributes();
        }
    }

    /**
     * 复制上下文
     * 
     * @param source 源上下文
     * @return 新的上下文
     */
    public ProcessContext copyContext(ProcessContext source) {
        if (source == null) {
            return null;
        }
        
        ProcessContext newContext = new ProcessContext(source.getProcessState(), source.getEventType(), source.getEventData());
        
        // 复制属性
        newContext.getAttributes().putAll(source.getAllAttributes());
        
        return newContext;
    }
}
