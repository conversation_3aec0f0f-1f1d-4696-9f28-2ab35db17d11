package com.saicmobility.evcard.md.order.bo.contract;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.AccompanyingCardAmount;
import com.saicmobility.evcard.md.mdorderservice.api.GetContractPrePayInfoRes;
import com.saicmobility.evcard.md.order.bo.fee.RenewPayPriceDetailNewBo;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;
import com.saicmobility.evcard.md.order.entity.ContractItemExemptionRecord;
import com.saicmobility.evcard.md.order.utils.AccompanyingCardUtil;
import com.saicmobility.evcard.md.order.utils.AppVersionUtil;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class GetContractPrePayInfoBo {


    //  用户id
    private String mid;
    //  租车合同号
    private String contractId;
    //  取车门店id
    private Long pickUpStoreId;
    //  取车门店名称
    private String pickUpStoreName;
    //  还车门店id
    private Long returnStoreId;
    //  还车门店名称
    private String returnStoreName;
    //  取车时间
    private LocalDateTime pickUpDateTime;
    //  还车时间
    private LocalDateTime returnDateTime;

    //---- 费用明细 ----
    // 总金额
    private BigDecimal totalAmount;

    //租车费
    private RentAmountAppBo rent;
    //  服务费
    private ServiceAmountAppBo service;
    // 折扣减免
    private DiscountAmountAppBo discount;
    // 购买折扣卡金额
    private CardAmountAppBo card;
    // 增值服务费
    private AddServiceAmountAppBo addService;
    // 服务费总金额=增值服务费+保障服务费+门店手续费
    private BigDecimal totalServiceAmount;

    // -----------------随享卡减免明细------------------
    // 随享卡减免明细
    private List<ContractItemExemptionRecord> accompanyingCardItemExemptionRecordList;
    // 随享卡id
    private long userAccompanyingCardId;

    // 保障服务费
    private ServiceAmountAppBo assureService;

    // 门店手续费
    private ServiceAmountAppBo storeProcedures;

    // 收费金额
    private BigDecimal chargeAmount;

    private String refundStartTime;

    private String refundEndTime;

    // 退款信息
    private RenewPayPriceDetailNewBo renewPayPriceDetailNewBo;

    private String appVersion; // 下单时的版本

    // 保障服务费
    private ServiceAmountAppBo valueAddService;

    public GetContractPrePayInfoRes toRes() {
        GetContractPrePayInfoRes.Builder builder = GetContractPrePayInfoRes.newBuilder();
        builder.setMid(mid)
                .setContractId(contractId)
                .setPickUpStoreId(pickUpStoreId)
                .setReturnStoreName(pickUpStoreName)
                .setReturnStoreId(returnStoreId)
                .setReturnStoreName(returnStoreName)
                .setPickUpDateTime(DateUtil.dateToString(pickUpDateTime, DateUtil.DATE_TYPE4))
                .setReturnDateTime(DateUtil.dateToString(returnDateTime, DateUtil.DATE_TYPE4))
                .setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(totalAmount.toPlainString()))
                .setAppVersion(appVersion == null ? "" : appVersion);

        // appv5.12，由于有随享卡抵扣，所以总金额可能为0，但是费用明细还需要展示
        if (appVersion != null && AppVersionUtil.compareTo(appVersion, BusinessConstants.APP_VERSION_5_12_0) >= 0) {
            if (rent != null) {
                builder.setRent(rent.toRes());
            }
            if (assureService != null) {
                builder.setAssureService(assureService.toRes());
            }
            if (storeProcedures != null) {
                builder.setStoreProcedures(storeProcedures.toRes());
            }
        } else {
            if (rent != null && rent.getRentAmount() != null && rent.getRentAmount().compareTo(BigDecimal.ZERO) > 0) {
                builder.setRent(rent.toRes());
            }
            if (assureService != null && assureService.getServiceAmount() != null  && assureService.getServiceAmount().compareTo(BigDecimal.ZERO) > 0) {
                builder.setAssureService(assureService.toRes());
            }
            if (storeProcedures != null && storeProcedures.getServiceAmount() != null  && storeProcedures.getServiceAmount().compareTo(BigDecimal.ZERO) > 0) {
                builder.setStoreProcedures(storeProcedures.toRes());
            }
        }

        if (service != null && service.getServiceAmount() != null && service.getServiceAmount().compareTo(BigDecimal.ZERO) > 0) {
            builder.setService(service.toRes());
        }
        if (discount != null && discount.getServiceAmount() != null && discount.getServiceAmount().compareTo(BigDecimal.ZERO) > 0) {
            builder.setDiscount(discount.toRes());
        }
        if (card != null && card.getCardAmount() != null && card.getCardAmount().compareTo(BigDecimal.ZERO) > 0) {
            builder.setCard(card.toRes());
        }
        if (addService != null && addService.getServiceAmount() != null && addService.getServiceAmount().compareTo(BigDecimal.ZERO) > 0) {
            builder.setAddService(addService.toRes());
        }

        if (valueAddService != null && valueAddService.getServiceAmount() != null && valueAddService.getServiceAmount().compareTo(BigDecimal.ZERO) > 0) {
            builder.setValueAddService(valueAddService.toRes());
        }


        builder.setTotalServiceAmount(BigDecimalUtil.toPlainString(totalServiceAmount));
//        Optional.ofNullable(rent).ifPresent(obj -> builder.setRent(rent.toRes()));
//        Optional.ofNullable(service).ifPresent(obj -> builder.setService(service.toRes()));
//        Optional.ofNullable(discount).ifPresent(obj -> builder.setDiscount(discount.toRes()));
//        Optional.ofNullable(card).ifPresent(obj -> builder.setCard(card.toRes()));
//        Optional.ofNullable(addService).ifPresent(obj -> builder.setAddService(addService.toRes()));
        AccompanyingCardAmount accompanyingCardAmount = AccompanyingCardUtil.toRes(accompanyingCardItemExemptionRecordList, userAccompanyingCardId);
        if (accompanyingCardAmount != null) {
            builder.setAccompanyingCardAmount(accompanyingCardAmount);
        }

        if (chargeAmount != null) {
            builder.setChargeAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(chargeAmount.toPlainString()));
        }

        if (renewPayPriceDetailNewBo != null) {
            builder.setRenewPayPriceDetailNew(renewPayPriceDetailNewBo.toRes());
        }
        if (StringUtils.isNotEmpty(refundStartTime)) {
            builder.setRefundStartTime(refundStartTime);
        }
        if (StringUtils.isNotEmpty(refundEndTime)) {
            builder.setRefundEndTime(refundEndTime);
        }
        return builder.build();
    }
}
