package com.saicmobility.evcard.md.order.bo.contract;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.UnReturnContractInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

@Data
public class UnReturnContractInfoDto {

    private Long id;

    private String mid;

    //租车合同号
    private String contractId;

    //合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消
    private Integer contractStatus;

    //车架号
    private String vin;

    //计划还车时间
    private LocalDateTime planReturnDateTime;

    public UnReturnContractInfo toUnReturnContractInfo() {
        return UnReturnContractInfo.newBuilder()
                .setId(id)
                .setMid(mid)
                .setContractId(contractId)
                .setContractStatus(contractStatus)
                .setVin(vin)
                .setPlanReturnDateTime(planReturnDateTime == null ? StringUtils.EMPTY : DateUtil.dateToString(planReturnDateTime, DateUtil.DATE_TYPE4))
                .build();

    }
}
