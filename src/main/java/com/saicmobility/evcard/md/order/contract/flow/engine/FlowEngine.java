package com.saicmobility.evcard.md.order.contract.flow.engine;

import com.saicmobility.evcard.md.order.contract.dto.ContractProcessContext;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.flow.chain.FlowChain;
import com.saicmobility.evcard.md.order.contract.flow.chain.FlowChainBuilder;
import com.saicmobility.evcard.md.order.contract.flow.dto.ActionResult;
import com.saicmobility.evcard.md.order.contract.flow.enums.ActionType;
import com.saicmobility.evcard.md.order.contract.flow.factory.ActionHandlerFactory;
import com.saicmobility.evcard.md.order.contract.flow.handler.ActionHandler;
import com.saicmobility.evcard.md.order.contract.flow.manager.StateTransitionManager;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 流程引擎
 * 负责协调整个合同处理流程的执行
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Component
public class FlowEngine {
    
    @Autowired
    private ActionHandlerFactory handlerFactory;
    
    @Autowired
    private FlowChainBuilder chainBuilder;
    
    @Autowired
    private StateTransitionManager stateTransitionManager;
    
    @Autowired
    private ContractProcessDataService dataService;
    
    /**
     * 执行完整的流程
     * 
     * @param context 流程上下文
     * @return 执行结果
     */
    public ActionResult executeFlow(ContractProcessContext context) {
        log.info("开始执行流程, contractId: {}", context.getContractId());
        
        try {
            // 获取当前状态
            ContractProcessState processState;
            if (context.getProcessInstanceId() != null) {
                processState = dataService.selectByProcessInstanceId(context.getProcessInstanceId());
            } else {
                processState = dataService.selectByContractId(context.getContractId());
            }

            if (processState == null) {
                return ActionResult.failure("PROCESS_STATE_NOT_FOUND", "未找到流程状态");
            }

            ContractProcessStateEnum currentState =
                    ContractProcessStateEnum.fromCode(processState.getCurrentState());

            // 构建流程链（支持触发类型）
            FlowChain flowChain = chainBuilder.buildChain(currentState, context.getTriggerType());
            
            // 获取下一个需要执行的动作
            ActionType nextAction = flowChain.getNextAction(currentState, context);
            
            if (nextAction == ActionType.NONE) {
                log.info("流程已完成或无需执行动作, contractId: {}, currentState: {}", 
                        context.getContractId(), currentState);
                return ActionResult.success().setNextAction(ActionType.NONE);
            }
            
            // 执行动作
            return executeAction(nextAction, context, flowChain);
            
        } catch (Exception e) {
            log.error("执行流程失败, contractId: {}", context.getContractId(), e);
            return ActionResult.failure("FLOW_EXECUTION_FAILED", e.getMessage());
        }
    }
    
    /**
     * 执行指定的动作
     * 
     * @param actionType 动作类型
     * @param context 流程上下文
     * @return 执行结果
     */
    public ActionResult executeAction(ActionType actionType, ContractProcessContext context) {
        return executeAction(actionType, context, null);
    }
    
    /**
     * 执行指定的动作（带流程链）
     * 
     * @param actionType 动作类型
     * @param context 流程上下文
     * @param flowChain 流程链
     * @return 执行结果
     */
    public ActionResult executeAction(ActionType actionType, ContractProcessContext context, FlowChain flowChain) {
        log.info("开始执行动作: {}, contractId: {}", actionType, context.getContractId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取动作处理器
            ActionHandler handler = handlerFactory.getHandler(actionType, context);
            
            // 获取当前状态
            ContractProcessState processState;
            if (context.getProcessInstanceId() != null) {
                processState = dataService.selectByProcessInstanceId(context.getProcessInstanceId());
            } else {
                processState = dataService.selectByContractId(context.getContractId());
            }

            ContractProcessStateEnum currentState =
                    ContractProcessStateEnum.fromCode(processState.getCurrentState());
            
            // 执行动作前的状态转换（如果需要）
            ContractProcessStateEnum processingState = getProcessingState(actionType);
            if (processingState != null && processingState != currentState) {
                stateTransitionManager.transitionTo(context.getContractId(), processingState, context);
            }
            
            // 执行动作
            ActionResult result = handler.execute(context);
            
            // 根据执行结果进行状态转换
            if (result != null) {
                ContractProcessStateEnum nextState =
                        stateTransitionManager.getNextState(currentState, result, flowChain);
                
                if (nextState != currentState) {
                    stateTransitionManager.transitionTo(context.getContractId(), nextState, context);
                }
                
                // 设置执行时间
                if (result.getExecutionTime() == null) {
                    result.setExecutionTime(System.currentTimeMillis() - startTime);
                }
            }
            
            log.info("动作执行完成: {}, contractId: {}, success: {}, executionTime: {}ms", 
                    actionType, context.getContractId(), result.isSuccess(), result.getExecutionTime());
            
            return result;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("动作执行失败: {}, contractId: {}", actionType, context.getContractId(), e);
            
            return ActionResult.failure("ACTION_EXECUTION_FAILED", e.getMessage())
                    .setExecutionTime(executionTime);
        }
    }
    
    /**
     * 继续执行流程（用于回调后继续）
     * 
     * @param context 流程上下文
     * @return 执行结果
     */
    public ActionResult continueFlow(ContractProcessContext context) {
        log.info("继续执行流程, contractId: {}", context.getContractId());
        
        try {
            // 获取当前状态
            ContractProcessState processState;
            if (context.getProcessInstanceId() != null) {
                processState = dataService.selectByProcessInstanceId(context.getProcessInstanceId());
            } else {
                processState = dataService.selectByContractId(context.getContractId());
            }

            ContractProcessStateEnum currentState =
                    ContractProcessStateEnum.fromCode(processState.getCurrentState());

            // 构建流程链（支持触发类型）
            FlowChain flowChain = chainBuilder.buildChain(currentState, context.getTriggerType());
            
            // 检查是否已完成
            if (flowChain.isCompleted(currentState)) {
                log.info("流程已完成, contractId: {}, currentState: {}", context.getContractId(), currentState);
                return ActionResult.success().setNextAction(ActionType.NONE);
            }
            
            // 获取下一个动作
            ActionType nextAction = flowChain.getNextAction(currentState, context);
            
            if (nextAction == ActionType.NONE) {
                log.info("无下一个动作, contractId: {}, currentState: {}", context.getContractId(), currentState);
                return ActionResult.success().setNextAction(ActionType.NONE);
            }
            
            // 执行下一个动作
            return executeAction(nextAction, context, flowChain);
            
        } catch (Exception e) {
            log.error("继续执行流程失败, contractId: {}", context.getContractId(), e);
            return ActionResult.failure("CONTINUE_FLOW_FAILED", e.getMessage());
        }
    }
    
    /**
     * 根据动作类型获取对应的处理状态
     */
    private ContractProcessStateEnum getProcessingState(ActionType actionType) {
        switch (actionType) {
            case VEHICLE_CHECK:
                return ContractProcessStateEnum.VEHICLE_CHECKING;
            case SMS_SEND:
                return ContractProcessStateEnum.SMS_SENDING;
            case SEAL:
                return ContractProcessStateEnum.SEALING;
            default:
                return null;
        }
    }
}
