package com.saicmobility.evcard.md.order.contract.service;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessStateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 事件驱动的合同流程服务
 * 简化版本，基于事件驱动状态机
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Service
public class EventDrivenContractProcessService {

    @Autowired
    private EventDrivenStateMachine stateMachine;

    @Autowired
    private ContractProcessStateMapper stateMapper;

    /**
     * 启动合同流程
     * 
     * @param contractId 合同ID
     * @param triggerType 触发类型
     * @param eventData 事件数据
     * @return 流程实例ID
     */
    @Transactional
    public String startProcess(String contractId, String triggerType, Map<String, Object> eventData) {
        log.info("启动合同流程: contractId={}, triggerType={}", contractId, triggerType);
        
        try {
            // 1. 生成流程实例ID
            String processInstanceId = generateProcessInstanceId(contractId, triggerType);
            
            // 2. 创建初始流程状态
            ContractProcessState processState = createInitialState(contractId, processInstanceId, triggerType, eventData);
            stateMapper.insert(processState);
            
            // 3. 触发MQ消息接收事件
            EventDrivenStateMachine.ProcessResult result = stateMachine.processEvent(
                    "MQ_MESSAGE_RECEIVED", processInstanceId, eventData);
            
            if (result.isSuccess()) {
                log.info("合同流程启动成功: contractId={}, processInstanceId={}", contractId, processInstanceId);
                return processInstanceId;
            } else {
                log.error("合同流程启动失败: contractId={}, error={}", contractId, result.getErrorMessage());
                throw new RuntimeException("流程启动失败: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("启动合同流程异常: contractId={}, triggerType={}", contractId, triggerType, e);
            throw new RuntimeException("启动流程异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理第三方回调事件
     * 
     * @param eventType 事件类型
     * @param processInstanceId 流程实例ID
     * @param callbackData 回调数据
     * @return 处理结果
     */
    @Transactional
    public boolean handleCallback(String eventType, String processInstanceId, Map<String, Object> callbackData) {
        log.info("处理第三方回调: eventType={}, processInstanceId={}", eventType, processInstanceId);
        
        try {
            // 处理回调事件
            EventDrivenStateMachine.ProcessResult result = stateMachine.processEvent(
                    eventType, processInstanceId, callbackData);
            
            if (result.isSuccess()) {
                log.info("回调处理成功: eventType={}, processInstanceId={}", eventType, processInstanceId);
                return true;
            } else {
                log.error("回调处理失败: eventType={}, processInstanceId={}, error={}", 
                        eventType, processInstanceId, result.getErrorMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("处理回调异常: eventType={}, processInstanceId={}", eventType, processInstanceId, e);
            return false;
        }
    }

    /**
     * 处理验车成功回调
     * 
     * @param processInstanceId 流程实例ID
     * @param vehicleCheckResult 验车结果
     * @return 处理结果
     */
    public boolean handleVehicleCheckSuccess(String processInstanceId, Map<String, Object> vehicleCheckResult) {
        return handleCallback("VEHICLE_CHECK_SUCCESS", processInstanceId, vehicleCheckResult);
    }

    /**
     * 处理验车失败回调
     * 
     * @param processInstanceId 流程实例ID
     * @param errorInfo 错误信息
     * @return 处理结果
     */
    public boolean handleVehicleCheckFailed(String processInstanceId, Map<String, Object> errorInfo) {
        return handleCallback("VEHICLE_CHECK_FAILED", processInstanceId, errorInfo);
    }

    /**
     * 处理短信发送成功回调
     * 
     * @param processInstanceId 流程实例ID
     * @param smsResult 短信发送结果
     * @return 处理结果
     */
    public boolean handleSmsSuccess(String processInstanceId, Map<String, Object> smsResult) {
        return handleCallback("SMS_SEND_SUCCESS", processInstanceId, smsResult);
    }

    /**
     * 处理短信发送失败回调
     * 
     * @param processInstanceId 流程实例ID
     * @param errorInfo 错误信息
     * @return 处理结果
     */
    public boolean handleSmsFailed(String processInstanceId, Map<String, Object> errorInfo) {
        return handleCallback("SMS_SEND_FAILED", processInstanceId, errorInfo);
    }

    /**
     * 处理盖章成功回调
     * 
     * @param processInstanceId 流程实例ID
     * @param sealResult 盖章结果
     * @return 处理结果
     */
    public boolean handleSealSuccess(String processInstanceId, Map<String, Object> sealResult) {
        return handleCallback("SEAL_SUCCESS", processInstanceId, sealResult);
    }

    /**
     * 处理盖章失败回调
     * 
     * @param processInstanceId 流程实例ID
     * @param errorInfo 错误信息
     * @return 处理结果
     */
    public boolean handleSealFailed(String processInstanceId, Map<String, Object> errorInfo) {
        return handleCallback("SEAL_FAILED", processInstanceId, errorInfo);
    }

    /**
     * 重试失败的流程
     * 
     * @param processInstanceId 流程实例ID
     * @return 重试结果
     */
    @Transactional
    public boolean retryProcess(String processInstanceId) {
        log.info("重试流程: processInstanceId={}", processInstanceId);
        
        try {
            // 获取当前流程状态
            ContractProcessState processState = getCurrentState(processInstanceId);
            if (processState == null) {
                log.warn("流程状态不存在: processInstanceId={}", processInstanceId);
                return false;
            }
            
            // 根据当前状态确定重试事件
            String retryEvent = determineRetryEvent(processState.getCurrentState());
            if (retryEvent == null) {
                log.warn("当前状态不支持重试: processInstanceId={}, currentState={}", 
                        processInstanceId, processState.getCurrentState());
                return false;
            }
            
            // 触发重试事件
            Map<String, Object> retryData = JSON.parseObject(processState.getProcessData(), Map.class);
            EventDrivenStateMachine.ProcessResult result = stateMachine.processEvent(
                    retryEvent, processInstanceId, retryData);
            
            if (result.isSuccess()) {
                log.info("流程重试成功: processInstanceId={}", processInstanceId);
                return true;
            } else {
                log.error("流程重试失败: processInstanceId={}, error={}", processInstanceId, result.getErrorMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("重试流程异常: processInstanceId={}", processInstanceId, e);
            return false;
        }
    }

    /**
     * 获取流程状态
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程状态
     */
    public ContractProcessState getProcessState(String processInstanceId) {
        return getCurrentState(processInstanceId);
    }

    // ==================== 私有方法 ====================

    /**
     * 生成流程实例ID
     */
    private String generateProcessInstanceId(String contractId, String triggerType) {
        return String.format("%s_%s_%d", contractId, triggerType, System.currentTimeMillis());
    }

    /**
     * 创建初始流程状态
     */
    private ContractProcessState createInitialState(String contractId, String processInstanceId, 
                                                  String triggerType, Map<String, Object> eventData) {
        ContractProcessState processState = new ContractProcessState();
        processState.setContractId(contractId);
        processState.setProcessInstanceId(processInstanceId);
        processState.setTriggerType(triggerType);
        processState.setCurrentState("INIT");
        processState.setPreviousState(null);
        processState.setProcessData(JSON.toJSONString(eventData));
        processState.setRetryCount(0);
        processState.setMaxRetryCount(3);
        processState.setIsManualIntervention(false);
        processState.setCreateTime(LocalDateTime.now());
        processState.setUpdateTime(LocalDateTime.now());
        processState.setVersion(1);
        
        return processState;
    }

    /**
     * 获取当前流程状态
     */
    private ContractProcessState getCurrentState(String processInstanceId) {
        return stateMapper.selectOne(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ContractProcessState>()
                        .eq(ContractProcessState::getProcessInstanceId, processInstanceId)
                        .orderByDesc(ContractProcessState::getCreateTime)
                        .last("LIMIT 1")
        );
    }

    /**
     * 确定重试事件
     */
    private String determineRetryEvent(String currentState) {
        switch (currentState) {
            case "VEHICLE_CHECK_FAILED":
                return "MQ_MESSAGE_RECEIVED"; // 重新开始验车
            case "SMS_SEND_FAILED":
                return "VEHICLE_CHECK_SUCCESS"; // 重新发送短信
            case "SEAL_FAILED":
                return "SMS_SEND_SUCCESS"; // 重新盖章
            default:
                return null; // 不支持重试
        }
    }
}
