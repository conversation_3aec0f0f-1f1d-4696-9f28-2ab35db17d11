package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.SearchCurrentStoreContractDiscountRecommendRes;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class SearchCurrentStoreContractDiscountRecommendBo {
    private int retCode; // 返回码
    private String retMsg; // 返回消息
    private String renewalDiscountDesc; // 再续租几天享受几折优惠
    private int isDisplayBuySuiXiangCard; // 是否显示可购买随享卡：1-是、2-否

    public SearchCurrentStoreContractDiscountRecommendRes toRes() {
        return SearchCurrentStoreContractDiscountRecommendRes.newBuilder()
                .setRetCode(retCode)
                .setRetMsg(retMsg)
                .setRenewalDiscountDesc(renewalDiscountDesc)
                .setIsDisplayBuySuiXiangCard(isDisplayBuySuiXiangCard)
                .build();
    }
}
