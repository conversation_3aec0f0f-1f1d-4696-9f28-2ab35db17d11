package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.DiscountStandardPricing;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class DiscountStandardPricingBo {
    private String days;  //满多少天  天数
    private String discount;   //折扣

    public static DiscountStandardPricingBo from(DiscountStandardPricing item) {
        DiscountStandardPricingBo bo = new DiscountStandardPricingBo();
        bo.setDays(item.getDays());
        bo.setDiscount(item.getDiscount());
        return bo;
    }
}
