package com.saicmobility.evcard.md.order.bo.contractitem;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractItemExemptionBo {

    // 租车合同号
    private String contractId;

    // 合同项编号
    private String contractItemId;

    // 减免金额
    private BigDecimal exemptAmount;

    // 单价
    private BigDecimal unitPrice = BigDecimal.ZERO;

    // 数量
    private BigDecimal num = BigDecimal.ONE;

    // 费用类型 (同租车合同费用明细表)
    private Integer amountType;

    // 减免来源：1自动减免, 2客服系统，3履约小程序
    private Integer exemptOrigin;

    // 减免事件：
    private Integer exemptEvent;

    // 减免原因分类
    private String exemptReasonType;

    // 减免原因详情
    private String exemptReason;

    // 支付方式 1：线下 2：线上
    private Integer payMode;

    // 扩展字段，格式为json对象ContractItemExemptionExtBo
    private String ext;
}
