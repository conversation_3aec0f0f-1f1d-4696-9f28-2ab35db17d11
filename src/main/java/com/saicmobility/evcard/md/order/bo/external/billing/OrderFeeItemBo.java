package com.saicmobility.evcard.md.order.bo.external.billing;

import com.saicmobility.evcard.md.order.bo.contract.RentDetailBo;
import com.saicmobility.evcard.md.order.bo.ofcbuyservice.ValueAddedServiceDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/** 
* @Desc
* <AUTHOR>
* @Date 2022年6月21日 下午8:40:33
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderFeeItemBo {
	private String feeItemDesc;// 费用描述
	private String feeAmount;// 费用总金额
	private Integer feeType;// 费用类型
	private String unitPrice;//单价
	private Integer totalDay;//总天数
	private List<RentDetailBo> rentDetail; // 租金明细，当feeType=2才有此字段
	private String accompanyingCardDeductionDesc; // 随享卡抵扣描述，如1：抵扣2天，如2：抵扣2次
	private int order; // 用来排序用，订单费用明细排序规则租车费>随享卡>自营活动>优惠券>企业折扣
	private List<ValueAddedServiceDto> valueAddedService; // 增值业务费，当feeType=48才有此字段
}	

