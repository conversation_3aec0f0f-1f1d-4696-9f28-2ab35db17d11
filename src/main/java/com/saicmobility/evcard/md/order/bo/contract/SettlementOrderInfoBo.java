package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.SettlementOrderInfo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Data
public class SettlementOrderInfoBo {
    private String vehicleNo; // 车牌号
    private String company; // 结算公司
    private String beginDate; // 结算开始日期（取车日期） yyyyMMddHHmmss
    private String endDate; // 结算结算日期（还车日期） yyyyMMddHHmmss
    private String taxCategories; // 税种（默认“普票”）
    private String taxRate; // 税率（默认13%）
    private String settlementType; // 结算单类型（默认“还车结算”）
    private String settlementManager; // 结算经理
    private String channelName; // 渠道名称
    private String channelKey; // 渠道key
    private String vin; // 车架号
    private long storeId; // 门店id
    private String storeName; // 门店名称
    private String storeCode; // 门店编码
    private String qlContractId; // 擎路订单号
    private String externalContractId; // 第三方订单号
    private String contractId; // 订单号
    private int contractStatus; // 合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消
    private int contractStatusNew; // 合同状态新： 1:已预约(待支付) 2:待取车(备车中) 3:待取车(已备车) 4:已取车 5:还车中(收车中) 6:已还车(待支付) 7:已取消(待支付) 8:已完成(已支付) 9:已取消(已支付) 10:已取消
    private int qlContractStatus; // 擎路订单状态 1已确认、2已排车、3已取车、4已还车、5已取消
    private String storeVehicleModelName; // 门店车型名称
    private long storeVehicleModelId; // 门店车型id
    private String payBody; // 支付主体/收款主体
    private String longRentContractId; // 企业短租长租合同号
    private String zfbTradeNo;//车生活订单交易号

    public SettlementOrderInfo toRes() {
        return SettlementOrderInfo.newBuilder()
                .setVehicleNo(Optional.ofNullable(vehicleNo).orElse(StringUtils.EMPTY))
                .setCompany(Optional.ofNullable(company).orElse(StringUtils.EMPTY))
                .setBeginDate(Optional.ofNullable(beginDate).orElse(StringUtils.EMPTY))
                .setEndDate(Optional.ofNullable(endDate).orElse(StringUtils.EMPTY))
                .setTaxCategories(Optional.ofNullable(taxCategories).orElse(StringUtils.EMPTY))
                .setTaxRate(Optional.ofNullable(taxRate).orElse(StringUtils.EMPTY))
                .setSettlementType(Optional.ofNullable(settlementType).orElse(StringUtils.EMPTY))
                .setSettlementManager(Optional.ofNullable(settlementManager).orElse(StringUtils.EMPTY))
                .setChannelKey(Optional.ofNullable(channelKey).orElse(StringUtils.EMPTY))
                .setChannelName(Optional.ofNullable(channelName).orElse(StringUtils.EMPTY))
                .setVin(Optional.ofNullable(vin).orElse(StringUtils.EMPTY))
                .setStoreId(storeId)
                .setStoreName(Optional.ofNullable(storeName).orElse(StringUtils.EMPTY))
                .setStoreCode(Optional.ofNullable(storeCode).orElse(StringUtils.EMPTY))
                .setQlContractId(Optional.ofNullable(qlContractId).orElse(StringUtils.EMPTY))
                .setExternalContractId(Optional.ofNullable(externalContractId).orElse(StringUtils.EMPTY))
                .setContractId(Optional.ofNullable(contractId).orElse(StringUtils.EMPTY))
                .setContractStatus(contractStatus)
                .setContractStatusNew(contractStatusNew)
                .setQlContractStatus(qlContractStatus)
                .setStoreVehicleModelName(Optional.ofNullable(storeVehicleModelName).orElse(StringUtils.EMPTY))
                .setStoreVehicleModelId(storeVehicleModelId)
                .setPayBody(Optional.ofNullable(payBody).orElse(StringUtils.EMPTY))
                .setLongRentContractId(Optional.ofNullable(longRentContractId).orElse(StringUtils.EMPTY))
                .setZfbTradeNo(Optional.ofNullable(zfbTradeNo).orElse(StringUtils.EMPTY))
                .build();
    }
}
