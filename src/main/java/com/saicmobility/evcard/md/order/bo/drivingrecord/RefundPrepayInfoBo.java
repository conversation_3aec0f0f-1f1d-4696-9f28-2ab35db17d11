package com.saicmobility.evcard.md.order.bo.drivingrecord;

import com.saicmobility.evcard.md.mdorderservice.api.RefundPrepayInfo;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RefundPrepayInfoBo {

    private String title;

    private int hour;

    private BigDecimal amount;

    public RefundPrepayInfo toRes() {
        return RefundPrepayInfo.newBuilder()
                .setTitle(title)
                .setHour(hour + "")
                .setAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(amount.toPlainString()))
                .build();
    }
}
