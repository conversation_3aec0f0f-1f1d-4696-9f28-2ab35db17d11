package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.NightServiceAmountInfo;
import com.saicmobility.evcard.md.mdorderservice.api.NightServiceAmountItemInfo;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class NightServiceAmountInfoBo {

    // 金额
    private BigDecimal amount;

    // 费用类型
    private int amountType;

    // 费用明细
    private List<NightServiceAmountItemInfoBo> nightServiceAmountItemInfoBoList;

    public NightServiceAmountInfo toRes() {
        NightServiceAmountInfo.Builder builder = NightServiceAmountInfo.newBuilder();
        if (amount != null) {
            builder.setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(amount.toPlainString()));
        }
        builder.setAmountType(amountType);
        if (CollectionUtils.isNotEmpty(nightServiceAmountItemInfoBoList)) {
            List<NightServiceAmountItemInfo> nightServiceAmountItemInfoList = new ArrayList<>();
            for (NightServiceAmountItemInfoBo info : nightServiceAmountItemInfoBoList) {
                nightServiceAmountItemInfoList.add(info.toRes());
            }
            builder.addAllNightServiceAmountItemInfo(nightServiceAmountItemInfoList);
        }
        return builder.build();
    }
}
