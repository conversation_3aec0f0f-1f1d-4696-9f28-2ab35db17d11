package com.saicmobility.evcard.md.order.bo.act;

import java.util.List;

import com.saicmobility.evcard.md.mdactservice.api.CouponViewForApp;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class CouponViewBo {

    // 优惠券编号
    private Long userCouponSeq;

    // 会员id
    private String mid;

    // 优惠券模板编号
    private Long couponSeq;

    // 有效期开始时间
    private String startDate;

    // 有效期结束时间
    private String expiresDate;

    // 0：未使用 1：已使用   2 已作废. 3 已过期
    private Integer status;

    // 优惠券名称
    private String couponOrigin;

    // 优惠券金额
    private String couponValue;

    // 优惠券描述（例如：10元优惠券）
    private String des;

    // 优惠券类型 1:直扣 2：折扣
    private Integer couponType;

    // 优惠券类型 1:直扣 2：折扣
    private Integer transactionType;

    // 业务类型（0不限/ 1分时/ 2短租/3长租)
    private Integer serviceType;

    // 折扣券的折扣率
    private Integer discountRate;

    // 金额门槛 最小消费金额
    private String minAmount;

    // 折扣率，单位%, 折扣券使用
    private String durationLimit;

    // 活动编号
    private String actionId;

    // 发券机构 (原orgSeq参数)
    private String orgCode;

    // 核销该优惠券的订单号 (原orderSeq参数)
    private String orderNo;

    // 抵扣金额，优惠券核销之后才会有该值
    private String discount;

    // 发放时间  yyyyMMddHHmmssSSS
    private String createdTime;

    // 是否有使用限制.
    private Boolean hasLimitCondition;

    // 使用限制的标签描述
    private List<String> tags;

    //左上角区域.
    private String space1;

    // 左上角 金额下方的区域.
    private List<String> space2;

    // 券的主题区域下方. (车牌车型及到期时间)
    private List<String> space3;

    // 剩余时间条件描述
    private String remainTimeTag;

    // 车辆使用条件限制（车牌车型）. todo确认此字段是否必须
    private String vehicleLimitDesc;

    // 是否可用。orderCoupon使用
    private Boolean availability;

    // 租车模式类别：1时租 2日租 空表示通用，多个以逗号分隔
    private String rentMethodGroup;

    public static CouponViewBo from(CouponViewForApp coupon) {
        CouponViewBo bo = new CouponViewBo();
        bo.setUserCouponSeq(coupon.getUserCouponSeq());
        bo.setMid(coupon.getMid());
        bo.setCouponSeq(coupon.getCouponSeq());
        bo.setStartDate(coupon.getStartDate());
        bo.setExpiresDate(coupon.getExpiresDate());
        bo.setStatus(coupon.getStatus());
        bo.setCouponOrigin(coupon.getCouponOrigin());
        bo.setCouponValue(coupon.getCouponValue());
        bo.setDes(coupon.getDes());
        bo.setCouponType(coupon.getCouponType());
        bo.setTransactionType(coupon.getTransactionType());
        bo.setServiceType(coupon.getServiceType());
        bo.setDiscountRate(coupon.getDiscountRate());
        bo.setMinAmount(coupon.getMinAmount());
        bo.setDurationLimit(coupon.getDurationLimit());
        bo.setActionId(bo.getActionId());
        bo.setOrgCode(bo.getOrgCode());
        bo.setOrderNo(bo.getOrderNo());
        bo.setCreatedTime(coupon.getCreatedTime());
        bo.setHasLimitCondition(coupon.getHasLimitCondition());
        bo.setTags(coupon.getTagsList());
        bo.setSpace1(coupon.getSpace1());
        bo.setSpace2(coupon.getSpace2List());
        bo.setSpace3(coupon.getSpace3List());
        bo.setRemainTimeTag(coupon.getRemainTimeTag());
        bo.setVehicleLimitDesc(coupon.getVehicleLimitDesc());
        bo.setAvailability(coupon.getAvailability());
        bo.setRentMethodGroup(StringUtils.isBlank(coupon.getRentMethodGroup()) ? "0" : coupon.getRentMethodGroup());
        return bo;
    }
}
