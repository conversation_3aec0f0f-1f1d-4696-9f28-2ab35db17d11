package com.saicmobility.evcard.md.order.bo.alipaycarlife;

import com.saicmobility.evcard.md.mdorderservice.api.StoreAndVehicleIdAmount;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class StoreAndVehicleIdAmountBo {

    private String planPickUpOrgCode;

    private long planPickUpStoreId;

    private String planReturnOrgCode;

    private long planReturnStoreId;

    private long goodsModelId;

    private BigDecimal averageRentAmount;

    private BigDecimal totalAmount;

    public StoreAndVehicleIdAmount toRes() {
        StoreAndVehicleIdAmount.Builder builder = StoreAndVehicleIdAmount.newBuilder().setPlanPickUpOrgCode(planPickUpOrgCode)
                .setPlanPickUpStoreId(planPickUpStoreId)
                .setPlanReturnOrgCode(planReturnOrgCode)
                .setPlanReturnStoreId(planReturnStoreId)
                .setGoodsModelId(goodsModelId);
        if (averageRentAmount != null) {
            builder.setAverageRentAmount(averageRentAmount.toPlainString());
        }
        if (totalAmount != null) {
            builder.setTotalAmount(totalAmount.toPlainString());
        }
        return builder.build();
    }
}
