package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.ActivityCouponModel;
import lombok.Data;

/**
 * 优惠券模板信息
 */
@Data
public class CouponMmpBo {

    private Long activityCouponId; //活动券模板记录id

    private Long activityId; //所属市场活动编号

    private Long activityStatus; //活动状态（0:待发布 1:待上线 2:进行中 3:已停止 4:暂停中）

    private Long couponSeq; //优惠券模板编号

    private String couponName;  //优惠券模板名称

    // 优惠券金额
    private String couponValue;

    // 还有剩余字段

    public static CouponMmpBo from(ActivityCouponModel model) {
        CouponMmpBo bo = new CouponMmpBo();
        bo.setActivityCouponId(model.getActivityCouponId());
        bo.setActivityId(model.getActivityId());
        bo.setActivityStatus(model.getActivityStatus());
        bo.setCouponSeq(model.getCouponSeq());
        bo.setCouponName(model.getCouponName());
        bo.setCouponValue(model.getCouponValue());
        // 字段赋值不全
        return bo;
    }
}
