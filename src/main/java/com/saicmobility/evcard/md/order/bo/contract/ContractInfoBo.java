package com.saicmobility.evcard.md.order.bo.contract;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.CarRentalContractInfo;
import com.saicmobility.evcard.md.order.enums.LongRentProductLineEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class ContractInfoBo implements Serializable {

    private Long id;

    // 用户id
    private String mid;

    // 租车合同号
    private String contractId;

    // 合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消
    private Integer contractStatus;

    // 合同状态新： 1:已预约(待支付) 2:待取车(备车中) 3:待取车(已备车) 4:已取车 5:还车中(收车中) 6:已还车(待支付) 7:已取消(待支付) 8:已完成(已支付) 9:已取消(已支付) 10:已取消
    private Integer contractStatusNew;

    private String externalContractId;

    private String qlContractId;

    private Integer qlContractStatus;

    private Integer billingType = 0;

    // 合同预付总金额
    private BigDecimal prepaidTotalAmount;

    // 计划取车门店id
    private Long planPickUpStoreId;

    // 计划取车虚拟门店id
    private Long planPickUpShopSeq;

    // 计划还车门店id
    private Long planReturnStoreId;

    // 计划还车虚拟门店id
    private Long planReturnShopSeq;

    // 计划取车时间
    private LocalDateTime planPickUpDateTime;

    // 计划还车时间
    private LocalDateTime planReturnDateTime;

    // 最晚可还车时间
    private LocalDateTime lastReturnDateTime;

    // 下单还车时间
    private LocalDateTime orderReturnDateTime;

    // 商品车型id
    private Long goodsModelId;

    //渠道车型
    private String channelVehicleId;



    // 下单城市id
    private Long cityId;

    // 下单渠道（分包渠道）
    private String orderChannel;

    // 合同自动取消时间
    private LocalDateTime autoCancelTime;

    // 取消方式 1用户取消  2自动取消 3平台取消
    private Integer cancelWay;

    // 车架号
    private String vin;

    // 实际取车时间
    private LocalDateTime realPickUpTime;

    // 实际还车时间
    private LocalDateTime realReturnTime;

    // 计费开始时间
    private LocalDateTime billingStartTime;
    // 计费截止时间
    private LocalDateTime billingEndTime;

    // 支付时间
    private LocalDateTime payTime;

    // 取消时间
    private LocalDateTime cancelTime;

    // 实际取车门店id
    private Long realPickUpStoreId;

    // 实际取车虚拟门店id
    private Long realPickUpShopSeq;

    // 实际还车门店id
    private Long realReturnStoreId;

    // 实际还车虚拟门店id
    private Long realReturnShopSeq;

    // 实际用车时长  单位：分钟
    private Integer costTime;

    // 订单所属机构(取车门店运营机构)
    private String orgCode;

    // 用户所属企业id(下单时保存)
    private String userAgencyId;

	// 企业支付标识 1:个人支付 2:企业支付
	private Integer businessFree;

	// 平台id
    private Long platformId = 0L;

    // 下单应用id
    private String appKey;

    // 下单时app版本
    private String appVersion;

    // 下单客户端IP
    private String clientIp;

    // 下单平台
    private String appType;

    private Integer depositState;
    private Integer oilGetCapacity;
    private Integer oilReturnCapacity;
    // 是否强行续租过 1没有 2有
    private Integer forceRenew;

    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    private Long vehicleModelId = 0L; //资产车型id  车生活使用

    //二级渠道 1携程、2携程分销、3哈罗、4飞猪、5悟空、6高德、7滴滴
    private String secondAppKey;

    // 税务主体
    private String taxMainCompany;

    //长租合同号
    private String longRentContractId;

    //代客下单转移合同类型：1-evcard个人用车、2-企业用车、3-员工用车、4-vip用车
    private Integer transferContractType;

    //代客下单转移合同支付方式：1-个人支付、2-企业支付、3-个人后付
    private Integer transferContractPayMode;

    //取车方式 3-免费接送
    private Integer pickupAddrType;

    //还车方式 3-免费接送
    private Integer returnAddrType;

    //免费接送取车地址
    private String pickupAddr;

    //免费接送还车地址
    private String returnAddr;

    //无损取消（金额）
    private int cancelDamageAmount;

    private int noWorriedOrder;//1:一口价订单

    private String packageId;// 无忧租，为空的话非无忧租

    private int illegalDepositAmount;//违章押金,携程一口价使用
    private int rentDepositAmount;//车辆押金，携程一口价使用

    private int defaultingParty;//违约承担方 1=企业承担 2=个人承担

    private String zfbTradeNo; //支付宝线上支付交易号

    private Integer separateAccountsFlag; // 是否分账：1=分账 2=非分账

    private Integer tenmaFlag;

    private int isHideOrderFlag;//是否隐藏订单： 0：默认值，否；1：是

    //是否自助取还订单 0：默认值，否；1：是,3:自助取还转非自助取还
    private Integer selfProrder;

    private Integer anxinOrder;//飞猪安心订单 1:是，0不是

    //@ApiModelProperty(value = "下单人和用车人是否一致。1：一致，2：不一致")
    private Integer isSeparate;

/*    //@ApiModelProperty(value = "下单人和用车人不一致时候，用车人的mid。")
    private String userMid;*/

    public CarRentalContractInfo toRes() {
        CarRentalContractInfo.Builder builder = CarRentalContractInfo.newBuilder()
                .setMid(mid)
                .setContractId(contractId)
                .setContractStatus(contractStatus == null ? 0 : contractStatus)
                .setPlanPickUpStoreId(planPickUpStoreId == null ? 0 : planPickUpStoreId)
                .setPlanPickUpShopSeq(planPickUpShopSeq == null ? 0 : planPickUpShopSeq)
                .setPlanReturnStoreId(planReturnStoreId == null ? 0 : planReturnStoreId)
                .setPlanReturnShopSeq(planReturnShopSeq == null ? 0 : planReturnShopSeq)
                .setPlanPickUpDateTime(null != planPickUpDateTime ? planPickUpDateTime.format(DateUtil.DATE_TYPE1) : "")
                .setPlanReturnDateTime(null != planReturnDateTime ? planReturnDateTime.format(DateUtil.DATE_TYPE1) : "")
                .setLastReturnDateTime(null != lastReturnDateTime ? lastReturnDateTime.format(DateUtil.DATE_TYPE1) : "")
                .setGoodsModelId(goodsModelId)
                .setCityId(cityId)
                .setOrderChannel(orderChannel)
                .setAutoCancelTime(null != autoCancelTime ? autoCancelTime.format(DateUtil.DATE_TYPE1) : "")
                .setCancelTime(null != cancelTime ? cancelTime.format(DateUtil.DATE_TYPE1) : "")
                .setRealPickUpStoreId(realPickUpStoreId == null ? 0 : realPickUpStoreId)
                .setRealPickUpShopSeq(realPickUpShopSeq == null ? 0 : realPickUpShopSeq)
                .setRealReturnStoreId(realReturnStoreId == null ? 0 : realReturnStoreId)
                .setRealReturnShopSeq(realReturnShopSeq == null ? 0 : realReturnShopSeq)
                .setVin(vin)
                .setVehicleModelId(vehicleModelId)
                .setSelfProrder(selfProrder);
        return builder.build();
    }

    public String getSecondAppKey() {
        if (StringUtils.isEmpty(secondAppKey)) {
            if (StringUtils.isNotEmpty(orderChannel)) {
                return "second_" + orderChannel;
            }
            return "second_" + appKey;
        }
        return LongRentProductLineEnum.getRentSecondAppKey(secondAppKey);
    }

    public String getAppKey() {
        if (StringUtils.isNotEmpty(orderChannel)) {
            return orderChannel;
        }
        return appKey;
    }

    public String getRealAppKey() {
        return appKey;
    }

    public String getRealSecondAppKey() {
        return secondAppKey;
    }
}
