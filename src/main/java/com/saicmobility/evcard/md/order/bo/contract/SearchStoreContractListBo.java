package com.saicmobility.evcard.md.order.bo.contract;

import java.util.ArrayList;
import java.util.List;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import org.apache.commons.collections.CollectionUtils;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.ContractInfo;
import com.saicmobility.evcard.md.mdorderservice.api.ContractTravelInfo;
import com.saicmobility.evcard.md.order.bo.external.store.GetStoreDetailInfoForAppBo;

import lombok.Data;

@Data
public class SearchStoreContractListBo {

    private String contractId; // 合同id

    private Integer contractStatus; //  合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消

    private Long pickUpStoreId; //  取车门店id (没有real就plan)

    private String pickUpStoreName; //  取车门店名称 (没有real就plan)

    private Long returnStoreId; //  还车门店id (没有real就plan)

    private String returnStoreName; //  还车门店名称 (没有real就plan)

    private String pickUpDateTime; //  取车时间 yyyymmddhhmmss (没有real就plan)

    private String returnDateTime; //  还车时间 yyyymmddhhmmss (没有real就plan)

    private String goodsModelName; //  商品车型名称

    private String vin; //  车架号 (订单里的)

    private String vehicleNo; //  车牌号 (订单里的)

    private Integer isSendVehService; //  是否为上门订单信息 1：是 2：否

    private SendVehOrderInfoBo sendVehOrderInfo; // 送车上门订单信息

    private Integer isSmqcService; //  是否为上门取车订单 1：是 2：否

    private PickUpVehOrderInfoBo pickUpVehOrderInfo; // 上门取车订单信息

    private Integer unReturn; // 用车是否超时 1：已超时 2：未超时

    private String realAmount; // 实付金额 (完成订单或待结算) 查询结算订单

    private Integer appraiseOrder; // 是否评价过订单 1：已评价  2：未评价

    private Integer appraiseCredit; // 评价积分 文字评价 + 图片评价总和 (未评价才有)

    private List<ContractTravelInfoBo> contractTravelInfo; // 行程列表

    private Integer hasAdvanceAmount; // 是否付过款 1：是 2：否（付过款但是取消了订单）

    private String waitPayAMount; // 待支付金额（包括预付款、续租、结算金额）

    private Integer amountType; // 金额类型 1：违约金 2：支付 (取消订单则是违约金)

    private Integer billReturnStatus; // 退款状态  1：退款失败 2：退款成功

    private int recoveryTaskState; // 追偿任务状态 1：核损中 2：无需追偿 3：已核损

    private Integer isReturnFail;// 是否为退款失败 1：是 2：否


    private Integer isSeparate;// 下单人和用车人是否一致。1：一致，2：不一致
    private Integer buyerFeeAuth;// 下单人费用查权限。 1:有权限 2:无权限
    private Integer currentUserRole;// 当前应用操作人角色。1：下单人，2：用车人。


    public ContractInfo toRes() {
        ContractInfo.Builder builder = ContractInfo.newBuilder()
                .setContractId(contractId)
                .setContractStatus(contractStatus == null ? 0 : contractStatus)
                .setPickUpStoreId(pickUpStoreId == null ? 0 : pickUpStoreId)
                .setPickUpStoreName(pickUpStoreName)
                .setReturnStoreId(returnStoreId == null ? 0 : returnStoreId)
                .setReturnStoreName(returnStoreName)
                .setPickUpDateTime(pickUpDateTime)
                .setReturnDateTime(returnDateTime)
                .setGoodsModelName(goodsModelName)
                .setVin(vin)
                .setVehicleNo(vehicleNo)
                .setIsSendVehService(isSendVehService == null ? 2 : isSendVehService)
                .setIsSmqcService(isSmqcService == null ? 2 : isSmqcService)
                .setUnReturn(unReturn == null ? 2 : unReturn)
                .setRealAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(realAmount))
                .setAppraiseOrder(appraiseOrder == null ? 0 : appraiseOrder)
                .setAppraiseCredit(appraiseCredit == null ? 0 : appraiseCredit)
                .setHasAdvanceAmount(hasAdvanceAmount == null ? 2 : hasAdvanceAmount)
                .setWaitPayAMount(BigDecimalUtil.toPlainStringNoDecimalPoint(waitPayAMount))
                .setAmountType(amountType == null ? 0 : amountType)
                .setBillReturnStatus(billReturnStatus == null ? 0 : billReturnStatus)
                .setRecoveryTaskState(recoveryTaskState)
                .setIsReturnFail(isReturnFail == null ? 2 : isReturnFail)
                .setIsSeparate(isSeparate == null ? 0 : isSeparate)
                .setBuyerFeeAuth(buyerFeeAuth == null ? 0 : buyerFeeAuth)
                .setCurrentUserRole(currentUserRole == null ? 0 : currentUserRole)
                ;


        List<ContractTravelInfo> contractTravelInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractTravelInfo)) {
            for (ContractTravelInfoBo bo : contractTravelInfo) {
                contractTravelInfoList.add(bo.toRes());
            }
        }

        if (!CollectionUtils.isEmpty(contractTravelInfoList)) {
            builder.addAllContractTravelInfo(contractTravelInfoList);
        }
        if (sendVehOrderInfo != null) {
            builder.setSendVehOrderInfo(sendVehOrderInfo.toRes());
        }
        if (pickUpVehOrderInfo != null) {
            builder.setPickUpVehOrderInfo(pickUpVehOrderInfo.toRes());
        }

        return builder.build();
    }

    public void parse(ContractInfoBo c,
                      GetStoreDetailInfoForAppBo planPickUpStoreInfo,
                      GetStoreDetailInfoForAppBo planReturnStoreInfo,
                      GetStoreDetailInfoForAppBo realPickUpStoreInfo,
                      GetStoreDetailInfoForAppBo realReturnStoreInfo) {
        // 取车门店
        if (c.getRealPickUpStoreId() != -1 && c.getRealPickUpStoreId() != 0) {
            setPickUpStoreId(c.getRealPickUpStoreId());
            setPickUpStoreName(realPickUpStoreInfo.getStoreName());
            if (c.getRealPickUpTime() != null) {
                setPickUpDateTime(DateUtil.dateToString(c.getRealPickUpTime(), DateUtil.DATE_TYPE4));
            }
        } else {
            setPickUpStoreId(c.getPlanPickUpStoreId());
            setPickUpStoreName(planPickUpStoreInfo.getStoreName());
            setPickUpDateTime(DateUtil.dateToString(c.getPlanPickUpDateTime(), DateUtil.DATE_TYPE4));
        }
        // 还车门店
        if (c.getRealReturnStoreId() != -1 && c.getRealReturnStoreId() != 0) {
            setReturnStoreId(c.getRealReturnStoreId());
            setReturnStoreName(realReturnStoreInfo.getStoreName());
            if (c.getRealReturnTime() != null) {
                setReturnDateTime(DateUtil.dateToString(c.getRealReturnTime(), DateUtil.DATE_TYPE4));
            }
        } else {
            setReturnStoreId(c.getPlanReturnStoreId());
            setReturnStoreName(planReturnStoreInfo.getStoreName());
            setReturnDateTime(DateUtil.dateToString(c.getPlanReturnDateTime(), DateUtil.DATE_TYPE4));
        }
    }
}
