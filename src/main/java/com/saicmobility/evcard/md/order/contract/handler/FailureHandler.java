package com.saicmobility.evcard.md.order.contract.handler;

import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.core.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 失败处理器
 * 处理各种失败事件的通用逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class FailureHandler extends AbstractEventHandler {

    @Override
    public String getHandlerName() {
        return "FailureHandler";
    }

    @Override
    protected EventDrivenStateMachine.ProcessResult doHandle(ProcessContext context) {
        log.info("开始执行失败处理: contractId={}, eventType={}", 
                context.getContractId(), context.getEventType());
        
        try {
            // 1. 记录失败信息
            logFailureInfo(context);
            
            // 2. 发送失败告警
            sendFailureAlert(context);
            
            // 3. 判断是否需要重试
            boolean needRetry = shouldRetry(context);
            
            // 4. 更新重试计数
            updateRetryCount(context);
            
            if (needRetry) {
                log.info("失败处理完成，标记为需要重试: contractId={}", context.getContractId());
                return EventDrivenStateMachine.ProcessResult.success("失败处理完成，等待重试");
            } else {
                log.info("失败处理完成，不再重试: contractId={}", context.getContractId());
                return EventDrivenStateMachine.ProcessResult.success("失败处理完成，流程终止");
            }
            
        } catch (Exception e) {
            log.error("失败处理异常: contractId={}", context.getContractId(), e);
            return EventDrivenStateMachine.ProcessResult.failure("FAILURE_HANDLER_ERROR", 
                    "失败处理异常: " + e.getMessage());
        }
    }

    /**
     * 记录失败信息
     */
    private void logFailureInfo(ProcessContext context) {
        String eventType = context.getEventType();
        String contractId = context.getContractId();
        
        log.error("流程执行失败: contractId={}, eventType={}, processInstanceId={}", 
                contractId, eventType, context.getProcessInstanceId());
        
        // 从事件数据中获取错误信息
        Object eventData = context.getEventData();
        if (eventData instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) eventData;
            String errorMessage = (String) dataMap.get("errorMessage");
            String errorCode = (String) dataMap.get("errorCode");
            
            log.error("失败详情: contractId={}, errorCode={}, errorMessage={}", 
                    contractId, errorCode, errorMessage);
        }
        
        // 这里可以记录到专门的失败日志表
        // 或者发送到监控系统
    }

    /**
     * 发送失败告警
     */
    private void sendFailureAlert(ProcessContext context) {
        try {
            String alertType = getAlertType(context.getEventType());
            
            log.warn("发送失败告警: contractId={}, alertType={}", 
                    context.getContractId(), alertType);
            
            // 这里可以调用告警服务
            // 例如：发送邮件、短信、钉钉、企业微信等
            
            // 模拟告警发送
            simulateAlertSend(context, alertType);
            
        } catch (Exception e) {
            log.error("发送失败告警异常: contractId={}", context.getContractId(), e);
            // 告警发送失败不影响主流程
        }
    }

    /**
     * 判断是否需要重试
     */
    private boolean shouldRetry(ProcessContext context) {
        // 从流程状态中获取重试次数
        int currentRetryCount = getCurrentRetryCount(context);
        int maxRetryCount = getMaxRetryCount(context);
        
        boolean needRetry = currentRetryCount < maxRetryCount;
        
        log.info("重试判断: contractId={}, currentRetryCount={}, maxRetryCount={}, needRetry={}", 
                context.getContractId(), currentRetryCount, maxRetryCount, needRetry);
        
        return needRetry;
    }

    /**
     * 更新重试计数
     */
    private void updateRetryCount(ProcessContext context) {
        try {
            int currentRetryCount = getCurrentRetryCount(context);
            int newRetryCount = currentRetryCount + 1;
            
            // 这里应该更新数据库中的重试计数
            // 暂时只记录日志
            log.info("更新重试计数: contractId={}, retryCount: {} -> {}", 
                    context.getContractId(), currentRetryCount, newRetryCount);
            
        } catch (Exception e) {
            log.error("更新重试计数失败: contractId={}", context.getContractId(), e);
        }
    }

    /**
     * 获取告警类型
     */
    private String getAlertType(String eventType) {
        switch (eventType) {
            case "VEHICLE_CHECK_FAILED":
                return "VEHICLE_CHECK_FAILURE_ALERT";
            case "SMS_SEND_FAILED":
                return "SMS_SEND_FAILURE_ALERT";
            case "SEAL_FAILED":
                return "SEAL_FAILURE_ALERT";
            default:
                return "PROCESS_FAILURE_ALERT";
        }
    }

    /**
     * 模拟告警发送
     */
    private void simulateAlertSend(ProcessContext context, String alertType) {
        log.warn("模拟发送告警: contractId={}, alertType={}", context.getContractId(), alertType);
        
        // 实际实现中可以调用：
        // - 邮件服务
        // - 短信服务
        // - 钉钉机器人
        // - 企业微信机器人
        // - 监控系统API等
    }

    /**
     * 获取当前重试次数
     */
    private int getCurrentRetryCount(ProcessContext context) {
        // 这里应该从数据库查询当前的重试次数
        // 暂时返回默认值
        return context.getAttribute("retryCount", 0);
    }

    /**
     * 获取最大重试次数
     */
    private int getMaxRetryCount(ProcessContext context) {
        // 这里可以根据不同的事件类型返回不同的最大重试次数
        String eventType = context.getEventType();
        switch (eventType) {
            case "VEHICLE_CHECK_FAILED":
                return 3; // 验车失败最多重试3次
            case "SMS_SEND_FAILED":
                return 5; // 短信发送失败最多重试5次
            case "SEAL_FAILED":
                return 2; // 盖章失败最多重试2次
            default:
                return 3; // 默认重试3次
        }
    }
}
