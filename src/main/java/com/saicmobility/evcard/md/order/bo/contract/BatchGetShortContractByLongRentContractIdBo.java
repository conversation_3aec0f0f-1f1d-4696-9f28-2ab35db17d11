package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.BatchGetShortContractByLongRentContractIdRes;
import com.saicmobility.evcard.md.mdorderservice.api.BatchGetShortContractByLongRentContractIdResItem;
import com.saicmobility.evcard.md.mdorderservice.api.ShortContractItem;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
@Data
public class BatchGetShortContractByLongRentContractIdBo {
    private int retCode; // 返回码
    private String retMsg; // 返回消息
    private Map<String, BatchGetShortContractByLongRentContractIdResItemBo> resMap; // key：longRentContractId、value：BatchGetShortContractByLongRentContractIdResItemBo

    public BatchGetShortContractByLongRentContractIdRes toRes() {
        Map<String, BatchGetShortContractByLongRentContractIdResItem> resultResMap = new HashMap<>();
        if (MapUtils.isNotEmpty(resMap)) {
            for (Map.Entry<String, BatchGetShortContractByLongRentContractIdResItemBo> entry : resMap.entrySet()) {
                String key = entry.getKey();
                BatchGetShortContractByLongRentContractIdResItemBo value = entry.getValue();
                List<ShortContractItem> shortContractItem = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(value.getShortContractItem())) {
                    for (ShortContractItemBo item : value.getShortContractItem()) {
                        shortContractItem.add(ShortContractItem.newBuilder()
                                .setContractId(item.getContractId())
                                .setContractStatusNew(item.getContractStatusNew())
                                .build());
                    }
                }
                resultResMap.put(key, BatchGetShortContractByLongRentContractIdResItem.newBuilder()
                        .addAllShortContractItem(shortContractItem)
                        .build());
            }
        }

        return BatchGetShortContractByLongRentContractIdRes.newBuilder()
                .setRetCode(retCode)
                .setRetMsg(retMsg)
                .putAllResMap(resultResMap)
                .build();
    }
}
