package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.TimeRange;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class TimeRangeBo {
    private String startDate; //yyyyMMdd
    private String endDate; //yyyyMMdd

    public static TimeRangeBo from(TimeRange item) {
        TimeRangeBo bo = new TimeRangeBo();
        bo.setStartDate(item.getStartDate());
        bo.setEndDate(item.getEndDate());
        return bo;
    }
}
