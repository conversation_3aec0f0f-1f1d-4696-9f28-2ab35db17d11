package com.saicmobility.evcard.md.order.bo.contractitem;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ContractItemBo {

    /**
     * 合同项类型 1门店订单(预付/续租) 2送车上门 3上门取车 4购卡 5履约购买基础服务 6修改订单 7修改车型 8渠道取消续租违约金  9渠道线上加购或升级服务费
     */
    private Integer itemType;
    /**
     * 合同项信息
     */
    private ContractItemInfoBo itemInfo;

    /**
     * 合同项配置
     */
    private ContractItemConfigBo itemConfig;


}
