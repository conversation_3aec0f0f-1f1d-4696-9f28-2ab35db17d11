package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.ContractTravelInfo;
import com.saicmobility.evcard.md.order.entity.CarRentalTravelInfo;

import lombok.Data;

/**
 * 行程单
 */
@Data
public class ContractTravelInfoBo {

    private String vehicleNo; // 车牌号

    private Integer taskOrderStatus; // 救援任务标识 7：分配中 1：已出发 2：已到达 3：已完成 4：上报 5：超时 6：取消 -1：无救援

    private Integer appraiseScore; // 是否评价救援服务 1:未评价 2:已经评价 -1:超时未评价

    private Integer orderTag; // 标签 1：风险 2：无风险

    public static ContractTravelInfoBo parse(CarRentalTravelInfo carRentalTravelInfo) {
        ContractTravelInfoBo bo = new ContractTravelInfoBo();
        bo.setVehicleNo(carRentalTravelInfo.getVehicleNo());
        return bo;
    }

    public ContractTravelInfo toRes() {
        return ContractTravelInfo.newBuilder()
                .setVehicleNo(vehicleNo)
                .setTaskOrderStatus(taskOrderStatus == null ? 0 : taskOrderStatus)
				.setAppraiseScore(appraiseScore == null ? -1 : appraiseScore) // 默认无救援
                .setOrderTag(orderTag == null ? 0 : orderTag)
                .build();
    }
}
