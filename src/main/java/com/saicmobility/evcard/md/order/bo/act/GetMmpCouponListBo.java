package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.ActivityCouponModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/22 22:35
 */
@Data
public class GetMmpCouponListBo {

    //  优惠券信息
    List<ActivityCouponModelBo> couponModelList;

    public static GetMmpCouponListBo from(List<ActivityCouponModel> couponModelListList) {
        GetMmpCouponListBo couponListBo = new GetMmpCouponListBo();
        List<ActivityCouponModelBo> couponModelList = new ArrayList<>();
        couponModelListList.forEach(p -> {
            ActivityCouponModelBo bo = ActivityCouponModelBo.from(p);
            couponModelList.add(bo);
        });
        couponListBo.setCouponModelList(couponModelList);
        return couponListBo;
    }
}
