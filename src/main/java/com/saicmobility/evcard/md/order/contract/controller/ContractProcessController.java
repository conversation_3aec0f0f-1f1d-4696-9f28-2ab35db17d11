package com.saicmobility.evcard.md.order.contract.controller;

import com.saicmobility.evcard.md.order.contract.dto.ApiResponse;
import com.saicmobility.evcard.md.order.contract.dto.ContractProcessContext;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessLog;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.enums.TriggerType;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessDataService;
import com.saicmobility.evcard.md.order.contract.service.ProcessInstanceManager;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同处理流程管理接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@RestController
@RequestMapping("/api/contract/process")
public class ContractProcessController {

    @Autowired
    private ContractProcessService contractProcessService;

    @Autowired
    private ContractProcessDataService dataService;

    @Autowired
    private ProcessInstanceManager processInstanceManager;

    /**
     * 手动启动合同处理流程
     * 
     * @param context 流程上下文
     * @return 处理结果
     */
    @PostMapping("/start")
    public ApiResponse<String> startProcess(@RequestBody ContractProcessContext context) {
        try {
            log.info("手动启动合同处理流程, contractId: {}", context.getContractId());

            boolean success = contractProcessService.startProcess(context);

            if (success) {
                return ApiResponse.success("合同处理流程启动成功");
            } else {
                return ApiResponse.error("合同处理流程启动失败");
            }

        } catch (Exception e) {
            log.error("启动合同处理流程异常, contractId: {}", context.getContractId(), e);
            return ApiResponse.error("启动合同处理流程异常: " + e.getMessage());
        }
    }

    /**
     * 查询合同处理状态
     * 
     * @param contractId 合同ID
     * @return 处理状态
     */
    @GetMapping("/state/{contractId}")
    public ApiResponse<ContractProcessState> getProcessState(@PathVariable String contractId) {
        try {
            ContractProcessState state = contractProcessService.getProcessState(contractId);

            if (state != null) {
                return ApiResponse.success(state);
            } else {
                return ApiResponse.error("未找到合同处理状态");
            }

        } catch (Exception e) {
            log.error("查询合同处理状态异常, contractId: {}", contractId, e);
            return ApiResponse.error("查询合同处理状态异常: " + e.getMessage());
        }
    }

    /**
     * 查询合同处理日志
     * 
     * @param contractId 合同ID
     * @return 处理日志列表
     */
    @GetMapping("/logs/{contractId}")
    public ApiResponse<List<ContractProcessLog>> getProcessLogs(@PathVariable String contractId) {
        try {
            List<ContractProcessLog> logs = dataService.selectLogsByContractId(contractId);
            return ApiResponse.success(logs);

        } catch (Exception e) {
            log.error("查询合同处理日志异常, contractId: {}", contractId, e);
            return ApiResponse.error("查询合同处理日志异常: " + e.getMessage());
        }
    }

    /**
     * 手动修改合同处理状态
     * 
     * @param contractId 合同ID
     * @param targetState 目标状态
     * @param operator 操作人
     * @return 处理结果
     */
    @PostMapping("/change-state")
    public ApiResponse<String> changeState(@RequestParam String contractId,
                                           @RequestParam String targetState,
                                           @RequestParam String operator) {
        try {
            log.info("手动修改合同处理状态, contractId: {}, targetState: {}, operator: {}",
                    contractId, targetState, operator);

            boolean success = contractProcessService.changeState(contractId, targetState, operator);

            if (success) {
                return ApiResponse.success("状态修改成功");
            } else {
                return ApiResponse.error("状态修改失败");
            }

        } catch (Exception e) {
            log.error("修改合同处理状态异常, contractId: {}", contractId, e);
            return ApiResponse.error("修改合同处理状态异常: " + e.getMessage());
        }
    }

    /**
     * 手动重试合同处理流程
     * 
     * @param contractId 合同ID
     * @return 处理结果
     */
    @PostMapping("/retry/{contractId}")
    public ApiResponse<String> retryProcess(@PathVariable String contractId) {
        try {
            log.info("手动重试合同处理流程, contractId: {}", contractId);

            boolean success = contractProcessService.retryProcess(contractId);

            if (success) {
                return ApiResponse.success("重试成功");
            } else {
                return ApiResponse.error("重试失败");
            }

        } catch (Exception e) {
            log.error("重试合同处理流程异常, contractId: {}", contractId, e);
            return ApiResponse.error("重试合同处理流程异常: " + e.getMessage());
        }
    }

    /**
     * 查询需要重试的记录
     *
     * @return 需要重试的记录列表
     */
    @GetMapping("/retryable-records")
    public ApiResponse<List<ContractProcessState>> getRetryableRecords() {
        try {
            List<ContractProcessState> records = contractProcessService.getRetryableRecords();
            return ApiResponse.success(records);

        } catch (Exception e) {
            log.error("查询需要重试的记录异常", e);
            return ApiResponse.error("查询需要重试的记录异常: " + e.getMessage());
        }
    }

    /**
     * 查询超时的记录
     *
     * @return 超时的记录列表
     */
    @GetMapping("/timeout-records")
    public ApiResponse<List<ContractProcessState>> getTimeoutRecords() {
        try {
            List<ContractProcessState> records = contractProcessService.getTimeoutRecords();
            return ApiResponse.success(records);

        } catch (Exception e) {
            log.error("查询超时记录异常", e);
            return ApiResponse.error("查询超时记录异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发自动恢复
     *
     * @return 处理结果
     */
    @PostMapping("/auto-recover")
    public ApiResponse<String> autoRecover() {
        try {
            log.info("手动触发自动恢复");

            int recoveredCount = contractProcessService.autoRecover();

            return ApiResponse.success("自动恢复完成，处理记录数: " + recoveredCount);

        } catch (Exception e) {
            log.error("自动恢复异常", e);
            return ApiResponse.error("自动恢复异常: " + e.getMessage());
        }
    }

    /**
     * 人工干预处理
     *
     * @param contractId 合同ID
     * @param targetState 目标状态
     * @param operator 操作人
     * @param reason 干预原因
     * @return 处理结果
     */
    @PostMapping("/manual-intervention")
    public ApiResponse<String> manualIntervention(@RequestParam String contractId,
                                                  @RequestParam String targetState,
                                                  @RequestParam String operator,
                                                  @RequestParam(required = false) String reason) {
        try {
            log.info("人工干预处理, contractId: {}, targetState: {}, operator: {}, reason: {}",
                    contractId, targetState, operator, reason);

            boolean success = contractProcessService.manualIntervention(contractId, targetState, operator, reason);

            if (success) {
                return ApiResponse.success("人工干预处理成功");
            } else {
                return ApiResponse.error("人工干预处理失败");
            }

        } catch (Exception e) {
            log.error("人工干预处理异常, contractId: {}", contractId, e);
            return ApiResponse.error("人工干预处理异常: " + e.getMessage());
        }
    }

    // ==================== 新增的发车收车触发接口 ====================

    /**
     * 发车触发合同处理流程
     *
     * @param context 流程上下文
     * @return 处理结果
     */
    @PostMapping("/start-rental")
    public ApiResponse<String> startRental(@RequestBody ContractProcessContext context) {
        try {
            log.info("发车触发合同处理流程, contractId: {}, userId: {}",
                    context.getContractId(), context.getUserId());

            // 设置触发类型为发车
            context.setTriggerType(TriggerType.START_RENTAL.getCode());

            boolean success = contractProcessService.startProcess(context);

            if (success) {
                return ApiResponse.success("发车流程启动成功", context.getProcessInstanceId());
            } else {
                return ApiResponse.error("发车流程启动失败");
            }

        } catch (Exception e) {
            log.error("发车触发流程异常, contractId: {}", context.getContractId(), e);
            return ApiResponse.error("发车触发流程异常: " + e.getMessage());
        }
    }

    /**
     * 收车触发合同处理流程
     *
     * @param context 流程上下文
     * @return 处理结果
     */
    @PostMapping("/end-rental")
    public ApiResponse<String> endRental(@RequestBody ContractProcessContext context) {
        try {
            log.info("收车触发合同处理流程, contractId: {}, userId: {}",
                    context.getContractId(), context.getUserId());

            // 设置触发类型为收车
            context.setTriggerType(TriggerType.END_RENTAL.getCode());

            boolean success = contractProcessService.startProcess(context);

            if (success) {
                return ApiResponse.success("收车流程启动成功", context.getProcessInstanceId());
            } else {
                return ApiResponse.error("收车流程启动失败");
            }

        } catch (Exception e) {
            log.error("收车触发流程异常, contractId: {}", context.getContractId(), e);
            return ApiResponse.error("收车触发流程异常: " + e.getMessage());
        }
    }

    /**
     * 查询合同的所有流程实例
     *
     * @param contractId 合同ID
     * @return 流程实例列表
     */
    @GetMapping("/instances/{contractId}")
    public ApiResponse<Map<String, Object>> getProcessInstances(@PathVariable String contractId) {
        try {
            log.info("查询合同流程实例, contractId: {}", contractId);

            Map<String, Object> result = new HashMap<>();

            // 查询发车流程实例
            ContractProcessState startInstance = processInstanceManager.getProcessInstance(contractId, TriggerType.START_RENTAL.getCode());
            if (startInstance != null) {
                Map<String, Object> startRentalInfo = new HashMap<>();
                startRentalInfo.put("processInstanceId", startInstance.getProcessInstanceId());
                startRentalInfo.put("currentState", startInstance.getCurrentState());
                startRentalInfo.put("triggerType", startInstance.getTriggerType());
                startRentalInfo.put("createdTime", startInstance.getCreateTime());
                startRentalInfo.put("updatedTime", startInstance.getUpdateTime());
                result.put("startRental", startRentalInfo);
            }

            // 查询收车流程实例
            ContractProcessState endInstance = processInstanceManager.getProcessInstance(contractId, TriggerType.END_RENTAL.getCode());
            if (endInstance != null) {
                Map<String, Object> endRentalInfo = new HashMap<>();
                endRentalInfo.put("processInstanceId", endInstance.getProcessInstanceId());
                endRentalInfo.put("currentState", endInstance.getCurrentState());
                endRentalInfo.put("triggerType", endInstance.getTriggerType());
                endRentalInfo.put("createdTime", endInstance.getCreateTime());
                endRentalInfo.put("updatedTime", endInstance.getUpdateTime());
                result.put("endRental", endRentalInfo);
            }

            return ApiResponse.success("查询成功", result);

        } catch (Exception e) {
            log.error("查询流程实例异常, contractId: {}", contractId, e);
            return ApiResponse.error("查询流程实例异常: " + e.getMessage());
        }
    }

    /**
     * 根据流程实例ID查询状态
     *
     * @param processInstanceId 流程实例ID
     * @return 流程状态
     */
    @GetMapping("/instance/{processInstanceId}")
    public ApiResponse<ContractProcessState> getProcessInstanceState(@PathVariable String processInstanceId) {
        try {
            log.info("查询流程实例状态, processInstanceId: {}", processInstanceId);

            ContractProcessState processState = dataService.selectByProcessInstanceId(processInstanceId);

            if (processState != null) {
                return ApiResponse.success("查询成功", processState);
            } else {
                return ApiResponse.error("未找到流程实例");
            }

        } catch (Exception e) {
            log.error("查询流程实例状态异常, processInstanceId: {}", processInstanceId, e);
            return ApiResponse.error("查询流程实例状态异常: " + e.getMessage());
        }
    }

    /**
     * 根据流程实例ID查询日志
     *
     * @param processInstanceId 流程实例ID
     * @return 日志列表
     */
    @GetMapping("/instance/{processInstanceId}/logs")
    public ApiResponse<List<ContractProcessLog>> getProcessInstanceLogs(@PathVariable String processInstanceId) {
        try {
            log.info("查询流程实例日志, processInstanceId: {}", processInstanceId);

            List<ContractProcessLog> logs = dataService.selectLogsByProcessInstanceId(processInstanceId);
            return ApiResponse.success("查询成功", logs);

        } catch (Exception e) {
            log.error("查询流程实例日志异常, processInstanceId: {}", processInstanceId, e);
            return ApiResponse.error("查询流程实例日志异常: " + e.getMessage());
        }
    }
}
