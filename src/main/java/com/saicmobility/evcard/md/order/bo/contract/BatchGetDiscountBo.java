package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.BatchGetDiscountRes;
import com.saicmobility.evcard.md.mdorderservice.api.BatchGetDiscountResItem;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
public class BatchGetDiscountBo {
    private int retCode; // 返回码
    private String retMsg; // 返回消息
    private Map<String, BatchGetDiscountResItemBo> resMap; // key：storeVehicleModelId、value：BatchGetDiscountResItemBo

    public BatchGetDiscountRes toRes() {
        Map<String, BatchGetDiscountResItem> resultResMap = new HashMap<>();
        if (MapUtils.isNotEmpty(resMap)) {
            for (Map.Entry<String, BatchGetDiscountResItemBo> entry : resMap.entrySet()) {
                String key = entry.getKey();
                BatchGetDiscountResItemBo item = entry.getValue();
                resultResMap.put(key, BatchGetDiscountResItem.newBuilder()
                        .setStoreVehicleModelId(item.getStoreVehicleModelId())
                        .setSelfOperatedDiscountAmount(item.getSelfOperatedDiscountAmount())
                        .setSelfOperatedDiscountName(item.getSelfOperatedDiscountName())
                        .setCouponDeductionAmount(item.getCouponDeductionAmount())
                        .setAverageRent(item.getAverageRent())
                        .setOrgAverageRent(item.getOrgAverageRent())
                        .setAfterTotalAmount(item.getAfterTotalAmount())
                        .setBeforeTotalAmount(item.getBeforeTotalAmount())
                        .build());
            }
        }

        return BatchGetDiscountRes.newBuilder()
                .setRetCode(retCode)
                .setRetMsg(retMsg)
                .putAllResMap(resultResMap)
                .build();
    }
}
