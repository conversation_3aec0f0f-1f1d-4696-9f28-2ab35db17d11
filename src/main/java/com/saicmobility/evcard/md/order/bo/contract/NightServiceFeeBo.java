package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.NightServiceFee;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class NightServiceFeeBo {

    // 30:还车-夜间服务费
    private int amountType;

    // 金额
    private BigDecimal amount;

    public NightServiceFee toRes() {
        NightServiceFee.Builder builder = NightServiceFee.newBuilder();
        builder.setAmountType(amountType);
        if (amount != null) {
            builder.setAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(amount));
        }
        return builder.build();
    }
}
