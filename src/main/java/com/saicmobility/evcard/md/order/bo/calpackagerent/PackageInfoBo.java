package com.saicmobility.evcard.md.order.bo.calpackagerent;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.saicmobility.evcard.md.order.enums.IsEarlyBirdEnum;
import org.apache.commons.lang3.StringUtils;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.PackageInfo;
import com.saicmobility.evcard.md.order.bo.external.rentpackage.PackageDetailInfoBo;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;

import lombok.Data;

/**
 * 套餐配置接口
 */
@Data
public class PackageInfoBo {

    private Long packageId;

    /** 套餐类型 1标准套餐 2套餐 */
    private Integer packageType;

    private String packageName;

    /** 套餐内天数 */
    private Integer daysNumber;

    /** 套餐内总价 */
    private String totalPrice;

    private String packageDesc;

    /** 套餐日均价格  天数总价/天数 */
    private String packageAverageAmount;

    /** 是否可用早鸟套餐 1 是 2 否 */
    private Integer earlyBirdFlag;

    /** 使用早鸟套餐后的总价 */
    private String earlyBirdTotalPrice;

    /** 使用早鸟套餐后的日均价格 使用早鸟套餐后的总价/天数 */
    private String earlyBirdPackageAverageAmount;

    /** 早鸟套餐的开始时间，格式：yyyyMMdd */
    private String earlyBirdStartDate;

    /** 早鸟套餐的结束时间，格式：yyyyMMdd */
    private String earlyBirdEndDate;

    /** 距早鸟活动结束还剩几天 */
    private Integer earlyBirdDaysLeft;

    /** 升级为早鸟模式，立省xx元 */
    private String earlyBirdSaveMoney;

    public static PackageInfoBo from(PackageDetailInfoBo packageDetailInfoBo) {
        PackageInfoBo packageInfoBo = new PackageInfoBo();
        packageInfoBo.setPackageId(packageDetailInfoBo.getId());
        packageInfoBo.setPackageType(BusinessConstants.PACKAGE_TYPE_PACK);
        packageInfoBo.setPackageName(packageDetailInfoBo.getPackageName());
        packageInfoBo.setDaysNumber(packageDetailInfoBo.getDaysNumber());
        packageInfoBo.setTotalPrice(packageDetailInfoBo.getTotalPrice());
        packageInfoBo.setPackageDesc(packageDetailInfoBo.getDesc());
        packageInfoBo.setPackageAverageAmount(new BigDecimal(packageDetailInfoBo.getTotalPrice()).divide(new BigDecimal(packageDetailInfoBo.getDaysNumber()), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
        packageInfoBo.setEarlyBirdFlag(packageDetailInfoBo.getEarlyFlag());

        if(packageDetailInfoBo.getEarlyFlag() == IsEarlyBirdEnum.YES.getValue()){
            packageInfoBo.setEarlyBirdTotalPrice(packageDetailInfoBo.getEarlyPrice());
            packageInfoBo.setEarlyBirdPackageAverageAmount(new BigDecimal(packageDetailInfoBo.getEarlyPrice()).divide(new BigDecimal(packageDetailInfoBo.getDaysNumber()), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
            if (StringUtils.isNotBlank(packageDetailInfoBo.getEarlyStartDate())) {
                packageInfoBo
                        .setEarlyBirdStartDate(LocalDate.parse(packageDetailInfoBo.getEarlyStartDate(), DateUtil.DATE_TYPE1)
                                .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            }
            if (StringUtils.isNotBlank(packageDetailInfoBo.getEarlyEndDate())) {
                packageInfoBo
                        .setEarlyBirdEndDate(LocalDate.parse(packageDetailInfoBo.getEarlyEndDate(), DateUtil.DATE_TYPE1)
                                .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                LocalDate earlyEndLocalDate = LocalDate.parse(packageDetailInfoBo.getEarlyEndDate(), DateUtil.DATE_TYPE1);
                LocalDate nowLocalDate = LocalDate.now();
                int days = (int)(earlyEndLocalDate.toEpochDay() - nowLocalDate.toEpochDay());
                int earlyBirdDaysLeft = days < 0 ? 0 : (days + 1);
                packageInfoBo.setEarlyBirdDaysLeft(earlyBirdDaysLeft);
                // 如果早鸟活动已结束，那么设置标志为不能升级为早鸟
                if (earlyBirdDaysLeft == 0) {
                    packageInfoBo.setEarlyBirdFlag(IsEarlyBirdEnum.NO.getValue());
                }
            }
            packageInfoBo.setEarlyBirdSaveMoney(new BigDecimal(packageDetailInfoBo.getTotalPrice()).subtract(new BigDecimal(packageDetailInfoBo.getEarlyPrice())).setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
        }

        return packageInfoBo;
    }

    public PackageInfo toRes(){
        return PackageInfo.newBuilder()
                .setPackageId(getPackageId() == null ? BusinessConstants.PACKAGE_TYPE_PACK : getPackageId())
                .setPackageType(getPackageType() == null ? 0 : getPackageType())
                .setPackageName(getPackageName())
                .setDaysNumber(getDaysNumber() == null ? 0 : getDaysNumber())
                .setTotalPrice(getTotalPrice())
                .setPackageDesc(getPackageDesc())
                .setPackageAverageAmount(getPackageAverageAmount())
                .setEarlyBirdFlag(getEarlyBirdFlag() == null ? 0 : getEarlyBirdFlag())
                .setEarlyBirdTotalPrice(getEarlyBirdTotalPrice())
                .setEarlyBirdPackageAverageAmount(getEarlyBirdPackageAverageAmount())
                .setEarlyBirdStartDate(getEarlyBirdStartDate())
                .setEarlyBirdEndDate(getEarlyBirdEndDate())
				.setEarlyBirdDaysLeft(getEarlyBirdDaysLeft() == null ? 0 : getEarlyBirdDaysLeft())
                .setEarlyBirdSaveMoney(getEarlyBirdSaveMoney())
                .build();
    }
}
