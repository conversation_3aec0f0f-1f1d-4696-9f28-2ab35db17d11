package com.saicmobility.evcard.md.order.bo.drivingrecord;

import com.saicmobility.evcard.md.mdorderservice.api.PreCardInfo;
import com.saicmobility.evcard.md.order.bo.external.user.GetMemberCardBo;

import lombok.Data;

/**
 * 预付选择的会员卡信息
 */
@Data
public class PreCardInfoBo {

    //  会员卡编号
    private String userCardNo;

    //  会员卡名称
    private String cardName;

    //  折扣，1~99整数
    private Integer discount;

    //  会员卡类别：1企业会员卡 2企业个人卡 3付费会员卡
    private Integer cardGroup;

    //  会员卡状态：0未生效 1生效中 3已过期(非实时更新) 4已禁用 5作废
    private Integer cardStatus;

    //  卡片背景图(全路径)
    private String backUrl;

    public static PreCardInfoBo from(GetMemberCardBo memberCard) {
        PreCardInfoBo bo = new PreCardInfoBo();
        bo.setUserCardNo(memberCard.getUserCardNo() + "");
        bo.setCardName(memberCard.getCardName());
        bo.setDiscount(memberCard.getDiscountRate());
        bo.setCardGroup(memberCard.getCardGroup());
        bo.setCardStatus(memberCard.getCardStatus());
        bo.setBackUrl(memberCard.getBackUrl());
        return bo;
    }

    public PreCardInfo toRes() {
        return PreCardInfo.newBuilder()
                .setUserCardNo(userCardNo)
                .setCardName(cardName)
                .setDiscount(discount == null ? 0 : discount)
                .setCardGroup(cardGroup)
                .setCardStatus(cardStatus)
                .setBackUrl(backUrl)
                .build();
    }
}
