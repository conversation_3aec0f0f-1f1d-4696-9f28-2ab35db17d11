package com.saicmobility.evcard.md.order.contract.handler;

import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.core.ProcessContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 流程完成处理器
 * 处理流程完成后的收尾工作
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class ProcessCompleteHandler extends AbstractEventHandler {

    @Override
    public String getHandlerName() {
        return "ProcessCompleteHandler";
    }

    @Override
    protected EventDrivenStateMachine.ProcessResult doHandle(ProcessContext context) {
        log.info("开始执行流程完成处理: contractId={}, triggerType={}", 
                context.getContractId(), context.getTriggerType());
        
        try {
            // 1. 记录流程完成日志
            logProcessCompletion(context);
            
            // 2. 发送完成通知
            sendCompletionNotification(context);
            
            // 3. 清理临时数据
            cleanupTempData(context);
            
            // 4. 更新业务状态
            updateBusinessStatus(context);
            
            log.info("流程完成处理成功: contractId={}", context.getContractId());
            
            return EventDrivenStateMachine.ProcessResult.success("流程完成处理成功");
            
        } catch (Exception e) {
            log.error("流程完成处理异常: contractId={}", context.getContractId(), e);
            return EventDrivenStateMachine.ProcessResult.failure("PROCESS_COMPLETE_ERROR", 
                    "流程完成处理异常: " + e.getMessage());
        }
    }

    /**
     * 记录流程完成日志
     */
    private void logProcessCompletion(ProcessContext context) {
        log.info("流程完成记录: contractId={}, processInstanceId={}, triggerType={}, " +
                "startTime={}, endTime={}", 
                context.getContractId(), 
                context.getProcessInstanceId(),
                context.getTriggerType(),
                context.getCreateTime(),
                java.time.LocalDateTime.now());
        
        // 这里可以记录到专门的完成日志表
        // 或者发送到监控系统
    }

    /**
     * 发送完成通知
     */
    private void sendCompletionNotification(ProcessContext context) {
        try {
            // 根据触发类型发送不同的通知
            String notificationType = getNotificationType(context.getTriggerType());
            
            log.info("发送完成通知: contractId={}, notificationType={}", 
                    context.getContractId(), notificationType);
            
            // 这里可以调用通知服务
            // 例如：发送邮件、短信、推送等
            
            // 模拟通知发送
            simulateNotificationSend(context, notificationType);
            
        } catch (Exception e) {
            log.warn("发送完成通知失败: contractId={}", context.getContractId(), e);
            // 通知失败不影响主流程
        }
    }

    /**
     * 清理临时数据
     */
    private void cleanupTempData(ProcessContext context) {
        try {
            log.info("清理临时数据: contractId={}", context.getContractId());
            
            // 清理上下文中的临时属性
            context.removeAttribute("thirdPartyRequestId");
            context.removeAttribute("smsRequestId");
            context.removeAttribute("sealRequestId");
            
            // 这里可以清理其他临时文件、缓存等
            
        } catch (Exception e) {
            log.warn("清理临时数据失败: contractId={}", context.getContractId(), e);
            // 清理失败不影响主流程
        }
    }

    /**
     * 更新业务状态
     */
    private void updateBusinessStatus(ProcessContext context) {
        try {
            log.info("更新业务状态: contractId={}, triggerType={}", 
                    context.getContractId(), context.getTriggerType());
            
            // 根据触发类型更新不同的业务状态
            if ("START_RENTAL".equals(context.getTriggerType())) {
                // 发车流程完成，更新合同状态为"生效中"
                updateContractStatus(context.getContractId(), "ACTIVE");
            } else if ("END_RENTAL".equals(context.getTriggerType())) {
                // 收车流程完成，更新合同状态为"已完成"
                updateContractStatus(context.getContractId(), "COMPLETED");
            }
            
        } catch (Exception e) {
            log.error("更新业务状态失败: contractId={}", context.getContractId(), e);
            // 业务状态更新失败需要记录，但不影响流程完成
        }
    }

    /**
     * 获取通知类型
     */
    private String getNotificationType(String triggerType) {
        switch (triggerType) {
            case "START_RENTAL":
                return "START_RENTAL_COMPLETE";
            case "END_RENTAL":
                return "END_RENTAL_COMPLETE";
            default:
                return "PROCESS_COMPLETE";
        }
    }

    /**
     * 模拟通知发送
     */
    private void simulateNotificationSend(ProcessContext context, String notificationType) {
        // 模拟发送通知
        log.info("模拟发送通知: contractId={}, type={}", context.getContractId(), notificationType);
        
        // 实际实现中可以调用：
        // - 邮件服务
        // - 短信服务  
        // - 推送服务
        // - 消息队列
        // - Webhook等
    }

    /**
     * 更新合同状态
     */
    private void updateContractStatus(String contractId, String status) {
        // 模拟更新合同状态
        log.info("更新合同状态: contractId={}, status={}", contractId, status);
        
        // 实际实现中这里应该调用合同服务的API
        // 或者直接更新数据库
    }
}
