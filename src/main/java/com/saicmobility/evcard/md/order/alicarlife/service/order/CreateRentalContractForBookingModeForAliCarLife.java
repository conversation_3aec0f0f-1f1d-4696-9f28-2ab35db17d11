package com.saicmobility.evcard.md.order.alicarlife.service.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.DateUtil;
import com.extracme.framework.core.dto.UserDTO;
import com.google.common.collect.Lists;
import com.saicmobility.evcard.md.mdactservice.api.*;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.md.mdgoodsservice.api.*;
import com.saicmobility.evcard.md.mdpayservice.api.MdPayService;
import com.saicmobility.evcard.md.mdstockservice.api.CheckVehicleModelStockReq;
import com.saicmobility.evcard.md.mdstockservice.api.MdStockService;
import com.saicmobility.evcard.md.mdstoreservice.api.*;
import com.saicmobility.evcard.md.mduserservice.api.*;
import com.saicmobility.evcard.md.order.bo.CreateRentalContractOut;
import com.saicmobility.evcard.md.order.bo.act.ActSignUpDetailInfoBo;
import com.saicmobility.evcard.md.order.bo.act.CouponViewBo;
import com.saicmobility.evcard.md.order.bo.external.accompanyingcard.CalAccompanyingCardInfoBo;
import com.saicmobility.evcard.md.order.bo.external.billing.*;
import com.saicmobility.evcard.md.order.bo.external.user.GetUserBasicInfoBo;
import com.saicmobility.evcard.md.order.bo.external.user.PurchaseCardBo;
import com.saicmobility.evcard.md.order.configuration.DailyBookCancelCountConfig;
import com.saicmobility.evcard.md.order.configuration.GlobalConfig;
import com.saicmobility.evcard.md.order.constants.*;
import com.saicmobility.evcard.md.order.dto.CreateContractContext;
import com.saicmobility.evcard.md.order.dto.CreateContractInputDto;
import com.saicmobility.evcard.md.order.dto.CreateRentalContractInput;
import com.saicmobility.evcard.md.order.dto.contractitem.*;
import com.saicmobility.evcard.md.order.dto.external.billing.ActivityRulerDto;
import com.saicmobility.evcard.md.order.dto.external.billing.CalPaymentDto;
import com.saicmobility.evcard.md.order.dto.external.billing.CalculatePrePayFeeDto;
import com.saicmobility.evcard.md.order.dto.external.billing.SelfOperatedActivityDto;
import com.saicmobility.evcard.md.order.dto.external.pay.CreatePayOrderDto;
import com.saicmobility.evcard.md.order.dto.external.user.PlatformChannelBo;
import com.saicmobility.evcard.md.order.dto.external.user.PurchaseCardDto;
import com.saicmobility.evcard.md.order.dto.goodsorder.SaveGoodsDetailDto;
import com.saicmobility.evcard.md.order.entity.*;
import com.saicmobility.evcard.md.order.enums.*;
import com.saicmobility.evcard.md.order.exception.BookVehicleException;
import com.saicmobility.evcard.md.order.iservice.*;
import com.saicmobility.evcard.md.order.mq.MQPushService;
import com.saicmobility.evcard.md.order.service.*;
import com.saicmobility.evcard.md.order.service.external.ExternalSystemFacade;
import com.saicmobility.evcard.md.order.service.order.ContractTransactions;
import com.saicmobility.evcard.md.order.service.order.CreateRentalContractForBookingMode;
import com.saicmobility.evcard.md.order.support.YiGuanTrackSupport;
import com.saicmobility.evcard.md.order.utils.*;
import krpc.rpc.core.ClientContext;
import krpc.rpc.impl.TracablePool;
import krpc.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 普通预约日租订单
 *
 * <AUTHOR>
 * @since 2022/5/23 15:39
 */
@Slf4j
@Component
public class CreateRentalContractForBookingModeForAliCarLife {

    @Autowired
    DailyBookCancelCountConfig dailyBookCancelCountConfig;

    /**
     * 服务分隔符
     */
    public static final String ID_SPILT_1 = ",";

    @Autowired
    ICarRentalContractInfoService rentalContractInfoService;
    @Autowired
    ICarRentalContractConfigService rentalContractConfigService;
    @Autowired
    IContractItemInfoService contractItemInfoService;
    @Autowired
    IContractItemConfigService contractItemConfigService;
    @Autowired
    ICarRentalContractOperationLogService contractOperationLogService;
    @Autowired
    IGoodsOrderInfoService goodsOrderInfoService;
    @Autowired
    IGoodsOrderDetailsService goodsOrderDetailsService;
    @Autowired
    IGoodsOrderOperationRecordService goodsOperationRecordService;
    @Autowired
    MdActService mdActService;
    @Autowired
    MdPayService mdPayService;
    @Autowired
    ExternalSystemFacade externalSystemFacade;
    @Autowired
    ContractItemService contractItemService;
    @Autowired
    GoodsOrderService goodsOrderService;
    @Autowired
    ITaskService taskService;

    @Autowired
    MdDataProxy mdDataProxy;
    @Autowired
    ICarRentalContractInfoService carRentalContractInfoService;
    @Autowired
    MdUserService mdUserService;
    @Autowired
    MdStoreService mdStoreService;
    @Autowired
    MdGoodsService mdGoodsService;
    @Autowired
    MdStockService mdStockService;

    @Autowired
    private CouponService couponService;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    ContractTransactions contractTransactions;

    @Autowired
    MessagePushService messagePushService;

    @Autowired
    @Qualifier("mqWritePool")
    TracablePool mqWritePool;

    @Autowired
    @Qualifier("trackPool")
    TracablePool trackPool;

    @Autowired
    @Qualifier("alipaySyncPool")
    TracablePool alipaySyncPool;

    @Autowired
    StoreService storeService;

    @Autowired
    private PackageService packageService;

    @Value("${issueDateV52}")
    private String issueDateV52;

    @Autowired
    private AliPayService aliPayService;

    @Autowired
    private AliPayCarLifeService aliPayCarLifeService;

    @Autowired
    @Qualifier("alipayCarLifeSyncPool")
    TracablePool alipayCarLifeSyncPool;

    @Resource
    private YiGuanTrackSupport yiGuanTrackSupport;

    @Autowired
    private ContractService contractService;

    @Autowired
    private MQPushService mqPushService;

    @Autowired
    private GlobalConfig globalConfig;

    @Autowired
    private CreateRentalContractForBookingMode createRentalContractForBookingMode;

    @Resource
    private IContractSeparateAccountsConfigService contractSeparateAccountsConfigService;

    @Resource
    private BillResultCompareService billResultCompareService;

    @Resource
    private BillParamConvertService billParamConvertService;

    public void process(CreateContractContext ctx) {

        CreateRentalContractInput orderVehicleInputDto = ctx.input;
        CreateRentalContractOut orderVehicleOutputDTO = ctx.output;

        try {
            // 1. 入参必要性检查
            checkInputParam(ctx);

            // 2. 约车条件检查
            checkOrderCondition1(ctx);
            checkOrderCondition2(ctx);

            // 3. 创建订单
            createOrder(ctx);

            // 4. 订单收尾工作(推送、通知其他业务、埋点) 可自行扩展
            afterProcess(ctx);

        } catch (Exception e) {

            if (e instanceof BookVehicleException) {
                log.error(e.getMessage(), e);
                log.warn("预约失败,原因：" + e.getMessage() + " |  入参={}", orderVehicleInputDto);
                orderVehicleOutputDTO.setCodeAndMsg(((BookVehicleException) e).getCode(), e.getMessage());
            } else {
                log.error("预约失败,原因：" + e.getMessage(), e);
                orderVehicleOutputDTO.setCodeAndMsg(ExceptionEnum.CREATE_ORDER_EXCEPTION);
            }

            // 事务未成功取消购卡
            if (ctx.purchaseCardBo != null) {
                CancelPurchaseReq req = CancelPurchaseReq.newBuilder().setPurchaseId(ctx.purchaseCardBo.getPurchaseId()).setCancelType(2).build();
                ClientContext.setRetrier(10, 5, 10, 30, 60); // 仅针对此方法后的第一次rpc调用
                mdUserService.cancelPurchase(req);
            }
        }

        // 异步处理埋点
        trackPool.post(() -> yiGuanTrackSupport.createOrderTrack(ctx,"submit_booking"));
    }



    void checkInputParam(CreateContractContext ctx) throws BookVehicleException {

        CreateContractInputDto orderVehicleInputDTO = ctx.input;

        String mid = orderVehicleInputDTO.getBuyMid();
        Long goodsModelId = orderVehicleInputDTO.getGoodsModelId();
        String planPickUpDateTime = orderVehicleInputDTO.getPlanPickUpDateTime();
        Long planPickUpStoreId = orderVehicleInputDTO.getPlanPickUpStoreId();
        String planReturnDateTime = orderVehicleInputDTO.getPlanReturnDateTime();
        Long planReturnStoreId = orderVehicleInputDTO.getPlanReturnStoreId();
        Long packageId = orderVehicleInputDTO.getPackageId();
        //  判断入参
        if (mid == null ||
                goodsModelId == null ||
                StringUtils.isBlank(planPickUpDateTime) ||
                planPickUpStoreId == null ||
                StringUtils.isBlank(planReturnDateTime) ||
                planReturnStoreId == null ||
                packageId == null) {
            log.error(mid + "下单失败，入参={}", JSON.toJSONString(orderVehicleInputDTO), StatusCode.PARAM_EMPTY.getMsg());
            HidLog.order(LogPoint.NOT_ALLOW_ORDER_VEHICLE, StringUtils.EMPTY, mid, StringUtils.EMPTY, StringUtils.EMPTY, StatusCode.PARAM_EMPTY.getMsg(), mid, false, null, null ,null);
            HidLog.order(LogPoint.NOT_ALLOW_ORDER_VEHICLE, StringUtils.EMPTY, mid, StringUtils.EMPTY, StringUtils.EMPTY, StatusCode.PARAM_EMPTY.getMsg(), mid.toString(), false, null, null ,null);
            throw new BookVehicleException(ExceptionEnum.PARAM_EMPTY);
        }
        Date pickCarTime = DateUtil.getDateFromTimeStr(planPickUpDateTime, DateUtil.DATE_TYPE4);
        if (pickCarTime.compareTo(new Date()) <= 0) {
            throw new BookVehicleException(ExceptionEnum.ORDER_CHOOSE_OTHER_PICK_TIME);
        }
    }

    void checkOrderCondition1(CreateContractContext ctx) throws BookVehicleException {

        CreateRentalContractInput orderVehicleInputDTO = ctx.input;

        long goodsModelId = orderVehicleInputDTO.getGoodsModelId();
        long planPickUpStoreId = orderVehicleInputDTO.getPlanPickUpStoreId();
        long planPickUpShopSeq = orderVehicleInputDTO.getPlanPickUpShopSeq();
        long planReturnStoreId = orderVehicleInputDTO.getPlanReturnStoreId();
        long planReturnShopSeq = orderVehicleInputDTO.getPlanReturnShopSeq();
        long packageId = orderVehicleInputDTO.getPackageId();
        long vehicleModelId = orderVehicleInputDTO.getVehicleModelId();

        //  如果选择了送车上门服务，则门店是否开通上门服务
        Integer chooseSendService = orderVehicleInputDTO.getChooseSendService();
        if (chooseSendService == 1) {
            GetStoreServiceInfoReq getStoreServiceInfoReq = GetStoreServiceInfoReq.newBuilder().setId(planPickUpStoreId).build();
            GetStoreServiceInfoRes pickStoreServiceInfoRes = mdStoreService.getStoreServiceInfo(getStoreServiceInfoReq);
            int deliveryServiceStatus = pickStoreServiceInfoRes.getDeliveryServiceStatus();
            if (deliveryServiceStatus == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_NOT_SEND_SERVE);
            }
        }

        //  如果还车选择了上门取车服务，则门店是否开通上门服务
        Integer choosePickUpService = orderVehicleInputDTO.getChoosePickUpService();
        if (choosePickUpService == 1) {
            GetStoreServiceInfoReq getStoreServiceInfoReq = GetStoreServiceInfoReq.newBuilder().setId(planReturnStoreId).build();
            GetStoreServiceInfoRes returnStoreServiceInfoRes = mdStoreService.getStoreServiceInfo(getStoreServiceInfoReq);
            int deliveryServiceStatus = returnStoreServiceInfoRes.getDeliveryServiceStatus();
            if (deliveryServiceStatus == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_NOT_PICK_SERVE);
            }
        }

        //  判断营业/服务时间
        boolean isPickVirtualStore = false;
        if (planPickUpShopSeq > 0) {
            isPickVirtualStore = true;
        }
        boolean isReturnVirtualStore = false;
        if (planReturnShopSeq > 0) {
            isReturnVirtualStore = true;
        }

        long planSendDeliveryServiceId = orderVehicleInputDTO.getPlanPickUpDeliveryServiceId() == null ? 0L : orderVehicleInputDTO.getPlanPickUpDeliveryServiceId();
        long planPickUpDeliveryServiceId = orderVehicleInputDTO.getPlanSendDeliveryServiceId() == null ? 0L : orderVehicleInputDTO.getPlanSendDeliveryServiceId();

        // TODO: - 校验门店营业时间、暂停营业时间（车生活下单）
        // 校验取车门店营业时间、暂停营业时间
        CheckStoreBusinessTimeReq checkPickStoreReq = CheckStoreBusinessTimeReq.newBuilder()
                .setStoreId(planPickUpStoreId)
                .setStoreType(isPickVirtualStore ? 2 : 1)
                .setShopSeq(planPickUpShopSeq)
                .setDeliveryServiceId(planSendDeliveryServiceId)
                .setTakeType(chooseSendService == 1 ? 2 : 1)
                .setTakeTime(orderVehicleInputDTO.getPlanPickUpDateTime())
                .build();
        CheckStoreBusinessTimeRes pickupRes = mdStoreService.checkStoreBusinessTime(checkPickStoreReq);
        int pickupBusinessTimeStatus = pickupRes.getBusinessTimeStatus();
        if (pickupBusinessTimeStatus == 2) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_RETURN_3);
        } else if (pickupBusinessTimeStatus == 3) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_RETURN_4);
        }

        // 校验还车门店营业时间、暂停营业时间
        CheckStoreBusinessTimeReq checkReturnStoreReq = CheckStoreBusinessTimeReq.newBuilder()
                .setStoreId(planReturnStoreId)
                .setStoreType(isReturnVirtualStore ? 2 : 1)
                .setShopSeq(planReturnShopSeq)
                .setDeliveryServiceId(planPickUpDeliveryServiceId)
                .setTakeType(choosePickUpService == 1 ? 2 : 1)
                .setTakeTime(orderVehicleInputDTO.getPlanReturnDateTime())
                .build();
        CheckStoreBusinessTimeRes returnRes = mdStoreService.checkStoreBusinessTime(checkReturnStoreReq);
        int returnBusinessTimeStatus = returnRes.getBusinessTimeStatus();
        if (returnBusinessTimeStatus == 2) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_RETURN_5);
        } else if (returnBusinessTimeStatus == 3) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_RETURN_6);
        }

        //  判断取还车门店是否能还车
        CanReturnVehicleReq canReturnVehicleReq = CanReturnVehicleReq.newBuilder()
                .setTakeStoreId(planPickUpStoreId)
                .setTakeStoreType(isPickVirtualStore ? 2 : 1)
                .setTakeShopSeq(planPickUpShopSeq)
                .setTakeType(chooseSendService == 1 ? 2 : 1)
                .setSendDeliveryServiceId(planSendDeliveryServiceId)
                .setReturnStoreId(planReturnStoreId)
                .setReturnStoreType(isReturnVirtualStore ? 2 : 1)
                .setReturnShopSeq(planReturnShopSeq)
                .setReturnType(choosePickUpService == 1 ? 2 : 1)
                .setPickUpDeliveryServiceId(planPickUpDeliveryServiceId)
                .build();
        int canReturnVehicleStatus = mdStoreService.canReturnVehicle(canReturnVehicleReq).getCanReturnVehicleStatus();
        if (canReturnVehicleStatus == 2) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_RETURN_1);
        } else if (canReturnVehicleStatus == 3) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_RETURN_2);
        }

        //  判断取车门店营业/服务时间
        String planPickUpTimeStr = orderVehicleInputDTO.getPlanPickUpDateTime();
        String planReturnTimeStr = orderVehicleInputDTO.getPlanReturnDateTime();
        GetTakeReturnCarTimeAvailableReq pickCarStoreReq = GetTakeReturnCarTimeAvailableReq.newBuilder()
                .setReturnType(chooseSendService == 1 ? 2 : 1)
                .setJudgeType(1)
                .setStoreId(planPickUpStoreId)
                .setTime(planPickUpTimeStr)
                .setDeliveryServiceId(planSendDeliveryServiceId)
                .setStoreType(planPickUpShopSeq > 0 ? 2 : 1)
                .setShopSeq(planPickUpShopSeq)
                .build();
        int pickCarStoreStatus = mdStoreService.getTakeReturnCarTimeAvailable(pickCarStoreReq).getTakeReturnTimeStatus();
        //  1：在服务时间内 2：不在服务时间内 3：不在提前下单时间区间内
        if (pickCarStoreStatus == 2) {
            throw new BookVehicleException(ExceptionEnum.STORE_TIME_NOT_SUPPORT_ONE);
        } else if (pickCarStoreStatus == 3) {
            throw new BookVehicleException(ExceptionEnum.STORE_TIME_NOT_SUPPORT_THREE);
        }

        //  判断还车门店营业/服务时间
        GetTakeReturnCarTimeAvailableReq returnCarReq = GetTakeReturnCarTimeAvailableReq.newBuilder()
                .setReturnType(choosePickUpService == 1 ? 2 : 1)
                .setJudgeType(1)
                .setStoreId(planReturnStoreId)
                .setTime(planReturnTimeStr)
                .setDeliveryServiceId(planPickUpDeliveryServiceId)
                .setStoreType(planReturnShopSeq > 0 ? 2 : 1)
                .setShopSeq(planReturnShopSeq)
                .build();
        int returnCarStoreStatus = mdStoreService.getTakeReturnCarTimeAvailable(returnCarReq).getTakeReturnTimeStatus();
        // 1：在服务时间内 2：不在服务时间内 3：不在提前下单时间区间内
        if (returnCarStoreStatus == 2) { // 还车门店不判断提前下单时间
            throw new BookVehicleException(ExceptionEnum.STORE_TIME_NOT_SUPPORT_TWO);
        }

        Date planPickUpDateTime = DateUtil.getDateFromTimeStr(planPickUpTimeStr, DateUtil.DATE_TYPE4);
        Date planReturnDateTime = DateUtil.getDateFromTimeStr(planReturnTimeStr, DateUtil.DATE_TYPE4);

        //  如果取车门店为虚拟门店，判断运力
        if (isPickVirtualStore) {
            int sendAbilityStatus = checkAbility(1, planPickUpStoreId, DateUtil.dateToString(planPickUpDateTime, DateUtil.DATE_TYPE5),
                    DateUtil.dateToString(planPickUpDateTime, DateUtil.DATE_TYPE1));
            if (sendAbilityStatus == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_ABILITY_NOT_SUPPORT_FORE);
            }
        }

        //  如果选择了送车上门服务，则判断运力
        if (chooseSendService == BusinessConstants.ENABLE) {
            int sendAbilityStatus = checkAbility(3, planPickUpStoreId, DateUtil.dateToString(planPickUpDateTime, DateUtil.DATE_TYPE5),
                    DateUtil.dateToString(planPickUpDateTime, DateUtil.DATE_TYPE1));
            if (sendAbilityStatus == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_ABILITY_NOT_SUPPORT_THREE);
            }
        }
        //  如果还车门店为虚拟门店，判断运力
        if (isReturnVirtualStore) {
            int sendAbilityStatus = checkAbility(2, planReturnStoreId, DateUtil.dateToString(planReturnDateTime, DateUtil.DATE_TYPE5),
                    DateUtil.dateToString(planReturnDateTime, DateUtil.DATE_TYPE1));
            if (sendAbilityStatus == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_ABILITY_NOT_SUPPORT_ONE);
            }
        }

        //  查询取车门店
        GetStoreBaseInfoReq getStoreBaseInfoReq = GetStoreBaseInfoReq.newBuilder().setId(planPickUpStoreId).build();
        GetStoreBaseInfoRes pickStoreBaseInfo = mdStoreService.getStoreBaseInfo(getStoreBaseInfoReq);
        if (pickStoreBaseInfo == null) {
            throw new BookVehicleException(ExceptionEnum.STORE_QUERY_ERROR);
        }
        //  判断门店状态
        if (pickStoreBaseInfo.getStoreStatus() == 2) {
            throw new BookVehicleException(ExceptionEnum.STORE_NOT_PUBLIC);
        }
        GetStoreBaseInfoRes returnStoreBaseInfo = pickStoreBaseInfo;
        if (planReturnStoreId != planPickUpStoreId) {
            GetStoreBaseInfoReq getReturnStoreReq = GetStoreBaseInfoReq.newBuilder().setId(planReturnStoreId).build();
            returnStoreBaseInfo = mdStoreService.getStoreBaseInfo(getReturnStoreReq);
            if (returnStoreBaseInfo == null) {
                throw new BookVehicleException(ExceptionEnum.STORE_QUERY_ERROR);
            }
            if (returnStoreBaseInfo.getStoreStatus() == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_NOT_PUBLIC);
            }
        }

        //  如果还车选择了上门取车服务，则判断运力
        if (choosePickUpService == BusinessConstants.ENABLE) {
            //  如果还车选择了上门取车服务，则是否跨城市
            if (!pickStoreBaseInfo.getCityCode().equals(returnStoreBaseInfo.getCityCode())) {
                throw new BookVehicleException(ExceptionEnum.PICK_SERVER_CAN_NOT_CROSS);
            }
            int sendAbilityStatus = checkAbility(4, planReturnStoreId, DateUtil.dateToString(planReturnDateTime, DateUtil.DATE_TYPE5),
                    DateUtil.dateToString(planReturnDateTime, DateUtil.DATE_TYPE1));
            if (sendAbilityStatus == 2) {
                throw new BookVehicleException(ExceptionEnum.STORE_ABILITY_NOT_SUPPORT_TWO);
            }
        }

        // 获取计划取车门店订单间隔时间
        GetStoreDetailByIdRes getStoreDetailByIdRes = mdStoreService.getStoreDetailById(GetStoreDetailByIdReq.newBuilder().setId(planReturnStoreId).setStoreType(1).build());
        if (getStoreDetailByIdRes == null) {
            throw new BookVehicleException(ExceptionEnum.STORE_QUERY_ERROR);
        }
        LocalDateTime stockEndTime = LocalDateTime.parse(planReturnTimeStr,DateUtil.DATE_TYPE4).plusMinutes(getStoreDetailByIdRes.getOrderIntervalTime());
        //  判断车型库存
        CheckVehicleModelStockReq checkVehicleModelStockReq = CheckVehicleModelStockReq.newBuilder()
                .setStoreId(planPickUpStoreId)
                .setGoodsModelId(goodsModelId)
                .setTakeTime(planPickUpTimeStr)
                .setReturnTime(DateUtil.dateToString(stockEndTime, DateUtil.DATE_TYPE4))
                .setVehicleModelId(vehicleModelId)
                .build();
        int checkStock = mdStockService.checkVehicleModelStock(checkVehicleModelStockReq).getCheckStock();

        /*YGThirdPartySubmitDto ygThirdPartySubmitDto = new YGThirdPartySubmitDto();

        ygThirdPartySubmitDto.setSuccess(checkStock);
        trackPool.post(() -> yiGuanTrackSupport.thirdPartySubmitTrack(ygThirdPartySubmitDto, 1, "third_party_submit"));*/

        if (checkStock == 2) {
            throw new BookVehicleException(ExceptionEnum.STORE_STOCK_NOT_SUPPORT);
        }


        //  检查套餐是否可用
        if (packageId > 1) {
            SearchAvailablePackageReq searchAvailablePackageReq = SearchAvailablePackageReq.newBuilder()
                    .setOrgCode(pickStoreBaseInfo.getOperOrgCode())
                    .setStoreId(planPickUpStoreId)
                    .setGoodsModelId(goodsModelId)
                    .setUseScene(1)
                    .setUseStartDate(planPickUpTimeStr)
                    .setUseEndDate(planReturnTimeStr)
                    .setEarlyFlag(orderVehicleInputDTO.getIsEarlyBird())
                    .build();
            SearchAvailablePackageRes searchAvailablePackageRes = mdActService.searchAvailablePackage(searchAvailablePackageReq);
            List<PackageConfigForApp> availablePackageList = searchAvailablePackageRes.getAvailablePackageList();
            List<Long> availablePackageIdList = availablePackageList.stream().map(PackageConfigForApp::getId).collect(Collectors.toList());
            if (!availablePackageIdList.contains(packageId)) {
                throw new BookVehicleException(ExceptionEnum.PACKAGE_INVALID);
            }
        }
        // 设置参数
        orderVehicleInputDTO.setPickStoreBaseInfo(pickStoreBaseInfo);
        orderVehicleInputDTO.setReturnStoreBaseInfo(returnStoreBaseInfo);

        Set<String> allStoreCitySet = new HashSet<>();
        // 验证取还门店城市是否是门店模式，避免下单城市是大库模式
        GetStoreModeAllCityRes storeModeAllCity = mdStoreService.getStoreModeAllCity(GetStoreModeAllCityReq.newBuilder().build());
        if (storeModeAllCity.getRetCode() == 0) {
            List<CityBaseInfo> cityInfoList = storeModeAllCity.getCityInfoList();
            if (CollectionUtils.isNotEmpty(cityInfoList)) {
                for (CityBaseInfo cityBaseInfo : cityInfoList) {
                    allStoreCitySet.add(cityBaseInfo.getCityId() + "");
                }
            }
        }
        if (!allStoreCitySet.contains(pickStoreBaseInfo.getCityCode())) {
            throw new BookVehicleException(ExceptionEnum.PICK_STORE_NOT_IN_MODEL);
        }
        if (!allStoreCitySet.contains(returnStoreBaseInfo.getCityCode())) {
            throw new BookVehicleException(ExceptionEnum.RETURN_STORE_NOT_IN_MODEL);
        }
    }

    void checkOrderCondition2(CreateContractContext ctx) throws BookVehicleException {

        CreateRentalContractInput orderVehicleInputDTO = ctx.input;
        CreateRentalContractOut orderVehicleOutputDTO = ctx.output;

        //  判断门店约车条件
        GetStoreBaseInfoRes pickStoreBaseInfo = orderVehicleInputDTO.getPickStoreBaseInfo();
        GetStoreBaseInfoRes returnStoreBaseInfo = orderVehicleInputDTO.getReturnStoreBaseInfo();

        String mid = orderVehicleInputDTO.getBuyMid();
        Long planPickUpStoreId = orderVehicleInputDTO.getPlanPickUpStoreId();
        Long planReturnStoreId = orderVehicleInputDTO.getPlanReturnStoreId();
        String appKey = orderVehicleInputDTO.getAppKey();
        Long goodsModelId = orderVehicleInputDTO.getGoodsModelId();
        //  查询用户信息
        GetUserBasicInfoBo memberInfo = externalSystemFacade.getUserBasicInfo(mid);
        if (memberInfo == null) {
            throw new BookVehicleException(ExceptionEnum.USER_EMPTY);
        }
        //  判断用户是否有未支付的门店订单
        Integer waitPayStoreOrderCount = carRentalContractInfoService.getOrderCountByStatus(mid, new ArrayList<>(Arrays.asList(5)));
        if (waitPayStoreOrderCount > 0) {
            throw new BookVehicleException(ExceptionEnum.EXIST_NO_PAY_ORDER_1);
        }
        //  判断用户是否有未支付的大库订单
        String authId = memberInfo.getAuthId();
        GetUnPayCountOrderReq getUnPayCountOrderReq = GetUnPayCountOrderReq.newBuilder().setAuthId(authId).setPaymentStatus(ContractStatusEnum.TO_BE_PAID.getValue()).build();
        int waitPayOrderNum = mdDataProxy.getUnPayCountOrder(getUnPayCountOrderReq).getOrderCount();
        if (waitPayOrderNum > 0) {
            throw new BookVehicleException(ExceptionEnum.EXIST_NO_PAY_ORDER_2);
        }
        //  判断用户是否进行中的门店订单
        Integer currentStoreOrderCount = carRentalContractInfoService.getOrderCountByStatus(mid, new ArrayList<>(Arrays.asList(1,2,3,4,5)));
        if (currentStoreOrderCount > 0) {
            throw new BookVehicleException(ExceptionEnum.EXIST_CURRENT_ORDER_1);
        }
        //  判断用户是否进行中的门店订单（企业用车和员工用车）
        Integer currentStoreOrderCountForTransferTypeEnterpriseAndEmployee = carRentalContractInfoService.getOrderCountByStatusForTransferTypeEnterpriseAndEmployee(mid, new ArrayList<>(Arrays.asList(1, 2, 3, 4)));
        if (currentStoreOrderCountForTransferTypeEnterpriseAndEmployee > 0) {
            throw new BookVehicleException(ExceptionEnum.EXIST_CURRENT_ORDER_3);
        }
        //  限制相同驾照号账号约车(evwork)
        GetMemberIdsReq getMemberIdsReq = GetMemberIdsReq.newBuilder().setMid(mid).build();
        GetMemberIdsRes memberIdsWithSameDriverCodeRes = mdUserService.getMemberIdsWithSameDriverCode(getMemberIdsReq);
        List<String> authIds;
        if (memberIdsWithSameDriverCodeRes.getRetCode() != 0) {
            log.error("mdUserService.getMemberIdsWithSameDriverCode {},{}", memberIdsWithSameDriverCodeRes.getRetCode(), memberIdsWithSameDriverCodeRes.getRetMsg());
            authIds = new ArrayList<>();
        } else {
            authIds = new ArrayList<>(memberIdsWithSameDriverCodeRes.getAuthIdListList());
        }
        if (CollectionUtils.isEmpty(authIds)) {
            authIds.add(authId);
        }
        GetUnReturnVehOrderByAuthIdReq getUnReturnVehOrderByAuthIdReq = GetUnReturnVehOrderByAuthIdReq.newBuilder().addAllAuthId(authIds).build();
        List<OrderInfoDto> unReturnOder = mdDataProxy.getUnReturnVehOrderByAuthId(getUnReturnVehOrderByAuthIdReq).getOrderInfoList();
        if (CollectionUtils.isNotEmpty(unReturnOder)) {
            long innerOrderNum = unReturnOder.stream().filter(p -> p.getOrderType() == 1).count();
            if (innerOrderNum > 0) {
                throw new BookVehicleException(ExceptionEnum.EXIST_INNER_ORDER);
            }
            long personOrderNum = unReturnOder.stream().filter(p -> p.getOrderType() == 0).count();
            if(personOrderNum > 0) {
                throw new BookVehicleException(ExceptionEnum.EXIST_CURRENT_ORDER_2);
            }
        }

        //  判断是否有未处理的风控违约金订单
        GetRiskOrderByUserReq getRiskOrderByUserReq = GetRiskOrderByUserReq.newBuilder().setAuthId(authId).build();
        List<RiskOrderInfo> userRiskOrderDtoList = mdDataProxy.getRiskOrderByUser(getRiskOrderByUserReq).getRiskOrderList();
        if (CollectionUtils.isNotEmpty(userRiskOrderDtoList)) {
            long riskOrderNum = userRiskOrderDtoList.stream().filter(p -> "0".equals(p.getRiskOrderStatus())).count();
            if (riskOrderNum > 0) {
                throw new BookVehicleException(ExceptionEnum.EXIST_NO_PAY_ORDER_3);
            }
        }

        //  判断用户是否有违章
        GetUserNoHandleIllegalCountReq getUserNoHandleIllegalCountReq = GetUserNoHandleIllegalCountReq.newBuilder().setAuthId(authId).build();
        int illegal = mdDataProxy.getUserNoHandleIllegalCount(getUserNoHandleIllegalCountReq).getIllegalCount();
        if (illegal > 0) {
            throw new BookVehicleException(ExceptionEnum.NO_HANDLE_ILLEGAL);
        }

        // 用户暂停使用
        if (memberInfo.getStatus() == 1) {
            throw new BookVehicleException(ExceptionEnum.USER_SUSPEND);
        }

        // 判断身份认证，决定是否能下单
        //车生活不需要身份认证
        if (!AppKey.ALI_PAY_CAR_LIFE.equals(orderVehicleInputDTO.getAppKey())) {
            judgeIdentityAuthentication(memberInfo);
        }

//		String national = memberInfo.getNational();
//		if (authenticationStatus == 0) {
//			throw new BookVehicleException(ExceptionEnum.AUTHENTICATION_ERROR_ONE);
//		} else if (authenticationStatus == 1) {
//			if (!national.contains(BusinessConstants.CHINA_NATIONAL)) {
//				throw new BookVehicleException(ExceptionEnum.AUTHENTICATION_ERROR_TWO);
//			} else {
//				throw new BookVehicleException(ExceptionEnum.AUTHENTICATION_ERROR_THREE);
//			}
//		}

//        if (reviewStatus == 0) {
//            throw new BookVehicleException(ExceptionEnum.USER_IN_AUDIT);
//        } else if (reviewStatus == 2) {
//            throw new BookVehicleException(ExceptionEnum.USER_AUTHENTICATION_FAILED);
//        } else if (reviewStatus == -1 || reviewStatus == 3 || reviewStatus == 4) {
//            throw new BookVehicleException(ExceptionEnum.AUTHENTICATION_ERROR_ONE);
//        }

//        //  无驾照图片，档案编号，准驾车型的需要补全
//        if (national.contains(BusinessConstants.CHINA_NATIONAL) && StringUtils.isBlank(memberInfo.getDrivingLicenseImgUrl()) ){
//            throw new BookVehicleException(ExceptionEnum.LICENSE_STATUS_INVALID);
//        }
//        if (national.contains(BusinessConstants.CHINA_NATIONAL) &&
//                (StringUtils.isBlank(memberInfo.getDrivingLicenseType()) || StringUtils.isBlank(memberInfo.getLicenseExpirationTime()))){
//            throw new BookVehicleException(ExceptionEnum.LICENSE_STATUS_INVALID);
//        }
//
//        //  判断驾照三要素状态
//        GetDriverLicenseConfigReq getDriverLicenseConfigReq = GetDriverLicenseConfigReq.newBuilder().setCityName(memberInfo.getCityOfOrigin()).build();
//        int openStatus = mdDataProxy.getDriverLicenseConfig(getDriverLicenseConfigReq).getOpenStatus();
//        boolean enableAutoCheck = NumberUtils.INTEGER_ONE.equals(openStatus);
//        if (enableAutoCheck && national.contains(BusinessConstants.CHINA_NATIONAL)){
//            if (memberInfo.getLicenseElementsAuthStatus() == 2){
//                throw new BookVehicleException(ExceptionEnum.LICENSE_ELEMENTS_AUTH_FAILED);
//            }
//            if (memberInfo.getLicenseAuthStatus() == 2){
//                throw new BookVehicleException(ExceptionEnum.LICENSE_ELEMENTS_AUTH_ING);
//            }
//        }

//        if (StringUtils.isBlank(memberInfo.getFileNo())){
//            throw new BookVehicleException(ExceptionEnum.FILE_NO_EMPTY);
//        }

        // 驾照是否过期
//        if (StringUtils.isNotBlank(memberInfo.getLicenseExpirationTime())) {
//            LocalDate now = LocalDate.now();
//            LocalDate licenseExpirationTime = LocalDate.parse(memberInfo.getLicenseExpirationTime(), DateUtil.DATE_TYPE5);
//            if (now.isAfter(licenseExpirationTime)) {
//                throw new BookVehicleException(ExceptionEnum.DRIVER_LICENSE_EXPIRE);
//            }
//        } else {
//            throw new BookVehicleException(ExceptionEnum.DRIVER_LICENSE_EXPIRE);
//        }

//        //  用户是否存在人脸识别照片
//        String faceRecognitionImgUrl = memberInfo.getFaceRecognitionImgUrl();
//
//        //  人脸图片校验
//        //  2021-11-15 微信小程序  支付宝小程序,下单不判断人脸图片
//        if (!BusinessConstants.NO_FACE_XIAOCHEGNXU_ORDER_ORIGIN.contains(appKey)) {
//            if (memberInfo.getForeignNationality() == 0) {
//                String holdIdcardPicUrl = memberInfo.getHoldIdcardPicUrl();
//                if (StringUtils.isBlank(faceRecognitionImgUrl) && StringUtils.isBlank(holdIdcardPicUrl)) {
//                    throw new BookVehicleException(ExceptionEnum.FACE_IMAGE_EMPTY);
//                }
//                if (!(OrderComUtils.isConnect(faceRecognitionImgUrl) || OrderComUtils.isConnect(holdIdcardPicUrl))) {
//                    throw new BookVehicleException(ExceptionEnum.FACE_IMAGE_EMPTY);
//                }
//                if (StringUtils.isNotBlank(holdIdcardPicUrl) && StringUtils.isBlank(faceRecognitionImgUrl)) {
//                    throw new BookVehicleException(ExceptionEnum.FACE_PIC_NOT_EXIT);
//                }
//            } else {
//                if (StringUtils.isBlank(faceRecognitionImgUrl)) {
//                    throw new BookVehicleException(ExceptionEnum.FACE_PIC_NOT_EXIT);
//                }
//            }
//        }

        //  用户会员卡状态
        String cardNo = memberInfo.getCardNo(); //"null";
       /* QueryCardInfoReq queryCardInfoReq = QueryCardInfoReq.newBuilder().setCardNo(cardNo).build();
        QueryCardInfoRes queryCardInfoRes = mdUserService.queryCardInfo(queryCardInfoReq);
        if (queryCardInfoRes.getRetCode() != 0) {
            throw new BookVehicleException(ExceptionEnum.CARD_PRODUCTION);
        } else if (queryCardInfoRes.getStatus() != 0) {
            throw new BookVehicleException(ExceptionEnum.CARD_SUSPEND);
        } else if (queryCardInfoRes.getActivateStatus() != 1) {
            throw new BookVehicleException(ExceptionEnum.CARD_ACTIVATION);
        }*/

        if(!AppKey.ALI_PAY_CAR_LIFE.equals(orderVehicleInputDTO.getAppKey())) {
            //  判断会员押金状态
            GetVehicleDetailInfoForAppReq getVehicleDetailInfoForAppReq = GetVehicleDetailInfoForAppReq.newBuilder().setGoodsModelId(goodsModelId).build();
            GetVehicleDetailInfoForAppRes vehicleDetailInfoForAppRes = mdGoodsService.getVehicleDetailInfoForApp(getVehicleDetailInfoForAppReq);
            CheckDepositOrderReq checkDepositOrderReq = CheckDepositOrderReq.newBuilder()
                    .setMid(mid)
                    .setGoodsModelType(vehicleDetailInfoForAppRes.getVehicleModelType())
                    .setOrgCode(pickStoreBaseInfo.getOperOrgCode())
                    .setGoodsModelInfo(vehicleDetailInfoForAppRes.getGoodsModelName())
                    .build();
            CheckDepositOrderRes checkDepositOrderRes = mdUserService.checkDepositOrder(checkDepositOrderReq);
            orderVehicleOutputDTO.setZhimaStatus(checkDepositOrderRes.getZhimaStatus());
            orderVehicleOutputDTO.setWithHoldSignStatus(checkDepositOrderRes.getWithHoldSignStatus());
            orderVehicleOutputDTO.setDepositType(checkDepositOrderRes.getDepositType());
            if (checkDepositOrderRes.getResultStatus() == 3) {
                BigDecimal baseDeposit = StringUtils.isBlank(checkDepositOrderRes.getDeposit()) ? BigDecimal.ZERO : new BigDecimal(checkDepositOrderRes.getDeposit());
                BigDecimal vehicleDeposit = new BigDecimal(checkDepositOrderRes.getVehicleDeposit());
                if (checkDepositOrderRes.getDepositStatus() == 0 && BigDecimal.ZERO.equals(baseDeposit) && BigDecimal.ZERO.equals(vehicleDeposit)) {
                    throw new BookVehicleException(ExceptionEnum.ABSENCE_OF_DEPOSIT);
                } else {
                    throw new BookVehicleException(ExceptionEnum.DEPOSIT_LACK);
                }
            } else if (checkDepositOrderRes.getResultStatus() == 4 || checkDepositOrderRes.getResultStatus() == 7) {
                throw new BookVehicleException(ExceptionEnum.DEPOSIT_APPLY_DRAWBACK);
            }
        }

        //  判断订单取消次数
        String cancelNum = (String) redisUtils.hget(BusinessConstants.MAP_USER_CANCEL_COUNT_BOOKING, cardNo);
        int cancelCount = StringUtils.isNotBlank(cancelNum)?Integer.parseInt(cancelNum):0;
        if (cancelCount >= dailyBookCancelCountConfig.dailyBookCancelCount) {
            throw new BookVehicleException(ExceptionEnum.CANCEL_COUNT_LIMIT.getErrCode(), String.format(ExceptionEnum.CANCEL_COUNT_LIMIT.getErrMsg(), cancelCount));
        }

        String planPickUpTimeStr = orderVehicleInputDTO.getPlanPickUpDateTime();
        String planReturnTimeStr = orderVehicleInputDTO.getPlanReturnDateTime();
        Date planPickUpDateTime = DateUtil.getDateFromTimeStr(planPickUpTimeStr, DateUtil.DATE_TYPE4);
        Date planReturnDateTime = DateUtil.getDateFromTimeStr(planReturnTimeStr, DateUtil.DATE_TYPE4);

        String secondAppKey = "second_" + appKey;
        // 查询平台、一级、二级渠道
        PlatformChannelBo platformChannelBo = externalSystemFacade.queryBySecondChannel(secondAppKey);

        int minDays = 0;
        int maxDays = 0;
        //  判断起租天数、最长租期
        if (StringUtils.isNotEmpty(orderVehicleInputDTO.getAppVersion()) &&
                AppVersionUtil.compareTo(globalConfig.V5_10_0_APP_VERSION, orderVehicleInputDTO.getAppVersion()) <= 0) {
            GetEffectiveRentDaysNewReq getEffectiveRentDaysReq = GetEffectiveRentDaysNewReq.newBuilder()
                    .setOrgCode(pickStoreBaseInfo.getOperOrgCode())
                    .setStoreId(planPickUpStoreId)
                    .setPlanPickUpTime(DateUtil.getFormatDate(planPickUpTimeStr, DateUtil.DATE_TYPE4, DateUtil.DATE_TYPE1))
                    .setGoodsModelId(orderVehicleInputDTO.getVehicleModelId() + "")
                    .setPlatformId((int)platformChannelBo.getPlatformId())
                    .setAppKey(appKey).build();
            GetEffectiveRentDaysNewRes effectiveRentDays = mdGoodsService.getEffectiveRentDaysNew(getEffectiveRentDaysReq);
            minDays = effectiveRentDays.getMinDays();
            maxDays = effectiveRentDays.getMaxDays();
        } else {
            GetEffectiveRentDaysReq getEffectiveRentDaysReq = GetEffectiveRentDaysReq.newBuilder()
                    .setOrgCode(pickStoreBaseInfo.getOperOrgCode())
                    .setStoreId(planPickUpStoreId)
                    .setPlanPickUpTime(DateUtil.getFormatDate(planPickUpTimeStr, DateUtil.DATE_TYPE4, DateUtil.DATE_TYPE1)).build();
            GetEffectiveRentDaysRes effectiveRentDays = mdGoodsService.getEffectiveRentDays(getEffectiveRentDaysReq);
            minDays = effectiveRentDays.getMinDays();
            maxDays = effectiveRentDays.getMaxDays();
        }


        int rentDays = OrderComUtils.getDaysByTime(planPickUpDateTime, planReturnDateTime);

        if (AppVersionUtil.compareTo(globalConfig.V5_10_0_APP_VERSION, orderVehicleInputDTO.getAppVersion()) <= 0) {
            long validFragmentHourRules = externalSystemFacade.getValidFragmentHourRules(pickStoreBaseInfo.getOperOrgCode());

            // 零散小时是否满足1天
            int fragmentHourToOneDay = externalSystemFacade.fragmentHourToOneDay(validFragmentHourRules,
                    DateUtil.getFormatDate(planPickUpTimeStr, DateUtil.DATE_TYPE4, DateUtil.DATE_TYPE1),
                    DateUtil.getFormatDate(planReturnTimeStr, DateUtil.DATE_TYPE4, DateUtil.DATE_TYPE1), null, null,null).getFragmentHourToOneDay();
            if (fragmentHourToOneDay != 2) {
                rentDays = OrderUtil.getRentDays1(DateUtil.getDateFromStr(planPickUpTimeStr, DateUtil.DATE_TYPE4), DateUtil.getDateFromStr(planReturnTimeStr, DateUtil.DATE_TYPE4));
            }
        }

        if (rentDays < minDays) {
            throw new BookVehicleException(ExceptionEnum.RENT_DAYS_ERROR_ONE.getErrCode(), String.format(ExceptionEnum.RENT_DAYS_ERROR_ONE.getErrMsg(), minDays));
        }
        if (rentDays > maxDays) {
            throw new BookVehicleException(ExceptionEnum.RENT_DAYS_ERROR_TWO.getErrCode(), String.format(ExceptionEnum.RENT_DAYS_ERROR_TWO.getErrMsg(), maxDays));

        }
        Date lastReturnDateTime = DateUtil.localTimeToDate(DateUtil.addDay(planPickUpDateTime, rentDays));

        //  设置参数
        orderVehicleInputDTO.setMinDays(minDays);
        orderVehicleInputDTO.setMaxDays(maxDays);
        orderVehicleInputDTO.setRentDays(rentDays);
        orderVehicleInputDTO.setLastReturnDateTime(lastReturnDateTime);
        orderVehicleInputDTO.setUserName(memberInfo.getName());
        orderVehicleInputDTO.setUserAgencyId(memberInfo.getAgencyId());

    }

    /**
     * 判断身份认证，决定是否能下单
     * 【下单】仅校验身份认证状态为待认证/认证通过
     * 【下单】审核通过老会员30天内允许下单
     *
     * @param memberInfo
     */
    private void judgeIdentityAuthentication(GetUserBasicInfoBo memberInfo) {
        log.info("下单校验memberInfo[{}]", JSON.toJSONString(memberInfo));
        int reviewStatus = memberInfo.getReviewStatus(); // 老的驾驶证审核状态(-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
        int authenticationStatus = memberInfo.getAuthenticationStatus(); // 老的身份认证状态：0待认证(未认证) 1待认证(未刷脸上传) 2认证通过 3认证不通过
        int licenseReviewStatus = memberInfo.getLicenseReviewStatus(); // 新的驾驶证审核状态(1:未认证 2:待认证 3:已认证 4:认证不通过)
        int authenticationStatusNew = memberInfo.getAuthenticationStatusNew() == null ? 0 : memberInfo.getAuthenticationStatusNew(); // 新的身份认证状态 1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
        int expireType = memberInfo.getExpireType() == null ? 0 : memberInfo.getExpireType(); // 证件时间类型 1：非长期 2：长期
        String expirationDateStr = memberInfo.getExpirationDate(); // 证件有效期（yyyy-MM-dd)，如果expireType为2-长期，那么此字段为空
        LocalDate expirationDate = null;
        if (StringUtils.isNotBlank(expirationDateStr)) {
            expirationDate = LocalDate.parse(expirationDateStr, DateUtil.DATE_TYPE5);
        }
        String licenseExpirationTimeStr = memberInfo.getLicenseExpirationTime(); // 驾驶证有效期（yyyy-MM-dd)
        LocalDate licenseExpirationTime = null;
        // 沿用老逻辑，如果为空，认为驾驶证就是过期了
        if (StringUtils.isNotBlank(licenseExpirationTimeStr)) {
            licenseExpirationTime = LocalDate.parse(licenseExpirationTimeStr, DateUtil.DATE_TYPE5);
        }

        // 如果新的身份认证状态为 3或4 且身份证未过期 能下单
        if ((authenticationStatusNew == 3 || authenticationStatusNew == 4)
                && (expireType == 2 || (expirationDate != null && !LocalDate.now().isAfter(expirationDate)))) {
            log.info("下单校验，新的身份认证状态校验通过！authenticationStatusNew[{}]expireType[{}]expirationDate[{}]",
                    authenticationStatusNew, expireType, expirationDate);
        } else {
            String regTimeStr = memberInfo.getRegTime();
            if (StringUtils.isAnyBlank(issueDateV52, regTimeStr)) {
                throw new BookVehicleException(ExceptionEnum.AUTHENTICATION_ERROR_ONE);
            }
            LocalDate regTime = LocalDate.parse(regTimeStr, DateUtil.DATE_TYPE4);
            LocalDate issueDate = LocalDate.parse(issueDateV52, DateUtil.DATE_TYPE5);
            LocalDate issueDateAdd30 = issueDate.plusDays(30);
            // 否则，判断如果是老用户（注册时间比配置中心配置的发版时间早），且在新版发版30天内
            // 按照以前的条件（authenticationStatus=2、reviewStatus=1 且驾照未过期）进行判断如果通过，那么可以下单
            if (regTime.isBefore(issueDate) && LocalDate.now().isBefore(issueDateAdd30)
                    && authenticationStatus == 2 && reviewStatus == 1
                    && licenseExpirationTime != null && !LocalDate.now().isAfter(licenseExpirationTime)) {
                log.info("下单校验，符合老会员30天缓冲期条件！regTime[{}]issueDate[{}]issueDateAdd30[{}]authenticationStatus[{}]reviewStatus[{}]licenseExpirationTime[{}]",
                        regTime, issueDate, issueDateAdd30, authenticationStatus, reviewStatus, licenseExpirationTime);
            } else {
                log.info("下单校验，不符合老会员30天缓冲期条件，返回错误应答[{}]！regTime[{}]issueDate[{}]issueDateAdd30[{}]authenticationStatus[{}]reviewStatus[{}]licenseExpirationTime[{}]",
                        JSON.toJSONString(ExceptionEnum.AUTHENTICATION_ERROR_ONE), regTime, issueDate, issueDateAdd30, authenticationStatus, reviewStatus, licenseExpirationTime);
                throw new BookVehicleException(ExceptionEnum.AUTHENTICATION_ERROR_ONE);
            }
        }
    }

    public int checkAbility(int type, long storeId, String date, String datetime) {
        GetStoreSendAbilityReq getStoreSendAbilityReq = GetStoreSendAbilityReq.newBuilder()
                .setType(type)
                .setStoreId(storeId)
                .setDate(date)
                .setDatetime(datetime).build();
        return mdStoreService.getStoreSendAbility(getStoreSendAbilityReq).getSendAbilityStatus();
    }

    void createOrder(CreateContractContext ctx) throws BookVehicleException, BusinessException {

        CreateRentalContractInput rentalContractInput = ctx.input;
        CreateRentalContractOut orderVehicleOutputDTO = ctx.output;

        // 一级渠道
        String appKey = rentalContractInput.getAppKey();
        String secondAppKey = "second_" + appKey;
        rentalContractInput.setSecondAppKey(secondAppKey);
        // 查询平台、一级、二级渠道
        PlatformChannelBo platformChannelBo = externalSystemFacade.queryBySecondChannel(secondAppKey);
        String mid = rentalContractInput.getBuyMid();
        long goodsModelId = rentalContractInput.getGoodsModelId();
        long planPickUpStoreId = rentalContractInput.getPlanPickUpStoreId();
        long planReturnStoreId = rentalContractInput.getPlanReturnStoreId();
        String chooseServiceId = rentalContractInput.getChooseServiceId();
        Date planPickUpDateTime = DateUtil.getDateFromTimeStr(rentalContractInput.getPlanPickUpDateTime(), DateUtil.DATE_TYPE4);
        Date planReturnDateTime = DateUtil.getDateFromTimeStr(rentalContractInput.getPlanReturnDateTime(), DateUtil.DATE_TYPE4);
        int chooseSendService = rentalContractInput.getChooseSendService();
        int choosePickUpService = rentalContractInput.getChoosePickUpService();
        GetStoreBaseInfoRes pickStoreBaseInfo = rentalContractInput.getPickStoreBaseInfo();
        GetStoreBaseInfoRes returnStoreBaseInfo = rentalContractInput.getReturnStoreBaseInfo();
        long vehicleModelId = rentalContractInput.getVehicleModelId();
        int channel =1;
        if(rentalContractInput.getAppKey().equals(AppKey.ALI_PAY_CAR_LIFE)){
            channel=3;
        }

        // TODO：- 查询门店是否开启擎路
        int billingType = externalSystemFacade.checkStoreGrayFlag(planPickUpStoreId, rentalContractInput.getAppVersion());

        //  查询租金配置
        String pickStoreOrgCode = pickStoreBaseInfo.getOperOrgCode();
        if(vehicleModelId > 0){
            goodsModelId = vehicleModelId;
        }

        long priceId = 0;
        //  检查服务费用选项，补全强制选择的
        List<Integer> chooseServiceList = Lists.newArrayList();
        if (StringUtils.isNotBlank(chooseServiceId)) {
            List<Integer> tmp = Arrays.stream(chooseServiceId.split(ID_SPILT_1)).map(Integer::parseInt).collect(Collectors.toList());
            chooseServiceList.addAll(tmp);
        }
        Long serviceId = 0L;

        if (billingType != 2) {
            GetEffectiveRentConfigReq getEffectiveRentConfigReq = GetEffectiveRentConfigReq.newBuilder()
                    .setOrgCode(pickStoreOrgCode)
                    .setStoreId(planPickUpStoreId)
                    .setGoodsModelId(goodsModelId)
                    .setVehicleModelId(vehicleModelId)
                    .setChannel(channel)
                    .setAppKey(rentalContractInput.getAppKey())
                    .build();
            GetEffectiveRentConfigRes rentConfig = mdGoodsService.getEffectiveRentConfig(getEffectiveRentConfigReq);
            priceId = rentConfig.getId();
            if (priceId < 1) {
                throw new BookVehicleException(ExceptionEnum.NOT_EXIST_PRICE);
            }

            GetEffectiveBasicServiceConfigReq getEffectiveBasicServiceConfigReq = GetEffectiveBasicServiceConfigReq.newBuilder()
                    .setOrgCode(pickStoreOrgCode)
                    .setStoreId(planPickUpStoreId)
                    .setGoodsModelId(goodsModelId)
                    .setPlanPickUpTime(DateUtil.dateToString(planPickUpDateTime, DateUtil.DATE_TYPE1))
                    .setAppKey(rentalContractInput.getAppKey())
                    .build();
            GetEffectiveBasicServiceConfigRes basicServiceConfig = mdGoodsService.getEffectiveBasicServiceConfig(getEffectiveBasicServiceConfigReq);
            if (basicServiceConfig.getRetCode() != 0) {
                throw new BusinessException("查询基础服务费配置失败");
            }
            serviceId = basicServiceConfig.getId();

            // 如果车行手续费没配价格，就没有车行手续费类型
            if (StringUtils.isEmpty(basicServiceConfig.getVehicleMaintenanceFee())) {
                chooseServiceList.removeIf(amountType -> amountType.equals(AmountType.VEH_MAINTENANCE_FEE));
            }

            // 如果日租服务费没配价格，就没有日租服务费类型
            if (StringUtils.isEmpty(basicServiceConfig.getDailyRentalServiceFee())) {
                chooseServiceList.removeIf(amountType -> amountType.equals(AmountType.DAILY_RENT_SERVICE_FEE));
            }

            // 如果畅行服务费没配价格，就没有畅行服务费类型
            if (StringUtils.isEmpty(basicServiceConfig.getFreeTravelServiceFee())) {
                chooseServiceList.removeIf(amountType -> amountType.equals(AmountType.FREE_TRAVEL_SERVICE_FEE));
            }

            // 如果尊享服务费没配价格，就没有尊享服务费类型
            if (StringUtils.isEmpty(basicServiceConfig.getExclusiveServiceFee())) {
                chooseServiceList.removeIf(amountType -> amountType.equals(AmountType.EXCLUSIVE_SERVICE_AMOUNT));
            }

            if (basicServiceConfig.getFreeTravelServiceCheck() == 2) {
                if (!chooseServiceList.contains(AmountType.FREE_TRAVEL_SERVICE_FEE)) {
                    chooseServiceList.add(AmountType.FREE_TRAVEL_SERVICE_FEE);
                }
            }
            if (basicServiceConfig.getExclusiveServiceFeeCheck() == 2) {
                if (!chooseServiceList.contains(AmountType.EXCLUSIVE_SERVICE_AMOUNT)) {
                    chooseServiceList.add(AmountType.EXCLUSIVE_SERVICE_AMOUNT);
                }
            }
            if (basicServiceConfig.getDailyRentalServiceCheck() == 2) {
                if (!chooseServiceList.contains(AmountType.DAILY_RENT_SERVICE_FEE)) {
                    chooseServiceList.add(AmountType.DAILY_RENT_SERVICE_FEE);
                }
            }
            if (basicServiceConfig.getVehicleMaintenanceCheck() == 2) {
                if (!chooseServiceList.contains(AmountType.VEH_MAINTENANCE_FEE)) {
                    chooseServiceList.add(AmountType.VEH_MAINTENANCE_FEE);
                }
            }
        }

        if (planPickUpStoreId != rentalContractInput.getPlanReturnStoreId()) {
            // 跨城市+跨店服务费
            chooseServiceList.addAll(Arrays.asList(AmountType.CROSS_STORE_FEE_1, AmountType.CROSS_STORE_FEE_2));
        }
        //  立减活动
        GetStoreAvailableReduceActivityReq reduceActivityReq = GetStoreAvailableReduceActivityReq.newBuilder()
                .setMid(mid)
                .setStoreId(planPickUpStoreId)
                .setGoodsModelId(goodsModelId).build();
        GetStoreAvailableReduceActivityRes storeAvailableReduceActivity = mdActService.getStoreAvailableReduceActivity(reduceActivityReq);
        if (storeAvailableReduceActivity.getRetCode() != 0) {
            throw new BusinessException("查询立减活动失败");
        }
        long reduceActivityId = storeAvailableReduceActivity.getId();
        String activityDiscount = storeAvailableReduceActivity.getActivityDiscount();
        BigDecimal canReduceAmount = StringUtils.isBlank(activityDiscount) ? BigDecimal.ZERO : new BigDecimal(activityDiscount);

        //  取消订单配置
        long cancelOrderConfigId = 0;
        if (AppVersionUtil.compareTo(rentalContractInput.getAppVersion(), SystemConst.V_5_10_0) >= 0) {
            GetEffectiveCancelOrderConfigNewReq build = GetEffectiveCancelOrderConfigNewReq.newBuilder().setAppKey(rentalContractInput.getAppKey()).setOrgCode(pickStoreOrgCode).build();
            GetEffectiveCancelOrderConfigNewRes effectiveCancelOrderConfigNew = mdGoodsService.getEffectiveCancelOrderConfigNew(build);
            if (effectiveCancelOrderConfigNew.getRetCode() != 0) {
                throw new BusinessException("查询取消订单配置失败");
            }
            cancelOrderConfigId = effectiveCancelOrderConfigNew.getId();
        } else {
            GetEffectiveCancelOrderConfigReq cancelOrderConfigReq = GetEffectiveCancelOrderConfigReq.newBuilder()
                    .setOrgCode(pickStoreOrgCode)
                    .setStoreId(planPickUpStoreId).build();
            GetEffectiveCancelOrderConfigRes cancelOrderConfigRes = mdGoodsService.getEffectiveCancelOrderConfig(cancelOrderConfigReq);
            if (cancelOrderConfigRes.getRetCode() != 0) {
                throw new BusinessException("查询取消订单配置失败");
            }
            cancelOrderConfigId = cancelOrderConfigRes.getId();
        }

        //  超时还车配置
//        GetEffectiveReturnOverdueConfigReq returnOverdueConfigReq = GetEffectiveReturnOverdueConfigReq.newBuilder()
//                .setOrgCode(pickStoreOrgCode)
//                .setStoreId(planPickUpStoreId).build();
//        GetEffectiveReturnOverdueConfigRes returnOverdueConfigRes = mdGoodsService.getEffectiveReturnOverdueConfig(returnOverdueConfigReq);
//        if (returnOverdueConfigRes.getRetCode() != 0) {
//            throw new BusinessException("查询超时还车配置失败");
//        }
        long timeOutConfigId = -1;

        //  巡检超时配置-继续用车配置id
//        GetEffectivePatrolOverdueConfigReq patrolOverdueConfigReq = GetEffectivePatrolOverdueConfigReq.newBuilder()
//                .setOrgCode(pickStoreOrgCode)
//                .setStoreId(planPickUpStoreId)
//                .setCompensationType(1).build();
//        GetEffectivePatrolOverdueConfigRes patrolOverdueConfigOfUseCar = mdGoodsService.getEffectivePatrolOverdueConfig(patrolOverdueConfigReq);
//        if (patrolOverdueConfigOfUseCar.getRetCode() != 0) {
//            throw new BusinessException("查询巡检超时-继续用车配置失败");
//        }
        long inspectorTimeOutUseCarId = -1;

        //  巡检超时配置-用户取消配置id
//        GetEffectivePatrolOverdueConfigReq patrolOverdueConfigReq1 = GetEffectivePatrolOverdueConfigReq.newBuilder()
//                .setOrgCode(pickStoreOrgCode)
//                .setStoreId(planPickUpStoreId)
//                .setCompensationType(2).build();
//        GetEffectivePatrolOverdueConfigRes effectivePatrolOverdueConfigRes = mdGoodsService.getEffectivePatrolOverdueConfig(patrolOverdueConfigReq1);
//        if (effectivePatrolOverdueConfigRes.getRetCode() != 0) {
//            throw new BusinessException("查询巡检超时-用户取消配置失败");
//        }
        long inspectorTimeOutUserCancelId = -1;

        //  巡检取消配置id
//        GetEffectivePatrolOverdueConfigReq patrolOverdueConfigReq2 = GetEffectivePatrolOverdueConfigReq.newBuilder()
//                .setOrgCode(pickStoreOrgCode)
//                .setStoreId(planPickUpStoreId)
//                .setCompensationType(3).build();
        long inspectorCancelId = -1;

        //  送车上门服务费配置
        long sendSeverServiceId = -1;
        BigDecimal sendDeliveryServiceAmount = null;
        if (chooseSendService == 1) {
            chooseServiceList.add(AmountType.SEND_VEH_FEE);
            if (billingType != 2) {
                GetEffectiveSendServiceFeeReq sendServiceFeeReq = GetEffectiveSendServiceFeeReq.newBuilder()
                        .setOrgCode(pickStoreOrgCode)
                        .setStoreId(planPickUpStoreId)
                        .setGoodsModelId(goodsModelId)
                        .setPlanUseTime(DateUtil.dateToString(planPickUpDateTime, DateUtil.DATE_TYPE1)).build();
                GetEffectiveSendServiceFeeRes effectiveSendServiceFee = mdGoodsService.getEffectiveSendServiceFee(sendServiceFeeReq);
                if (effectiveSendServiceFee.getRetCode() != 0) {
                    throw new BusinessException("查询送车上门服务费配置失败");
                }
                sendSeverServiceId = effectiveSendServiceFee.getId();
            } else {
                sendDeliveryServiceAmount = externalSystemFacade.getDeliveryServiceById(rentalContractInput.getPlanSendDeliveryServiceId());
            }
        }

        //  上门取车服务费配置
        long pickSeverServiceId = -1;
        BigDecimal pickDeliveryServiceAmount = null;
        if (choosePickUpService == 1) {
            chooseServiceList.add(AmountType.PICK_UP_VEH_FEE);
            int returnBillingType = externalSystemFacade.checkStoreGrayFlag(planReturnStoreId, null);
            if (returnBillingType != 2) {
                GetEffectiveSendServiceFeeReq sendServiceFeeReq = GetEffectiveSendServiceFeeReq.newBuilder()
                        .setOrgCode(returnStoreBaseInfo.getOperOrgCode())
                        .setStoreId(planReturnStoreId)
                        .setGoodsModelId(goodsModelId)
                        .setPlanUseTime(DateUtil.dateToString(planReturnDateTime, DateUtil.DATE_TYPE1)).build();
                GetEffectiveSendServiceFeeRes effectiveSendServiceFee = mdGoodsService.getEffectiveSendServiceFee(sendServiceFeeReq);
                if (effectiveSendServiceFee.getRetCode() != 0) {
                    throw new BusinessException("查询上门取车服务费配置失败");
                }
                pickSeverServiceId = effectiveSendServiceFee.getId();
            } else {
                pickDeliveryServiceAmount = externalSystemFacade.getDeliveryServiceById(rentalContractInput.getPlanPickUpDeliveryServiceId());
            }
        }

        //  获取自动取消时长
        Date nowTime = new Date();
        GetOrgCancelTimeConfigReq getOrgCancelTimeConfigReq = GetOrgCancelTimeConfigReq.newBuilder().setOrgId(pickStoreOrgCode).build();
        int cancelOrderTime = mdDataProxy.getOrgCancelTimeConfig(getOrgCancelTimeConfigReq).getCancelOrderTime();
        LocalDateTime autoCancelTime;
        if (cancelOrderTime > 0) {
            autoCancelTime = DateUtil.AddMin(DateUtil.dateToLocalTimeDate(nowTime), cancelOrderTime);
        } else {
            autoCancelTime = DateUtil.AddMin(DateUtil.dateToLocalTimeDate(nowTime), 15);
        }
        Long preCardNo = rentalContractInput.getPreCardNo();
        Long preCouponSeq = rentalContractInput.getPreCouponSeq();
        Long preCardActivityId = rentalContractInput.getPreCardActivityId();

        long pickupNightServiceId = packageService.getNightServiceId(pickStoreBaseInfo.getOperOrgCode(), rentalContractInput.getPlanPickUpDateTime());
        if (pickupNightServiceId > 0) {
            chooseServiceList.add(AmountType.PICK_UP_NIGHT_AMOUNT);
        }

        long returnNightServiceId = packageService.getNightServiceId(returnStoreBaseInfo.getOperOrgCode(), rentalContractInput.getPlanReturnDateTime());
        if (returnNightServiceId > 0) {
            chooseServiceList.add(AmountType.RETURN_NIGHT_AMOUNT);
        }

        // 根据selfOperatedDiscountId调用act的查询自营折扣详情接口（getSignupProprietaryActivityDetail）
        List<ActSignUpDetailInfo> sourceInfoList = new ArrayList<>();
        List<SelfOperatedActivityDto> acts = getSelfOperatedActivityDtos(rentalContractInput.getAppVersion(), rentalContractInput.getSelfOperatedDiscountId(), billingType, sourceInfoList);
        Map<Long, String> actMap = new HashMap<>(); // key：报名id，value：活动标签id
        ActSignUpDetailInfoBo actSignUpDetailInfoBo = null;
        if (CollectionUtils.isNotEmpty(sourceInfoList)) {
            actSignUpDetailInfoBo = ActSignUpDetailInfoBo.from(sourceInfoList.get(0));
            actMap.put(actSignUpDetailInfoBo.getId(), actSignUpDetailInfoBo.getActivityTag());
        }

        // 计算预付款费用明细
        CalculatePrePayFeeDto prePayFeeDto = new CalculatePrePayFeeDto();
        prePayFeeDto.setPickUpDateTime(DateUtil.getDateFromStr(rentalContractInput.getPlanPickUpDateTime(), DateUtil.DATE_TYPE4));
        prePayFeeDto.setReturnDateTime(DateUtil.getDateFromStr(rentalContractInput.getPlanReturnDateTime(), DateUtil.DATE_TYPE4));
        prePayFeeDto.setPackageId(rentalContractInput.getPackageId());
        prePayFeeDto.setIsEarlyBird(rentalContractInput.getIsEarlyBird());
        prePayFeeDto.setPriceId(priceId);
        prePayFeeDto.setReduceActivityId(reduceActivityId);
        prePayFeeDto.setChooseServiceId(chooseServiceList);
        prePayFeeDto.setBaseServiceId(serviceId);
        prePayFeeDto.setSendServiceId(sendSeverServiceId);
        prePayFeeDto.setSendDistance(rentalContractInput.getPlanSendPointDistance());
        prePayFeeDto.setPickServiceId(pickSeverServiceId);
        prePayFeeDto.setPickDistance(rentalContractInput.getPlanPickUpPointDistance());
        prePayFeeDto.setPickStoreId(rentalContractInput.getPlanPickUpStoreId());
        prePayFeeDto.setReturnStoreId(rentalContractInput.getPlanReturnStoreId());
        prePayFeeDto.setUserCardNo(preCardNo);
        prePayFeeDto.setUserCouponSeq(preCouponSeq);
        prePayFeeDto.setCardActivityId(preCardActivityId);
        prePayFeeDto.setMid(mid);
        prePayFeeDto.setGoodsModelId(goodsModelId);
        prePayFeeDto.setAppVersion(rentalContractInput.getAppVersion());
        prePayFeeDto.setPickupNightServiceId(pickupNightServiceId);
        prePayFeeDto.setReturnNightServiceId(returnNightServiceId);
        // 下单使用随享卡
        long userAccompanyingCardId = rentalContractInput.getUserAccompanyingCardId();
        prePayFeeDto.setUserAccompanyingCardId(userAccompanyingCardId);

        // 获取零散小时配置
        long fragmentHourId = 0;
        if (AppVersionUtil.compareTo(globalConfig.V5_10_0_APP_VERSION, rentalContractInput.getAppVersion()) <= 0) {
            fragmentHourId = externalSystemFacade.getValidFragmentHourRules(pickStoreOrgCode);
        }
        prePayFeeDto.setFragmentHourId(fragmentHourId);

        // TODO: 擎路
        prePayFeeDto.setBillingType(billingType);
        prePayFeeDto.setOrgCode(pickStoreOrgCode);
        prePayFeeDto.setStoreId(planPickUpStoreId);
        prePayFeeDto.setMdModelId(rentalContractInput.getVehicleModelId()+"");
        prePayFeeDto.setPlatformId(platformChannelBo.getPlatformId());
        prePayFeeDto.setAppKey(platformChannelBo.getFirstChannelKey());
        prePayFeeDto.setSecondAppKey(platformChannelBo.getSecondChannelKey());
        prePayFeeDto.setSendServiceAmount(sendDeliveryServiceAmount);
        prePayFeeDto.setPickServiceAmount(pickDeliveryServiceAmount);
        prePayFeeDto.setActs(acts);
        CalculatePrePayFeeBo calculatePrePayFeeBo = externalSystemFacade.calculatePrePayFee(prePayFeeDto);
        log.info("下单调用计费计算费用接口,req:{}",prePayFeeDto);
        log.info("下单调用计费计算费用接口,res:{}",calculatePrePayFeeBo);

        CalPaymentBo calPaymentBo = null;
        try {
            CalPaymentDto calPaymentDto = billParamConvertService.convert(prePayFeeDto);
            calPaymentBo = externalSystemFacade.calPaymentFee(calPaymentDto);
            billResultCompareService.compare(calculatePrePayFeeBo, calPaymentBo);
        } catch (Exception e) {
            log.error("下单新计费失败,tid:{}", Trace.currentTraceId(), e);
            billResultCompareService.sendExceptionEmail("下单", e);
        }

        // 优惠券减免
        BigDecimal couponDeductionAmount = calculatePrePayFeeBo.getExemptionAmount().stream()
                .filter(exemption -> ExemptionType.COUPON_REDUCE.equals(exemption.getAmountType()))
                .map(ExemptionAmountBo::getAmount).findAny().orElse(BigDecimal.ZERO);
        // 折扣卡减免
        BigDecimal cardDeductionAmount = calculatePrePayFeeBo.getExemptionAmount().stream()
                .filter(exemption -> ExemptionType.USER_CARD_REDUCE.equals(exemption.getAmountType()))
                .map(ExemptionAmountBo::getAmount).findAny().orElse(BigDecimal.ZERO);
        BigDecimal preTotalAmount = calculatePrePayFeeBo.getOrderRealAmount();
        // 送车上门服务费
        BigDecimal preSendServiceAmount = calculatePrePayFeeBo.getServiceAmount().stream()
                .filter(service -> AmountType.SEND_VEH_FEE.equals(service.getAmountType()))
                .map(ServiceAmountBo::getAmount).findAny().orElse(BigDecimal.ZERO);
        // 上门取车服务费
        BigDecimal prePickServiceAmount = calculatePrePayFeeBo.getServiceAmount().stream()
                .filter(service -> AmountType.PICK_UP_VEH_FEE.equals(service.getAmountType()))
                .map(ServiceAmountBo::getAmount).findAny().orElse(BigDecimal.ZERO);

        if (couponDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
            preCouponSeq = calculatePrePayFeeBo.getCalCoupon().getCouponSeq();
        } else {
            preCouponSeq = -1L;
        }
        if (cardDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
            preCardNo = calculatePrePayFeeBo.getCalCard().getUserCardNo();
        } else {
            preCardNo = -1L;
        }
        String contractId = NoRuleUtil.generalContractId(redisUtils, mid, RedisKeyConst.CONTRACT_ID);
        LocalDateTime startTimeLocalDateTime = DateUtil.dateToLocalTimeDate(planPickUpDateTime);
        LocalDateTime endTimeLocalDateTime = DateUtil.dateToLocalTimeDate(planReturnDateTime);

        // 1. 保存租车合同
        CarRentalContractInfo rentalContractInfo = new CarRentalContractInfo();
        BeanUtils.copyProperties(rentalContractInput, rentalContractInfo);
        rentalContractInfo.setContractId(contractId);
        rentalContractInfo.setContractStatus(ContractStatusEnum.ORDERING.getValue());
        rentalContractInfo.setContractStatusNew(ContractStatusNewEnum.ORDERING.getValue());
        rentalContractInfo.setPrepaidTotalAmount(BigDecimal.ZERO);
        rentalContractInfo.setPlanPickUpDateTime(startTimeLocalDateTime);
        rentalContractInfo.setPlanReturnDateTime(endTimeLocalDateTime);
        rentalContractInfo.setLastReturnDateTime(OrderUtil.getLastEndTime(startTimeLocalDateTime, endTimeLocalDateTime));
        rentalContractInfo.setOrderReturnDateTime(endTimeLocalDateTime);
        rentalContractInfo.setCityId(pickStoreBaseInfo.getOperCityId());
        rentalContractInfo.setAutoCancelTime(autoCancelTime);
        rentalContractInfo.setOrgCode(pickStoreOrgCode);
        rentalContractInfo.setUserAgencyId(rentalContractInput.getUserAgencyId());
        rentalContractInfo.setDepositState(orderVehicleOutputDTO.getDepositType());
        rentalContractInfo.setAppVersion(rentalContractInput.getAppVersion());
        rentalContractInfo.setVehicleModelId(rentalContractInput.getVehicleModelId());
        if(StringUtils.isNotEmpty(rentalContractInput.getChannelVehicleId())){
            rentalContractInfo.setChannelVehicleId(rentalContractInput.getChannelVehicleId());
        }
        rentalContractInfo.setSecondAppKey(secondAppKey);
        rentalContractInfo.setTaxMainCompany(platformChannelBo.getTaxMainCompany());
        rentalContractInfo.setBillingType(billingType);
        rentalContractInfo.setPlatformId(platformChannelBo.getRealPlatformId());

        UserDTO userDTO = new UserDTO();
        userDTO.setId(-1L);
        userDTO.setName(rentalContractInput.getUserName());

        ctx.nowTime = nowTime;
        ctx.userDTO = userDTO;
        ctx.rentalContractInfo = rentalContractInfo;

        //用户信息进行数据组合
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("carLifeUserName",rentalContractInput.getCarLifeUserName());
        jsonObject.put("carLifeUserPhone",rentalContractInput.getCarLifeUserPhone());
        jsonObject.put("carLifeUserIdCard",rentalContractInput.getCarLifeUserIdCard());
        jsonObject.put("alipayExtInfo",rentalContractInput.getAlipayExtInfo());
        jsonObject.put("depositType",rentalContractInput.getDepositType());
        String ext = jsonObject.toString();

        // 2. 保存租车合同配置信息
        GetStoreServiceInfoReq getStoreServiceInfoReq = GetStoreServiceInfoReq.newBuilder().setId(planPickUpStoreId).build();
        GetStoreServiceInfoRes pickStoreServiceInfoRes = mdStoreService.getStoreServiceInfo(getStoreServiceInfoReq);
        CarRentalContractConfig rentalContractConfig = new CarRentalContractConfig();
        rentalContractConfig.setContractId(contractId);
        rentalContractConfig.setStagingNum(rentalContractInput.getStagingNum());
        rentalContractConfig.setPreCardNo(preCardNo < 0 ? -1 : preCardNo);
        rentalContractConfig.setPreCouponSeq(preCouponSeq < 0 ? -1 : preCouponSeq);
        rentalContractConfig.setCouponDeductionAmount(couponDeductionAmount);
        rentalContractConfig.setCardDeductionAmount(cardDeductionAmount);
        rentalContractConfig.setCardDiscount(calculatePrePayFeeBo.getCalCard().getDiscountRate());
        rentalContractConfig.setOrderIntervalTime(pickStoreServiceInfoRes.getOrderIntervalTime());
        rentalContractConfig.setExt(ext);
        rentalContractConfig.setLongRentSplitId(externalSystemFacade.getShortRentSplitRatioConfig());
        ctx.rentalContractConfig = rentalContractConfig;

        // 3. 保存商品订单
        String orderNo = NoRuleUtil.generalOrderNo(redisUtils, mid, RedisKeyConst.ORDER_NO);
        GoodsOrderInfo goodsOrderInfo = new GoodsOrderInfo();
        goodsOrderInfo.setMid(mid);
        goodsOrderInfo.setContractId(contractId);
        goodsOrderInfo.setOrderNo(orderNo);
        goodsOrderInfo.setOrderType(GoodsOrderTypeEnum.ORDER_TYPE_RENT.getValue());
        goodsOrderInfo.setOrderStatus(GoodsOrderStatusEnum.ORDER_STATUS_UNPAID.getValue());
        goodsOrderInfo.setTotalAmount(preTotalAmount);
        goodsOrderInfo.setHqTotalAmount(preTotalAmount);

        ctx.goodsOrderInfo = goodsOrderInfo;

        // 4. 保存租车合同项
            //总租车费
        BigDecimal totalRentAmount = calculatePrePayFeeBo.getCalRent().getTotalRentAmount();
           //服务费（取还车服务费、加油服务费、充电服务费）
        BigDecimal serviceAmount = calculatePrePayFeeBo.getServiceAmount().stream()
                .filter(service -> !AmountType.ADD_SERVICE_TYPE.contains(service.getAmountType()))
                .map(ServiceAmountBo::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
           //减免费用
        BigDecimal exemptionAmount = calculatePrePayFeeBo.getExemptionAmount().stream()
                .map(ExemptionAmountBo::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal rentAmount = totalRentAmount.add(serviceAmount).subtract(exemptionAmount); // 时长费 + 服务费（除了增值服务费）- 减免金额

        SaveRentContractItemDto saveRentContractItemDto = new SaveRentContractItemDto();
        saveRentContractItemDto.setServerOrigin(ServerOriginEnum.SERVER_ORIGIN_PREPAY.getValue());
        saveRentContractItemDto.setMid(mid);
        saveRentContractItemDto.setOrderNo(orderNo);
        saveRentContractItemDto.setMaxUseDays(rentalContractInput.getMaxDays());
        saveRentContractItemDto.setMinRentDays(rentalContractInput.getMinDays());
        saveRentContractItemDto.setRentAmount(rentAmount);
        saveRentContractItemDto.setPlanPickupTime(DateUtil.getDateFromStr(rentalContractInput.getPlanPickUpDateTime(), DateUtil.DATE_TYPE4));
        saveRentContractItemDto.setPlanReturnTime(DateUtil.getDateFromStr(rentalContractInput.getPlanReturnDateTime(), DateUtil.DATE_TYPE4));
        saveRentContractItemDto.setLastEndTime(OrderUtil.getLastEndTime(saveRentContractItemDto.getPlanPickupTime(), saveRentContractItemDto.getPlanReturnTime()));
        saveRentContractItemDto.setPackageId(rentalContractInput.getPackageId());
        saveRentContractItemDto.setPriceId(priceId);
        saveRentContractItemDto.setServiceId(serviceId);
        saveRentContractItemDto.setChooseServiceId(chooseServiceList.stream().map(Object::toString).collect(Collectors.joining(ID_SPILT_1)));
        saveRentContractItemDto.setReduceActivityId(reduceActivityId);
        saveRentContractItemDto.setCanReduceAmount(canReduceAmount);
        saveRentContractItemDto.setCancelOrderConfigId(cancelOrderConfigId);
        saveRentContractItemDto.setTimeOutConfigId(timeOutConfigId);
        saveRentContractItemDto.setInspectorTimeOutUseCarId(inspectorTimeOutUseCarId);
        saveRentContractItemDto.setInspectorTimeOutUserCancelId(inspectorTimeOutUserCancelId);
        saveRentContractItemDto.setInspectorCancelId(inspectorCancelId);
        saveRentContractItemDto.setFeeList(getPrePayFeeList(calculatePrePayFeeBo, pickupNightServiceId, returnNightServiceId)); // 保存费用明细
        saveRentContractItemDto.setExemptionList(getPrePayExemptionFeeList(calculatePrePayFeeBo, mid, actMap)); // 保存费用减免

        // 零散计费配置id
        saveRentContractItemDto.setHourRentConfigId(fragmentHourId);
        // 擎路计费配置入库
        if (billingType == 2) {
            saveRentContractItemDto.setPriceDetail(JSON.toJSONString(prePayFeeDto.getDailyRentConfig()));
            if (calPaymentBo != null && CollectionUtils.isNotEmpty(calPaymentBo.getDailyRentExemptionConfigList())) {
                saveRentContractItemDto.setPriceExemptionDetail(JSONObject.toJSONString(calPaymentBo.getDailyRentExemptionConfigList()));
            }
            saveRentContractItemDto.setServiceDetail(JSON.toJSONString(prePayFeeDto.getDailyServiceConfig()));
        }

        // 自营折扣明细入库
        if (CollectionUtils.isNotEmpty(acts) && calculatePrePayFeeBo.getActivityInfo() != null) {
            SelfOperatedDiscountDetailDto discountIdDetailDto = new SelfOperatedDiscountDetailDto();
            discountIdDetailDto.setAct(actSignUpDetailInfoBo);
            discountIdDetailDto.setBillingActs(acts);
            discountIdDetailDto.setBillingRealUse(calculatePrePayFeeBo.getActivityInfo());
            saveRentContractItemDto.setSelfOperatedDiscountDetail(JSON.toJSONString(discountIdDetailDto));
        }
        // 保存使用的随享卡
        CalAccompanyingCardInfoBo accompanyingCardInfo = calculatePrePayFeeBo.getAccompanyingCardInfo();
        List<ExemptionAmountBo> accompanyingCardInfoExemptionAmount = calculatePrePayFeeBo.getAccompanyingCardInfoExemptionAmount();
        if (accompanyingCardInfo != null) {
            saveRentContractItemDto.setAccompanyingCardId(accompanyingCardInfo.getUserAccompanyingCardId()); // 随享卡id
            saveRentContractItemDto.setAccompanyingCardDays(accompanyingCardInfo.getUseDays()); // 随享卡使用天数
        }
        if (CollectionUtils.isNotEmpty(accompanyingCardInfoExemptionAmount)) {
            ctx.accompanyingCardExemptionList = accompanyingCardInfoExemptionAmount;
        }
        ctx.contractId = contractId;
        ctx.orderNo = orderNo;
        ctx.contractItemIdForRent = NoRuleUtil.generalContractItemId(redisUtils, saveRentContractItemDto.getMid(), RedisKeyConst.CONTRACT_ITEM_ID);
        ctx.saveRentContractItemDto = saveRentContractItemDto;

        // 5. 租车合同-保存商品明细表
        String childOrderNo = NoRuleUtil.generalChildOrderNo(redisUtils, mid, RedisKeyConst.ORDER_NO_CHILD);
        GoodsOrderDetails goodsOrderDetails = new GoodsOrderDetails();
        goodsOrderDetails.setMid(mid);
        goodsOrderDetails.setOrderNo(orderNo);
        goodsOrderDetails.setChildOrderNo(childOrderNo);
        goodsOrderDetails.setTotalAmount(preTotalAmount);

        goodsOrderDetails.setFeeDetails(getGoodsFeeDetail(saveRentContractItemDto.getFeeList(), saveRentContractItemDto.getExemptionList()));
        goodsOrderDetails.setGoodsType(ItemTypeEnum.ITEM_TYPE_RENT.getValue());

        ctx.goodsOrderDetails = goodsOrderDetails;

        // 6. 送车上门服务合同项
        List<ContractItemFeeDto> sendCarFeeList = null;
        if (chooseSendService == 1) {
            // 保存送车上门合同项
            SaveSendCarContractItemDto saveSendCarContractItemDto = new SaveSendCarContractItemDto();
            saveSendCarContractItemDto.setServerOrigin(ServerOriginEnum.SERVER_ORIGIN_PREPAY.getValue());
            saveSendCarContractItemDto.setMid(mid);
            saveSendCarContractItemDto.setContractId(contractId);
            saveSendCarContractItemDto.setOrderNo(orderNo);
            saveSendCarContractItemDto.setPreSendServiceAmount(preSendServiceAmount);
            saveSendCarContractItemDto.setPlanSendStoreId(rentalContractInput.getPlanPickUpStoreId());
            saveSendCarContractItemDto.setPlanSendPointName(rentalContractInput.getPlanSendPointName());
            saveSendCarContractItemDto.setPlanSendPointAddress(rentalContractInput.getPlanSendPointAddress());
            saveSendCarContractItemDto.setPlanSendPointLongitude(rentalContractInput.getPlanSendPointLongitude());
            saveSendCarContractItemDto.setPlanSendPointLatitude(rentalContractInput.getPlanSendPointLatitude());
            saveSendCarContractItemDto.setPlanSendPointDistance(rentalContractInput.getPlanSendPointDistance());
            saveSendCarContractItemDto.setRentDays(rentalContractInput.getRentDays());
            saveSendCarContractItemDto.setServerPlanTime(startTimeLocalDateTime);
            saveSendCarContractItemDto.setCancelOrderConfigId(cancelOrderConfigId);
            saveSendCarContractItemDto.setTimeOutConfigId(timeOutConfigId);
            saveSendCarContractItemDto.setInspectorTimeOutUseCarId(inspectorTimeOutUseCarId);
            saveSendCarContractItemDto.setInspectorTimeOutUserCancelId(inspectorTimeOutUserCancelId);
            saveSendCarContractItemDto.setInspectorCancelId(inspectorCancelId);
            saveSendCarContractItemDto.setSendSeverServiceId(sendSeverServiceId);
            if (preSendServiceAmount.compareTo(BigDecimal.ZERO) > 0) {
                sendCarFeeList = Lists.newArrayList(new ContractItemFeeDto(AmountType.SEND_VEH_FEE, preSendServiceAmount));
                saveSendCarContractItemDto.setFeeList(sendCarFeeList); // 送车上门的费用明细
            }
            ctx.contractItemIdForSendCar = NoRuleUtil.generalContractItemId(redisUtils, saveRentContractItemDto.getMid(), RedisKeyConst.CONTRACT_ITEM_ID);
            ctx.saveSendCarContractItemDto = saveSendCarContractItemDto;

            // 保存送车上门订单明细
            SaveGoodsDetailDto saveGoodsDetailDto = new SaveGoodsDetailDto();
            saveGoodsDetailDto.setGoodsType(ItemTypeEnum.ITEM_TYPE_SEND_CAR.getValue());
            saveGoodsDetailDto.setMid(mid);
            saveGoodsDetailDto.setTotalAmount(preSendServiceAmount);
            saveGoodsDetailDto.setFeeDetail(Optional.ofNullable(sendCarFeeList).map(JSON::toJSONString).orElse(""));

            ctx.childOrderNoForSendCar = NoRuleUtil.generalChildOrderNo(redisUtils, saveGoodsDetailDto.getMid(), RedisKeyConst.ORDER_NO_CHILD);
            ctx.saveGoodsDetailDtoForSendCar  = saveGoodsDetailDto;
        }
        // 7. 上门取车服务合同项
        List<ContractItemFeeDto> pickCarFeeList = null;
        if (choosePickUpService == 1) {
            SavePickCarContractItemDto savePickCarContractItemDto = new SavePickCarContractItemDto();
            savePickCarContractItemDto.setServerOrigin(ServerOriginEnum.SERVER_ORIGIN_PREPAY.getValue());
            savePickCarContractItemDto.setMid(mid);
            savePickCarContractItemDto.setOrderNo(orderNo);
            savePickCarContractItemDto.setPrePickServiceAmount(prePickServiceAmount);
            savePickCarContractItemDto.setPlanPickUpStoreId(rentalContractInput.getPlanReturnStoreId());
            savePickCarContractItemDto.setPlanPickUpPointName(rentalContractInput.getPlanPickUpPointName());
            savePickCarContractItemDto.setPlanPickUpPointAddress(rentalContractInput.getPlanPickUpPointAddress());
            savePickCarContractItemDto.setPlanPickUpPointLongitude(rentalContractInput.getPlanPickUpPointLongitude());
            savePickCarContractItemDto.setPlanPickUpPointLatitude(rentalContractInput.getPlanPickUpPointLatitude());
            savePickCarContractItemDto.setPlanPickUpPointDistance(rentalContractInput.getPlanPickUpPointDistance());
            savePickCarContractItemDto.setRentDays(rentalContractInput.getRentDays());
            savePickCarContractItemDto.setServerPlanTime(endTimeLocalDateTime);
            savePickCarContractItemDto.setCancelOrderConfigId(cancelOrderConfigId);
            savePickCarContractItemDto.setTimeOutConfigId(timeOutConfigId);
            savePickCarContractItemDto.setInspectorTimeOutUseCarId(inspectorTimeOutUseCarId);
            savePickCarContractItemDto.setInspectorTimeOutUserCancelId(inspectorTimeOutUserCancelId);
            savePickCarContractItemDto.setInspectorCancelId(inspectorCancelId);
            savePickCarContractItemDto.setPickSeverServiceId(pickSeverServiceId);
            if (prePickServiceAmount.compareTo(BigDecimal.ZERO) > 0) {
                pickCarFeeList = Lists.newArrayList(new ContractItemFeeDto(AmountType.PICK_UP_VEH_FEE, preSendServiceAmount));
                savePickCarContractItemDto.setFeeList(pickCarFeeList); // 保存上门取车费用明细
            }

            ctx.contractItemIdForPickCar = NoRuleUtil.generalContractItemId(redisUtils, saveRentContractItemDto.getMid(), RedisKeyConst.CONTRACT_ITEM_ID);
            ctx.savePickCarContractItemDto = savePickCarContractItemDto;

            SaveGoodsDetailDto saveGoodsDetailDto = new SaveGoodsDetailDto();
            saveGoodsDetailDto.setGoodsType(ItemTypeEnum.ITEM_TYPE_PICK_CAR.getValue());
            saveGoodsDetailDto.setMid(mid);
            saveGoodsDetailDto.setTotalAmount(preSendServiceAmount);
            saveGoodsDetailDto.setFeeDetail(Optional.ofNullable(pickCarFeeList).map(JSON::toJSONString).orElse(""));

            ctx.childOrderNoForPickCar = NoRuleUtil.generalChildOrderNo(redisUtils, saveGoodsDetailDto.getMid(), RedisKeyConst.ORDER_NO_CHILD);
            ctx.saveGoodsDetailDtoForPickCar = saveGoodsDetailDto;
        }

        //  8. 购卡合同项
        List<ContractItemFeeDto> buyCardFeeList = null;
        if (preCardActivityId > 0) {
            PurchaseCardDto purchaseCardDto = new PurchaseCardDto();
            purchaseCardDto.setMid(mid);
            purchaseCardDto.setActivityId(preCardActivityId);
            purchaseCardDto.setQuantity(1);
            purchaseCardDto.setRealAmount(calculatePrePayFeeBo.getCalCard().getActivitySalesPrice());
            purchaseCardDto.setDiscountAmount(calculatePrePayFeeBo.getCalCard().getCardDeductionAmount());

            PurchaseCardBo purchaseCardBo = externalSystemFacade.purchaseCard(purchaseCardDto);
            ctx.purchaseCardBo = purchaseCardBo;

            BigDecimal cardSaleAmount = calculatePrePayFeeBo.getCalCard().getActivitySalesPrice();

            SaveGoodsDetailDto saveGoodsDetailDto = new SaveGoodsDetailDto();
            saveGoodsDetailDto.setGoodsType(ItemTypeEnum.ITEM_TYPE_BUY_CARD.getValue());
            saveGoodsDetailDto.setMid(mid);
            saveGoodsDetailDto.setTotalAmount(cardSaleAmount);
            buyCardFeeList = Lists.newArrayList(new ContractItemFeeDto(AmountType.BUY_CARD, cardSaleAmount, purchaseCardBo.getPurchaseId()));
            saveGoodsDetailDto.setFeeDetail(JSON.toJSONString(buyCardFeeList));
            ctx.childOrderNoForBuyCard = NoRuleUtil.generalChildOrderNo(redisUtils, saveGoodsDetailDto.getMid(), RedisKeyConst.ORDER_NO_CHILD);
            ctx.saveGoodsDetailDtoForBuyCard = saveGoodsDetailDto;

            SaveBuyCardContractItemDto saveBuyCardContractItemDto = new SaveBuyCardContractItemDto();
            saveBuyCardContractItemDto.setMid(mid);
            saveBuyCardContractItemDto.setOrderNo(orderNo);
            saveBuyCardContractItemDto.setCardSaleAmount(cardSaleAmount);
            saveBuyCardContractItemDto.setCardPurchaseId(purchaseCardBo.getPurchaseId());

            ctx.contractItemIdForBuyCard = NoRuleUtil.generalContractItemId(redisUtils, saveRentContractItemDto.getMid(), RedisKeyConst.CONTRACT_ITEM_ID);
            ctx.saveBuyCardContractItemDto = saveBuyCardContractItemDto;
        }
        // 9. 保存合同操作记录
        CarRentalContractOperationLog operationLog = new CarRentalContractOperationLog();
        operationLog.setContractId(contractId);
        operationLog.setContractItemId(ctx.contractItemIdForRent);
        operationLog.setOperateType(ContractStatusEnum.ORDERING.getValue());
        operationLog.setOperateTypeNew(ContractStatusNewEnum.ORDERING.getValue());
        operationLog.setOperateContent("预约成功");

        ctx.operationLog = operationLog;

        // 10. 保存商品订单操作记录
        GoodsOrderOperationRecord goodsOperationRecord = new GoodsOrderOperationRecord();
        goodsOperationRecord.setOrderNo(orderNo);
        goodsOperationRecord.setOperateContent("预约");

        ctx.goodsOperationRecord = goodsOperationRecord;

        // 11. 创建订单重试任务
        List<Task> tasks = new ArrayList<>();
        Task createPayOrderTask = new Task();
        createPayOrderTask.setTaskType(BusinessConstants.TASK_TYPE_CREATE_PAY_ORDER);
        createPayOrderTask.setTaskParam(orderNo);
        createPayOrderTask.setTaskStatus(BusinessConstants.TASK_STATUS_PENDING);
        createPayOrderTask.setNextRunTime(new Date(new Date().getTime() + 10*1000L)); // 10秒后再尝试
        tasks.add(createPayOrderTask);

        // 12. 取消订单任务
        Task cancelPayOrderTask = new Task();
        cancelPayOrderTask.setTaskType(BusinessConstants.TASK_TYPE_CANCEL_PAY_ORDER);
        cancelPayOrderTask.setTaskParam(orderNo);
        cancelPayOrderTask.setTaskStatus(BusinessConstants.TASK_STATUS_PENDING);
        cancelPayOrderTask.setNextRunTime(DateUtil.localTimeToDate(autoCancelTime)); // 到达设定的时间后取消
        tasks.add(cancelPayOrderTask);

        ctx.tasks = tasks;

        // 13.零散小时信息
        ctx.fragmentHourInfoList = createRentalContractForBookingMode.getFragmentHourInfoList(calculatePrePayFeeBo, ctx.contractId, ctx.orderNo, ctx.contractItemIdForRent);

        // 这里统一进行事务处理
        contractTransactions.createContract(ctx);

        orderVehicleOutputDTO.setContractId(contractId);

        ctx.calculatePrePayFeeBo = calculatePrePayFeeBo;
        if (CollectionUtils.isNotEmpty(saveRentContractItemDto.getFeeList())) {
            ctx.rentFeeList = saveRentContractItemDto.getFeeList().stream().map(item -> {
                item.setExt2(null);
                return item;
            }).collect(Collectors.toList());
        }
        ctx.sendCarFeeList = sendCarFeeList;
        ctx.pickCarFeeList = pickCarFeeList;
        ctx.buyCardFeeList = buyCardFeeList;


      /*  //组装埋点数据
        YGThirdPartySubmitDto ygThirdPartySubmitDto  = new YGThirdPartySubmitDto();
        ygThirdPartySubmitDto.setBranchId(String.valueOf(planPickUpStoreId));
        if(pickStoreBaseInfo!=null && !org.springframework.util.StringUtils.isEmpty(pickStoreBaseInfo.getStoreName())){
            ygThirdPartySubmitDto.setBranchName(pickStoreBaseInfo.getStoreName());
        }
        ygThirdPartySubmitDto.setCarModel(String.valueOf(vehicleModelId));
        if(platformChannelBo!=null){
            ygThirdPartySubmitDto.setChannelKey(platformChannelBo.getFirstChannelKey());
            ygThirdPartySubmitDto.setChannelName(platformChannelBo.getFirstChannelName());
        }
        if(pickStoreBaseInfo!=null){
            ygThirdPartySubmitDto.setCityCode(pickStoreBaseInfo.getCityCode());
            ygThirdPartySubmitDto.setCityName(pickStoreBaseInfo.getCityName());
        }
        if(returnStoreBaseInfo!=null){
            ygThirdPartySubmitDto.setReturnCityCode(returnStoreBaseInfo.getCityCode());
            ygThirdPartySubmitDto.setReturnCityName(returnStoreBaseInfo.getCityName());
        }

        if(chooseSendService==1){
            ygThirdPartySubmitDto.setRentType("上门送车");
        }else {
            ygThirdPartySubmitDto.setRentType("门店取车");
        }

        if(choosePickUpService==1){
            ygThirdPartySubmitDto.setReturnType("上门取车");
        }else {
            ygThirdPartySubmitDto.setReturnType("门店还车");
        }
        ygThirdPartySubmitDto.setPickupLat(rentalContractInput.getPlanSendPointLatitude().doubleValue());
        ygThirdPartySubmitDto.setPickupLon(rentalContractInput.getPlanSendPointLongitude().doubleValue());
        ygThirdPartySubmitDto.setReturnLat(rentalContractInput.getPlanPickUpPointLatitude().doubleValue());
        ygThirdPartySubmitDto.setReturnLon(rentalContractInput.getPlanPickUpPointLongitude().doubleValue());
        ygThirdPartySubmitDto.setPickupTime(startTimeLocalDateTime);
        ygThirdPartySubmitDto.setReturnTime(endTimeLocalDateTime);
        trackPool.post(() -> yiGuanTrackSupport.thirdPartySubmitTrack(ygThirdPartySubmitDto, 1, "third_party_submit"));*/
    }

    void createPayOrder(CreateContractContext ctx) {

        //  调用pay，生产支付订单
        CreatePayOrderDto createPayOrderDto = new CreatePayOrderDto();
        createPayOrderDto.setMid(ctx.input.getBuyMid());
        createPayOrderDto.setBillType(ExternalServiceConst.BILL_TYPE_PREPAY);
        createPayOrderDto.setOrderNo(ctx.orderNo);
        createPayOrderDto.setAmount(ctx.calculatePrePayFeeBo.getOrderRealAmount());
        createPayOrderDto.setGoodsSubject("订单预付款");
        List<ContractItemFeeDto> payFeeList = new ArrayList<>();
        Optional.ofNullable(ctx.rentFeeList).ifPresent(payFeeList::addAll);
        Optional.ofNullable(ctx.sendCarFeeList).ifPresent(payFeeList::addAll);
        Optional.ofNullable(ctx.pickCarFeeList).ifPresent(payFeeList::addAll);
        Optional.ofNullable(ctx.buyCardFeeList).ifPresent(payFeeList::addAll);
        createPayOrderDto.setFeeInfo(JSON.toJSONString(payFeeList));
        createPayOrderDto.setContractId(ctx.contractId);
        createPayOrderDto.setStoreId(ctx.input.getPlanPickUpStoreId());
        createPayOrderDto.setSource(1);
        createPayOrderDto.setBizLine(4);
        createPayOrderDto.setPayType(ExternalServiceConst.PAY_TYPE_PREPAY);
        createPayOrderDto.setClientIp(ctx.input.getClientIp());
        createPayOrderDto.setAppKey(ctx.input.getAppKey());
        createPayOrderDto.setAppType(ctx.input.getAppType());
        createPayOrderDto.setDistChannel(ctx.input.getOrderChannel());
        createPayOrderDto.setAgencyId(ctx.input.getUserAgencyId());

        try {
            String payOrderNo = externalSystemFacade.createPayOrder(createPayOrderDto);
            goodsOrderService.updateOrderInfo(ctx.orderNo, payOrderNo);
            taskService.updateToFinished(BusinessConstants.TASK_TYPE_CREATE_PAY_ORDER, ctx.orderNo, "创建支付单成功："+payOrderNo);
        } catch(BusinessException e) {
            Date nextRunTime = new Date(new Date().getTime() + 10*1000L);
            String msg = "创建支付单失败,retCode="+e.getCode()+",retMsg="+e.getMessage();
            taskService.updateNextRunTime(BusinessConstants.TASK_TYPE_CREATE_PAY_ORDER, ctx.orderNo, nextRunTime, msg);
            log.error("createPayOrder failed, message="+msg, e);
        }

    }

    void publishEvents(CreateContractContext ctx) {
        try {
            // 发送下单成功事件
            CarRentalContractInfo order = rentalContractInfoService.getByContractId(ctx.contractId);
            externalSystemFacade.publishCreateOrderEvent(order,"");
        } catch(Exception e) {
            log.error("publishCreateOrderEvent failed, message="+e.getMessage(), e);
        }
    }

    void afterProcess(CreateContractContext ctx) {

        createPayOrder(ctx); // 下单线程直接运行， 调用完成后再返回结果给前端

        mqWritePool.post( ()->publishEvents(ctx) );  // 异步发送消息, 使用tracablepool保证traceId在跨线程之间准确传递

        // 发送短信
        // 短信优化需求：https://wiki.gcsrental.com/wiki/spaces/T-1681206805087/pages/6524a9addaa28a4b20f6abb9，不再发送
        // pushOrderIngMsgToUser(ctx);

        // 同步订单状态至支付宝
        alipaySyncPool.post(() -> aliPayService.syncOrderToAliPay(ctx.rentalContractInfo.getContractId(), AlipayOrderSyncType.CREATE));

        // 同步订单状态至支付宝  车生活
        log.info("订单同步：调用---订单状态---下单成功---订单id--"+ctx.rentalContractInfo.getContractId()+"-----end");
        alipayCarLifeSyncPool.post(() -> aliPayCarLifeService.syncOrderToAliPayCarLife(ctx.rentalContractInfo.getContractId(), AliPayCarLifeOrderSyncType.UNPAID,false));
        // 保存押金担保配置id
        contractService.saveDepositConfigIdByAlipayCarLive(ctx.contractId, ctx.rentalContractInfo.getVehicleModelId());

        // 推送订单状态变更事件 0 -> 1下单
        mqPushService.pushStatusUpdate(ctx.contractId, ContractStatusEnum.ORDERING.getMessage());

        // 车生活埋点: 下单
        //trackPool.post(() -> yiGuanTrackSupport.aliCarLifeThirdPartyOrderStatus(ctx.contractId, "create", BigDecimal.ZERO,"third_party_order_status"));
    }

    // 尊敬的用户，您的订单预定成功，请于<planCancelOrderTime>前完成租金支付，逾期订单将自动取消，祝您用车愉快！
    private void pushOrderIngMsgToUser(CreateContractContext ctx) {
        CarRentalContractInfo rentalContractInfo = ctx.rentalContractInfo;
        Map<String, String> params = new HashMap<>();
        String mid = rentalContractInfo.getMid();
        params.put("planCancelOrderTime", DateUtil.dateToString(rentalContractInfo.getAutoCancelTime(), DateTimeFormatter.ofPattern("MM/dd HH:mm")));
        messagePushService.sendInformationToUser(mid, 217, params);
    }

    List<ContractItemFeeDto> getPrePayFeeList(CalculatePrePayFeeBo calculateRenewFeeBo, long pickupNightServiceId, long returnNightServiceId) {
        List<ContractItemFeeDto> list = new ArrayList<>();
        // 租金
        CalculateRentAmountBo calRent = calculateRenewFeeBo.getCalRent();
        if (calRent.getPackageAmount() != null && (calRent.getPackageDayNum() > 0)) {
            ContractItemFeeDto packageFee = new ContractItemFeeDto(AmountType.RENT_AMOUNT_PACKAGE, calRent.getPackageAmount(),
                    calRent.getPackageAmount().divide(new BigDecimal(calRent.getPackageDayNum()), 2, RoundingMode.HALF_UP), new BigDecimal(calRent.getPackageDayNum()));
            packageFee.setIsEarlyBird(calRent.getIsEarlyBird() == IsEarlyBirdEnum.YES.getValue() ? IsEarlyBirdEnum.YES.getValue() : IsEarlyBirdEnum.NO.getValue());

            // 每日价格
            if (CollectionUtils.isNotEmpty(calRent.getDailyPackageInfo())) {
                packageFee.setExt2(JSON.toJSONString(calRent.getDailyPackageInfo()));
            }

            list.add(packageFee);
        }
        if (calRent.getOutPackageStandardAmount() != null && (calRent.getOutPackageStandardDayNum() > 0)) {
            ContractItemFeeDto standardFee = new ContractItemFeeDto(AmountType.RENT_AMOUNT_STANDARD, calRent.getOutPackageStandardAmount(),
                    calRent.getOutPackageStandardAmount().divide(new BigDecimal(calRent.getOutPackageStandardDayNum()), 2, RoundingMode.HALF_UP),
                    new BigDecimal(calRent.getOutPackageStandardDayNum()));

            // 每日价格
            if (CollectionUtils.isNotEmpty(calRent.getDailyStandardInfo())) {
                standardFee.setExt2(JSON.toJSONString(calRent.getDailyStandardInfo()));
            }

            list.add(standardFee);
        }
        // 零散小时费用
        if (calRent.getFragmentHourAmount() != null && calRent.getFragmentHourAmount().compareTo(BigDecimal.ZERO) >0) {
            ContractItemFeeDto fragmentHourFee = new ContractItemFeeDto(AmountType.FRAGMENT_HOUR_AMOUNT, calRent.getFragmentHourAmount(),
                    calRent.getFragmentHourAmount().divide(calRent.getFragmentHour(), 2, RoundingMode.HALF_UP),
                    calRent.getFragmentHour());

            // 零散小时明细
            if (calRent.getFragmentHourInfo() != null) {
                fragmentHourFee.setExt2(JSON.toJSONString(calRent.getFragmentHourInfo()));
            }

            list.add(fragmentHourFee);
        }
        // 服务费   排除上门送取服务费
        List<ServiceAmountBo> serviceAmount = calculateRenewFeeBo.getServiceAmount();
        List<ContractItemFeeDto> serviceFee = serviceAmount.stream()
                .filter(service -> !(AmountType.PICK_UP_VEH_FEE.equals(service.getAmountType()) || AmountType.SEND_VEH_FEE.equals(service.getAmountType())))
                .map(service -> {
                    ContractItemFeeDto contractItemFeeDto = new ContractItemFeeDto(service.getAmountType(), service.getAmount(), service.getUnitPrice(), service.getNum());
                    if (AmountType.PICK_UP_NIGHT_AMOUNT.equals(service.getAmountType())) {
                        contractItemFeeDto.setNightServiceId(pickupNightServiceId);
                    }
                    if (AmountType.RETURN_NIGHT_AMOUNT.equals(service.getAmountType())) {
                        contractItemFeeDto.setNightServiceId(returnNightServiceId);
                    }

                    return contractItemFeeDto;
                }).collect(Collectors.toList());

        list.addAll(serviceFee);
        return list;
    }

    List<ContractItemFeeDto> getPrePayFeeListNew(CalPaymentBo calPaymentBo, long pickupNightServiceId, long returnNightServiceId) {
        List<ContractItemFeeDto> list = new ArrayList<>();
        // 租金
        CalculateRentAmountBo calRent = calPaymentBo.getCalRent();
        if (calRent.getPackageAmount() != null && (calRent.getPackageDayNum() > 0)) {
            ContractItemFeeDto packageFee = new ContractItemFeeDto(AmountType.RENT_AMOUNT_PACKAGE, calRent.getPackageAmount(),
                    calRent.getPackageAmount().divide(new BigDecimal(calRent.getPackageDayNum()), 2, RoundingMode.HALF_UP), new BigDecimal(calRent.getPackageDayNum()));
            packageFee.setIsEarlyBird(calRent.getIsEarlyBird() == IsEarlyBirdEnum.YES.getValue() ? IsEarlyBirdEnum.YES.getValue() : IsEarlyBirdEnum.NO.getValue());

            // 每日价格
            if (CollectionUtils.isNotEmpty(calRent.getDailyPackageInfo())) {
                packageFee.setExt2(JSON.toJSONString(calRent.getDailyPackageInfo()));
            }

            list.add(packageFee);
        }
        if (calRent.getOutPackageStandardAmount() != null && (calRent.getOutPackageStandardDayNum() > 0)) {
            ContractItemFeeDto standardFee = new ContractItemFeeDto(AmountType.RENT_AMOUNT_STANDARD, calRent.getOutPackageStandardAmount(),
                    calRent.getOutPackageStandardAmount().divide(new BigDecimal(calRent.getOutPackageStandardDayNum()), 2, RoundingMode.HALF_UP),
                    new BigDecimal(calRent.getOutPackageStandardDayNum()));

            // 每日价格
            if (CollectionUtils.isNotEmpty(calRent.getDailyStandardInfo())) {
                standardFee.setExt2(JSON.toJSONString(calRent.getDailyStandardInfo()));
            }

            list.add(standardFee);
        }
        // 零散小时费用
        if (calRent.getFragmentHourAmount() != null && calRent.getFragmentHourAmount().compareTo(BigDecimal.ZERO) >0) {
            ContractItemFeeDto fragmentHourFee = new ContractItemFeeDto(AmountType.FRAGMENT_HOUR_AMOUNT, calRent.getFragmentHourAmount(),
                    calRent.getFragmentHourAmount().divide(calRent.getFragmentHour(), 2, RoundingMode.HALF_UP),
                    calRent.getFragmentHour());

            // 零散小时明细
            if (calRent.getFragmentHourInfo() != null) {
                fragmentHourFee.setExt2(JSON.toJSONString(calRent.getFragmentHourInfo()));
            }

            list.add(fragmentHourFee);
        }
        // 服务费   排除上门送取服务费
        List<ServiceAmountBo> serviceAmount = calPaymentBo.getCalServiceAmountList();
        List<ContractItemFeeDto> serviceFee = serviceAmount.stream()
                .filter(service -> !(AmountType.PICK_UP_VEH_FEE.equals(service.getAmountType()) || AmountType.SEND_VEH_FEE.equals(service.getAmountType())))
                .map(service -> {
                    ContractItemFeeDto contractItemFeeDto = new ContractItemFeeDto(service.getAmountType(), service.getAmount(), service.getUnitPrice(), service.getNum());
                    if (AmountType.PICK_UP_NIGHT_AMOUNT.equals(service.getAmountType())) {
                        contractItemFeeDto.setNightServiceId(pickupNightServiceId);
                    }
                    if (AmountType.RETURN_NIGHT_AMOUNT.equals(service.getAmountType())) {
                        contractItemFeeDto.setNightServiceId(returnNightServiceId);
                    }

                    return contractItemFeeDto;
                }).collect(Collectors.toList());

        list.addAll(serviceFee);
        return list;
    }

    @Nullable
    public List<SelfOperatedActivityDto> getSelfOperatedActivityDtos(String appVersion, Long selfOperatedDiscountId, int billingType,
                                                                     List<ActSignUpDetailInfo> sourceInfoList) throws BusinessException {
        List<SelfOperatedActivityDto> acts = null; // 门店车型对应的 可用自营活动集合
        if (AppVersionUtil.compareTo(appVersion, globalConfig.V5_12_0_APP_VERSION) >= 0 && billingType == 2) {
            if (selfOperatedDiscountId != null && selfOperatedDiscountId > 0) {
                GetSignupProprietaryActivityDetailRes detailRes = externalSystemFacade.getSignupProprietaryActivityDetail(selfOperatedDiscountId);
                if (detailRes == null || detailRes.getInfo() == null) {
                    throw new BusinessException("查询自营折扣失败");
                }
                ActSignUpDetailInfo sourceInfo = detailRes.getInfo();
                sourceInfoList.add(sourceInfo);
                long signupId = sourceInfo.getId(); // 报名id
                long activityId = sourceInfo.getActivityId(); // 活动id
                String activityName = sourceInfo.getActivityName(); // 活动名称
                int activityType = sourceInfo.getActivityType(); // 活动类型：1-满减、2-打折
                int pricingType = sourceInfo.getPricingType(); // 定价类型：1-灵活定价、2-规范定价
                List<SignupFullMinusFlexiblePricing> signupFullMinusFlexiblePricing = sourceInfo.getSignupFullMinusFlexiblePricingList(); // 报名后满减灵活定价 (报名后会有值)
                List<SignupDiscountFlexiblePricing> signupDiscountFlexiblePricing = sourceInfo.getSignupDiscountFlexiblePricingList(); // 报名后打折灵活定价 (报名后会有值)
                List<FullMinusStandardPricing> fullMinusStandardPricing = sourceInfo.getFullMinusStandardPricingList(); // 满减规范定价
                List<DiscountStandardPricing> discountStandardPricing = sourceInfo.getDiscountStandardPricingList(); // 打折规范定价

                List<ActivityRulerDto> rulers = new ArrayList<>();
                if (activityType == 1 && pricingType == 1) {
                    for (SignupFullMinusFlexiblePricing item : signupFullMinusFlexiblePricing) {
                        rulers.add(ActivityRulerDto.builder()
                                .days(Integer.parseInt(item.getDays()))
                                .discount(Integer.parseInt(item.getDiscountAmount()))
                                .build());
                    }
                } else if (activityType == 2 && pricingType == 1) {
                    for (SignupDiscountFlexiblePricing item : signupDiscountFlexiblePricing) {
                        rulers.add(ActivityRulerDto.builder()
                                .days(Integer.parseInt(item.getDays()))
                                .discount(Integer.parseInt(item.getDiscount()))
                                .build());
                    }
                } else if (activityType == 1 && pricingType == 2) {
                    for (FullMinusStandardPricing item : fullMinusStandardPricing) {
                        rulers.add(ActivityRulerDto.builder()
                                .days(Integer.parseInt(item.getDays()))
                                .discount(Integer.parseInt(item.getDiscountAmount()))
                                .build());
                    }
                } else if (activityType == 2 && pricingType == 2) {
                    for (DiscountStandardPricing item : discountStandardPricing) {
                        rulers.add(ActivityRulerDto.builder()
                                .days(Integer.parseInt(item.getDays()))
                                .discount(Integer.parseInt(item.getDiscount()))
                                .build());
                    }
                }

                SelfOperatedActivityDto selfOperatedActivityDto = new SelfOperatedActivityDto();
                selfOperatedActivityDto.setActId(signupId);
                selfOperatedActivityDto.setActivityType(activityType);
                selfOperatedActivityDto.setRulers(rulers);
                selfOperatedActivityDto.setMaxRentDays(sourceInfo.getMaxRentDays());
                selfOperatedActivityDto.setSameDayUseFlag(sourceInfo.getSameDayUseFlag());
                acts = new ArrayList<>();
                acts.add(selfOperatedActivityDto);
            }
        }
        return acts;
    }



    List<ContractItemExemptionDto> getPrePayExemptionFeeList(CalculatePrePayFeeBo calculateRenewFeeBo) {
        return calculateRenewFeeBo.getExemptionAmount().stream()
                .map(exemption -> new ContractItemExemptionDto(exemption.getAmountType(), exemption.getAmount()))
                .collect(Collectors.toList());
    }

    public List<ContractItemExemptionDto> getPrePayExemptionFeeList(CalculatePrePayFeeBo calculateRenewFeeBo, String mid, Map<Long, String> actMap) {
        List<ContractItemExemptionDto> result = new ArrayList<>();
        for (ExemptionAmountBo exemption : calculateRenewFeeBo.getExemptionAmount()) {
            ContractItemExemptionDto dto = new ContractItemExemptionDto(exemption.getAmountType(), exemption.getAmount());
            // 当类型是 17-优惠券抵扣，ext字段落优惠券名称；
            if (exemption.getAmountType().equals(AmountType.COUPON_DEDUCTION)) {
                // 根据优惠券编号，查询优惠券名称
                if (calculateRenewFeeBo.getCalCoupon() != null && calculateRenewFeeBo.getCalCoupon().getCouponSeq() != null) {
                    CouponViewBo couponViewBo = couponService.getCouponCached(calculateRenewFeeBo.getCalCoupon().getCouponSeq(), mid);
                    if (couponViewBo != null) {
                        ContractItemExemptionExt ext = new ContractItemExemptionExt();
                        ext.setCouponOrigin(couponViewBo.getCouponOrigin());
                        dto.setExt(JSON.toJSONString(ext));
                    }
                }
            }
            // 当类型是 45-自营折扣，ext字段落折扣详情 满2天8折；
            else if (exemption.getAmountType().equals(AmountType.SELF_OPERATED_DISCOUNT)) {
                SelfOperatedActivityDiscountBo activityInfo = calculateRenewFeeBo.getActivityInfo();
                if (activityInfo != null && activityInfo.getActId() > 0 && actMap.get(activityInfo.getActId()) != null) {
                    ContractItemExemptionExt ext = new ContractItemExemptionExt();
                    ext.setFeeDesc(actMap.get(activityInfo.getActId()));
                    dto.setExt(JSON.toJSONString(ext));
                }
            }
            result.add(dto);
        }
        return result;
    }

    public List<ContractItemExemptionDto> getPrePayExemptionFeeListNew(CalPaymentBo calPaymentBo, String mid, Map<Long, String> actMap) {
        List<ContractItemExemptionDto> result = new ArrayList<>();
        for (ExemptionAmountBo exemption : calPaymentBo.getCalExemptionAmountList()) {
            ContractItemExemptionDto dto = new ContractItemExemptionDto(exemption.getAmountType(), exemption.getAmount());
            // 当类型是 17-优惠券抵扣，ext字段落优惠券名称；
            if (exemption.getAmountType().equals(AmountType.COUPON_DEDUCTION)) {
                // 根据优惠券编号，查询优惠券名称
                if (calPaymentBo.getCouponInfo() != null && calPaymentBo.getCouponInfo().getCouponSeq() != null) {
                    CouponViewBo couponViewBo = couponService.getCouponCached(calPaymentBo.getCouponInfo().getCouponSeq(), mid);
                    if (couponViewBo != null) {
                        ContractItemExemptionExt ext = new ContractItemExemptionExt();
                        ext.setCouponOrigin(couponViewBo.getCouponOrigin());
                        dto.setExt(JSON.toJSONString(ext));
                    }
                }
            }
            // 当类型是 45-自营折扣，ext字段落折扣详情 满2天8折；
            else if (exemption.getAmountType().equals(AmountType.SELF_OPERATED_DISCOUNT)) {
                SelfOperatedActivityDiscountBo activityInfo = calPaymentBo.getActDiscount();
                if (activityInfo != null && activityInfo.getActId() > 0 && actMap.get(activityInfo.getActId()) != null) {
                    ContractItemExemptionExt ext = new ContractItemExemptionExt();
                    ext.setFeeDesc(actMap.get(activityInfo.getActId()));
                    dto.setExt(JSON.toJSONString(ext));
                }
            }
            result.add(dto);
        }
        return result;
    }

    private String getGoodsFeeDetail(List<ContractItemFeeDto> itemFeeList, List<ContractItemExemptionDto> exemptionItemList) {
        List<ContractItemFeeDto> feeList = itemFeeList.stream().map(fee -> {
            ContractItemFeeDto contractItemFeeDto = new ContractItemFeeDto();
            contractItemFeeDto.setAmountType(fee.getAmountType());
            contractItemFeeDto.setAmount(fee.getAmount());
            contractItemFeeDto.setNum(fee.getNum());
            contractItemFeeDto.setUnitPrice(fee.getUnitPrice());
            return contractItemFeeDto;
        }).collect(Collectors.toList());
        List<ContractItemFeeDto> exemptionList = exemptionItemList.stream().map(exemption -> {
            ContractItemFeeDto contractItemFeeDto = new ContractItemFeeDto();
            contractItemFeeDto.setAmountType(exemption.getAmountType());
            contractItemFeeDto.setAmount(exemption.getExemptAmount());
            return contractItemFeeDto;
        }).collect(Collectors.toList());
        feeList.addAll(exemptionList);
        return JSON.toJSONString(feeList);
    }

}
