package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.DiscountFlexiblePricing;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class DiscountFlexiblePricingBo {
    private String days;  //满多少天  天数
    private String minDiscount;   //最小折扣
    private String maxDiscount;   //最大折扣

    public static DiscountFlexiblePricingBo from(DiscountFlexiblePricing item) {
        DiscountFlexiblePricingBo bo = new DiscountFlexiblePricingBo();
        bo.setDays(item.getDays());
        bo.setMinDiscount(item.getMinDiscount());
        bo.setMaxDiscount(item.getMaxDiscount());
        return bo;
    }
}
