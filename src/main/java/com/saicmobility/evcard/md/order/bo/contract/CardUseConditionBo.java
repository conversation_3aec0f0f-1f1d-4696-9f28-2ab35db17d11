package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.CardUseCondition;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CardUseConditionBo {

    private Integer cardUseStatus; // 会员卡使用状态 1未选择会员卡 2选择了但不满足使用条件 3选择了且满足使用条件件
    private BigDecimal cardDeductionAmount; //  会员卡抵扣金额

    public CardUseCondition toRes() {
        return CardUseCondition.newBuilder()
                .setCardUseStatus(cardUseStatus)
                .setCardDeductionAmount(BigDecimalUtil.toBigDecimalNoDecimalPoint(cardDeductionAmount).doubleValue())
                .build();
    }
}
