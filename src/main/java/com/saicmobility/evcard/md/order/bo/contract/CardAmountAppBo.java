package com.saicmobility.evcard.md.order.bo.contract;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.mdorderservice.api.CardAmount;
import com.saicmobility.evcard.md.mdorderservice.api.CardAmountItem;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

@Data
public class CardAmountAppBo {


      // 购买折扣卡金额
    private BigDecimal cardAmount ;
     // 折扣卡明细
    private List<CardAmountItemAppBo> item ;


    public CardAmount toRes() {
        return CardAmount.newBuilder()
                .setCardAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(cardAmount.toPlainString()))
                .addAllItem(getAllItem(item))
                .build();
    }

    private List<CardAmountItem> getAllItem(List<CardAmountItemAppBo> itemList) {
        return itemList.stream().map(item -> CardAmountItem.newBuilder()
                .setCardId(item.getCardId())
                .setAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(item.getAmount().toPlainString()))
                .setCardName(item.getCardName())
                .build()).collect(Collectors.toList());
    }
}
