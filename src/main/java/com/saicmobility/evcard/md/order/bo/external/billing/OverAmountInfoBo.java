package com.saicmobility.evcard.md.order.bo.external.billing;

import com.saicmobility.evcard.md.mdbillingservice.api.OverAmountInfo;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OverAmountInfoBo {

    // 根据item表的amountType
    private Integer amountType ;
    // 金额
    private BigDecimal amount ;
    // 数量（天数、小时）
    private int num;

    public static OverAmountInfoBo parse(OverAmountInfo info) {
        OverAmountInfoBo bo = new OverAmountInfoBo();
        bo.setAmountType(info.getAmountType());
        bo.setAmount(new BigDecimal(info.getAmount()));
        bo.setNum(info.getNum());
        return bo;
    }
}
