package com.saicmobility.evcard.md.order.contract.flow.chain;

import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.enums.TriggerType;
import com.saicmobility.evcard.md.order.contract.flow.enums.ActionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程链构建器
 * 负责构建标准的合同处理流程链
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Component
public class FlowChainBuilder {
    
    /**
     * 构建标准的合同处理流程链
     * 
     * @return 流程链
     */
    public FlowChain buildStandardChain() {
        List<FlowNode> nodes = new ArrayList<>();
        
        // 1. 初始状态 -> 验车
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.INIT,
                ActionType.VEHICLE_CHECK,
                ContractProcessStateEnum.VEHICLE_CHECKING,
                ContractProcessStateEnum.VEHICLE_CHECK_FAILED
        ).setDescription("开始验车流程"));
        
        // 2. 验车中 -> 等待验车结果
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.VEHICLE_CHECKING,
                ActionType.NONE,
                ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED,
                ContractProcessStateEnum.VEHICLE_CHECK_FAILED
        ).setDescription("等待验车结果"));
        
        // 3. 验车完成 -> 发送短信
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED,
                ActionType.SMS_SEND,
                ContractProcessStateEnum.SMS_SENDING,
                ContractProcessStateEnum.SMS_SEND_FAILED
        ).setDescription("开始短信发送流程"));
        
        // 4. 短信发送中 -> 等待短信结果
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SMS_SENDING,
                ActionType.NONE,
                ContractProcessStateEnum.SMS_SEND_COMPLETED,
                ContractProcessStateEnum.SMS_SEND_FAILED
        ).setDescription("等待短信发送结果"));
        
        // 5. 短信发送完成 -> 盖章
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SMS_SEND_COMPLETED,
                ActionType.SEAL,
                ContractProcessStateEnum.SEALING,
                ContractProcessStateEnum.SEAL_FAILED
        ).setDescription("开始盖章流程"));
        
        // 6. 盖章中 -> 等待盖章结果
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SEALING,
                ActionType.NONE,
                ContractProcessStateEnum.SEAL_COMPLETED,
                ContractProcessStateEnum.SEAL_FAILED
        ).setDescription("等待盖章结果"));
        
        // 7. 盖章完成 -> 流程完成
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SEAL_COMPLETED,
                ActionType.COMPLETE,
                ContractProcessStateEnum.PROCESS_COMPLETED,
                ContractProcessStateEnum.PROCESS_FAILED
        ).setDescription("完成整个流程"));
        
        // 8. 终止节点
        nodes.add(FlowNode.createTerminal(
                ContractProcessStateEnum.PROCESS_COMPLETED,
                "流程成功完成"
        ));
        
        nodes.add(FlowNode.createTerminal(
                ContractProcessStateEnum.PROCESS_FAILED,
                "流程执行失败"
        ));
        
        nodes.add(FlowNode.createTerminal(
                ContractProcessStateEnum.MANUAL_INTERVENTION,
                "需要人工干预"
        ));
        
        FlowChain chain = new FlowChain()
                .setNodes(nodes)
                .setChainName("StandardContractProcessChain");
        
        log.info("构建标准流程链完成，包含 {} 个节点", nodes.size());
        return chain;
    }
    

    
    /**
     * 根据起始状态构建流程链
     * 
     * @param startState 起始状态
     * @return 流程链
     */
    public FlowChain buildChain(ContractProcessStateEnum startState) {
        // 根据不同的起始状态构建不同的流程链
        switch (startState) {
            case INIT:
                return buildStandardChain();
            case VEHICLE_CHECKING:
                return buildChainFromVehicleCheck();
            case SMS_SENDING:
                return buildChainFromSms();
            case SEALING:
                return buildChainFromSeal();
            default:
                log.warn("未支持的起始状态: {}，使用标准流程链", startState);
                return buildStandardChain();
        }
    }
    
    /**
     * 从验车开始的流程链
     */
    private FlowChain buildChainFromVehicleCheck() {
        // 实现从验车开始的流程链
        return buildStandardChain(); // 简化实现
    }
    
    /**
     * 从短信开始的流程链
     */
    private FlowChain buildChainFromSms() {
        // 实现从短信开始的流程链
        return buildStandardChain(); // 简化实现
    }
    
    /**
     * 从盖章开始的流程链
     */
    private FlowChain buildChainFromSeal() {
        // 实现从盖章开始的流程链
        return buildStandardChain(); // 简化实现
    }

    // ==================== 支持不同触发类型的流程链 ====================

    /**
     * 根据起始状态和触发类型构建流程链
     */
    public FlowChain buildChain(ContractProcessStateEnum startState, String triggerType) {
        log.info("构建流程链, startState: {}, triggerType: {}", startState, triggerType);

        List<FlowNode> nodes = new ArrayList<>();

        // 根据触发类型选择不同的流程链
        if (triggerType != null) {
            try {
                TriggerType type = TriggerType.fromCode(triggerType);
                switch (type) {
                    case START_RENTAL:
                        nodes = buildStartRentalChain(startState);
                        break;
                    case END_RENTAL:
                        nodes = buildEndRentalChain(startState);
                        break;
                    default:
                        nodes = buildStandardChain(startState);
                        break;
                }
            } catch (IllegalArgumentException e) {
                log.warn("未知的触发类型: {}, 使用标准流程链", triggerType);
                nodes = buildStandardChain(startState);
            }
        } else {
            nodes = buildStandardChain(startState);
        }

        return new FlowChain().setNodes(nodes);
    }

    /**
     * 根据起始状态构建标准流程链
     */
    private List<FlowNode> buildStandardChain(ContractProcessStateEnum startState) {
        // 根据起始状态构建不同的流程链
        switch (startState) {
            case INIT:
                return buildStandardChain().getNodes();
            case VEHICLE_CHECK_FAILED:
                return buildRetryVehicleCheckChain().getNodes();
            case SMS_SEND_FAILED:
                return buildRetrySmsChain().getNodes();
            case SEAL_FAILED:
                return buildRetrySealChain().getNodes();
            default:
                log.warn("未支持的起始状态: {}", startState);
                return buildStandardChain().getNodes();
        }
    }

    /**
     * 构建发车触发的流程链
     */
    private List<FlowNode> buildStartRentalChain(ContractProcessStateEnum startState) {
        log.info("构建发车触发流程链, startState: {}", startState);

        List<FlowNode> nodes = new ArrayList<>();

        // 发车流程：验车 -> 短信通知 -> 盖章
        switch (startState) {
            case INIT:
                // 1. 初始状态 -> 验车
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.INIT,
                        ActionType.VEHICLE_CHECK,
                        ContractProcessStateEnum.VEHICLE_CHECKING,
                        ContractProcessStateEnum.VEHICLE_CHECK_FAILED
                ).setDescription("发车验车流程"));

                // 2. 验车中 -> 等待验车结果
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.VEHICLE_CHECKING,
                        ActionType.NONE,
                        ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED,
                        ContractProcessStateEnum.VEHICLE_CHECK_FAILED
                ).setDescription("等待发车验车结果"));

                // 3. 验车完成 -> 发送发车通知短信
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED,
                        ActionType.SMS_SEND,
                        ContractProcessStateEnum.SMS_SENDING,
                        ContractProcessStateEnum.SMS_SEND_FAILED
                ).setDescription("发送发车通知短信"));

                // 4. 短信发送中 -> 等待短信结果
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SMS_SENDING,
                        ActionType.NONE,
                        ContractProcessStateEnum.SMS_SEND_COMPLETED,
                        ContractProcessStateEnum.SMS_SEND_FAILED
                ).setDescription("等待发车短信发送结果"));

                // 5. 短信完成 -> 盖章
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SMS_SEND_COMPLETED,
                        ActionType.SEAL,
                        ContractProcessStateEnum.SEALING,
                        ContractProcessStateEnum.SEAL_FAILED
                ).setDescription("发车合同盖章"));

                // 6. 盖章中 -> 等待盖章结果
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SEALING,
                        ActionType.NONE,
                        ContractProcessStateEnum.SEAL_COMPLETED,
                        ContractProcessStateEnum.SEAL_FAILED
                ).setDescription("等待发车盖章结果"));

                // 7. 盖章完成 -> 流程完成
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SEAL_COMPLETED,
                        ActionType.COMPLETE,
                        ContractProcessStateEnum.PROCESS_COMPLETED,
                        ContractProcessStateEnum.PROCESS_FAILED
                ).setDescription("发车流程完成"));
                break;

            default:
                // 对于其他状态，使用标准流程链
                return buildStandardChain(startState);
        }

        return nodes;
    }

    /**
     * 构建收车触发的流程链
     */
    private List<FlowNode> buildEndRentalChain(ContractProcessStateEnum startState) {
        log.info("构建收车触发流程链, startState: {}", startState);

        List<FlowNode> nodes = new ArrayList<>();

        // 收车流程：验车 -> 短信通知 -> 盖章（与发车流程相同，但业务含义不同）
        switch (startState) {
            case INIT:
                // 1. 初始状态 -> 验车
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.INIT,
                        ActionType.VEHICLE_CHECK,
                        ContractProcessStateEnum.VEHICLE_CHECKING,
                        ContractProcessStateEnum.VEHICLE_CHECK_FAILED
                ).setDescription("收车验车流程"));

                // 2. 验车中 -> 等待验车结果
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.VEHICLE_CHECKING,
                        ActionType.NONE,
                        ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED,
                        ContractProcessStateEnum.VEHICLE_CHECK_FAILED
                ).setDescription("等待收车验车结果"));

                // 3. 验车完成 -> 发送收车通知短信
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED,
                        ActionType.SMS_SEND,
                        ContractProcessStateEnum.SMS_SENDING,
                        ContractProcessStateEnum.SMS_SEND_FAILED
                ).setDescription("发送收车通知短信"));

                // 4. 短信发送中 -> 等待短信结果
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SMS_SENDING,
                        ActionType.NONE,
                        ContractProcessStateEnum.SMS_SEND_COMPLETED,
                        ContractProcessStateEnum.SMS_SEND_FAILED
                ).setDescription("等待收车短信发送结果"));

                // 5. 短信完成 -> 盖章
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SMS_SEND_COMPLETED,
                        ActionType.SEAL,
                        ContractProcessStateEnum.SEALING,
                        ContractProcessStateEnum.SEAL_FAILED
                ).setDescription("收车合同盖章"));

                // 6. 盖章中 -> 等待盖章结果
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SEALING,
                        ActionType.NONE,
                        ContractProcessStateEnum.SEAL_COMPLETED,
                        ContractProcessStateEnum.SEAL_FAILED
                ).setDescription("等待收车盖章结果"));

                // 7. 盖章完成 -> 流程完成
                nodes.add(FlowNode.create(
                        ContractProcessStateEnum.SEAL_COMPLETED,
                        ActionType.COMPLETE,
                        ContractProcessStateEnum.PROCESS_COMPLETED,
                        ContractProcessStateEnum.PROCESS_FAILED
                ).setDescription("收车流程完成"));
                break;

            default:
                // 对于其他状态，使用标准流程链
                return buildStandardChain(startState);
        }

        return nodes;
    }

    // ==================== 重试流程链 ====================

    /**
     * 构建验车重试流程链
     */
    private FlowChain buildRetryVehicleCheckChain() {
        List<FlowNode> nodes = new ArrayList<>();

        // 从验车失败状态重新开始验车
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.VEHICLE_CHECK_FAILED,
                ActionType.VEHICLE_CHECK,
                ContractProcessStateEnum.VEHICLE_CHECKING,
                ContractProcessStateEnum.VEHICLE_CHECK_FAILED
        ).setDescription("重试验车流程"));

        // 后续流程与标准流程相同
        nodes.addAll(buildStandardChain().getNodes().subList(1, buildStandardChain().getNodes().size()));

        return new FlowChain().setNodes(nodes);
    }

    /**
     * 构建短信重试流程链
     */
    private FlowChain buildRetrySmsChain() {
        List<FlowNode> nodes = new ArrayList<>();

        // 从短信失败状态重新开始发送短信
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SMS_SEND_FAILED,
                ActionType.SMS_SEND,
                ContractProcessStateEnum.SMS_SENDING,
                ContractProcessStateEnum.SMS_SEND_FAILED
        ).setDescription("重试短信发送"));

        // 后续流程
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SMS_SENDING,
                ActionType.NONE,
                ContractProcessStateEnum.SMS_SEND_COMPLETED,
                ContractProcessStateEnum.SMS_SEND_FAILED
        ).setDescription("等待短信重试结果"));

        // 盖章流程
        List<FlowNode> standardNodes = buildStandardChain().getNodes();
        for (FlowNode node : standardNodes) {
            if (node.getCurrentState() == ContractProcessStateEnum.SMS_SEND_COMPLETED) {
                nodes.addAll(standardNodes.subList(standardNodes.indexOf(node) + 1, standardNodes.size()));
                break;
            }
        }

        return new FlowChain().setNodes(nodes);
    }

    /**
     * 构建盖章重试流程链
     */
    private FlowChain buildRetrySealChain() {
        List<FlowNode> nodes = new ArrayList<>();

        // 从盖章失败状态重新开始盖章
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SEAL_FAILED,
                ActionType.SEAL,
                ContractProcessStateEnum.SEALING,
                ContractProcessStateEnum.SEAL_FAILED
        ).setDescription("重试盖章流程"));

        // 等待盖章结果
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SEALING,
                ActionType.NONE,
                ContractProcessStateEnum.SEAL_COMPLETED,
                ContractProcessStateEnum.SEAL_FAILED
        ).setDescription("等待盖章重试结果"));

        // 完成流程
        nodes.add(FlowNode.create(
                ContractProcessStateEnum.SEAL_COMPLETED,
                ActionType.COMPLETE,
                ContractProcessStateEnum.PROCESS_COMPLETED,
                ContractProcessStateEnum.PROCESS_FAILED
        ).setDescription("流程完成"));

        return new FlowChain().setNodes(nodes);
    }
}
