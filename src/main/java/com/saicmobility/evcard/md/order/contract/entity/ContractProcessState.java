package com.saicmobility.evcard.md.order.contract.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 合同流程状态控制表实体
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_contract_process_state")
public class ContractProcessState {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 流程实例ID，用于区分同一合同的不同流程实例
     */
    private String processInstanceId;

    /**
     * 触发类型：START_RENTAL(发车), END_RENTAL(收车)
     */
    private String triggerType;

    /**
     * 当前状态
     */
    private String currentState;

    /**
     * 上一个状态
     */
    private String previousState;

    /**
     * 流程数据(JSON格式)
     */
    private String processData;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    private LocalDateTime nextRetryTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否需要人工干预
     */
    private Boolean isManualIntervention;

    /**
     * 状态（0=正常   1=已删除）
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createOperId;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createOperName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateOperId;

    /**
     * 更新人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateOperName;

    /**
     * 创建时间（保留原字段，兼容现有代码）
     * @deprecated 请使用 createTime
     */
    @Deprecated
    private LocalDateTime createdTime;

    /**
     * 更新时间（保留原字段，兼容现有代码）
     * @deprecated 请使用 updateTime
     */
    @Deprecated
    private LocalDateTime updatedTime;

    /**
     * 创建人（保留原字段，兼容现有代码）
     * @deprecated 请使用 createOperName
     */
    @Deprecated
    private String createdBy;

    /**
     * 更新人（保留原字段，兼容现有代码）
     * @deprecated 请使用 updateOperName
     */
    @Deprecated
    private String updatedBy;

    /**
     * 版本号，用于乐观锁控制
     */
    @Version
    private Integer version;
}
