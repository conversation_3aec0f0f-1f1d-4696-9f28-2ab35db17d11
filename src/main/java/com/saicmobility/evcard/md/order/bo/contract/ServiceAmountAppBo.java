package com.saicmobility.evcard.md.order.bo.contract;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.mdorderservice.api.ServiceAmount;
import com.saicmobility.evcard.md.mdorderservice.api.ServiceAmountItem;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

@Data
public class ServiceAmountAppBo {

    // 总服务费
    private BigDecimal serviceAmount;
    // 服务费明细列表
    private List<ServiceAmountItemAppBo> serviceAmountItem;

    public ServiceAmount toRes() {
        return ServiceAmount.newBuilder()
                .setServiceAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(serviceAmount))
                .addAllServiceAmountItem(getAllServiceAmountItem(serviceAmountItem))
                .build();
    }

    private List<ServiceAmountItem> getAllServiceAmountItem(List<ServiceAmountItemAppBo> serviceAmountItem) {
        return serviceAmountItem.stream().map(item -> ServiceAmountItem.newBuilder()
                .setFeeName(item.getFeeName())
                .setUnitPrice(Optional.ofNullable(item.getUnitPrice()).map(BigDecimalUtil::toPlainString).orElse(""))
                .setNum(Optional.ofNullable(item.getNum()).map(BigDecimal::intValue).orElse(0))
                .setAmount(Optional.ofNullable(item.getAmount()).map(BigDecimalUtil :: toPlainStringNoDecimalPoint).orElse(""))
                .setAmountType(Optional.ofNullable(item.getAmountType()).orElse(0))
                .setDesc(item.getDesc())
                .build()).collect(Collectors.toList());
    }
}
