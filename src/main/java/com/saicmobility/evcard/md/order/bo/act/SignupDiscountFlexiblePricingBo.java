package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.SignupDiscountFlexiblePricing;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class SignupDiscountFlexiblePricingBo {
    private String days;  // 满多少天  天数
    private String discount; // 折扣
    private String minDiscount; //最小折扣
    private String maxDiscount; //最大折扣

    public static SignupDiscountFlexiblePricingBo from(SignupDiscountFlexiblePricing item) {
        SignupDiscountFlexiblePricingBo bo = new SignupDiscountFlexiblePricingBo();
        bo.setDays(item.getDays());
        bo.setDiscount(item.getDiscount());
        bo.setMinDiscount(item.getMinDiscount());
        bo.setMaxDiscount(item.getMaxDiscount());
        return bo;
    }
}
