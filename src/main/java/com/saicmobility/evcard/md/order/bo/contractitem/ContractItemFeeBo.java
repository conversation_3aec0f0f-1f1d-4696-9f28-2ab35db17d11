package com.saicmobility.evcard.md.order.bo.contractitem;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

@Data
public class ContractItemFeeBo {

    // 租车合同号
    private String contractId;

    // 合同项编号
    private String contractItemId;

    // 金额
    private BigDecimal amount;

    // 费用类型 1租车费-套餐计费 2租车费-标准计费 3燃油费 4畅行服务费 5日租服务费 6车行手续费 7跨门店服务费(异点) 8跨门店服务费(异地)
    // 9送车上门费 10上门取车费 11订单取消违约金  12上门取车取消违约金 13超时还车违约金 14燃油差额(需要退E币)
    private Integer amountType;

    // 支付状态 1待支付 2已取消 3已支付  
    private Integer payStatus;

    // 单价 并不是所有费用都有单价.如果没有固定单价，该值就等于amount即可
    private BigDecimal unitPrice;

    // 数量，unit_price*num = amount
    private BigDecimal num;

    // 减免金额 提前还车时减免租金
    private BigDecimal exemptAmount;

    // 扩展字段
    private String ext;

    // 扩展字段2
    private String ext2;

    private Date createTime;

    // 支付方式 1：线下 2：线上
    private Integer payMode;

    // 扩展字段3
    private String ext3;
}
