package com.saicmobility.evcard.md.order.contract.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 事件处理器注册表
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
@Component
public class EventHandlerRegistry implements ApplicationContextAware {

    private ApplicationContext applicationContext;
    private final Map<String, EventHandler> handlerMap = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        registerHandlers();
    }

    /**
     * 注册所有事件处理器
     */
    private void registerHandlers() {
        log.info("开始注册事件处理器...");
        
        // 从Spring容器中获取所有EventHandler实现
        Map<String, EventHandler> handlers = applicationContext.getBeansOfType(EventHandler.class);
        
        for (Map.Entry<String, EventHandler> entry : handlers.entrySet()) {
            EventHandler handler = entry.getValue();
            String handlerName = handler.getHandlerName();
            
            if (handlerName != null && !handlerName.trim().isEmpty()) {
                handlerMap.put(handlerName, handler);
                log.info("注册事件处理器: {} -> {}", handlerName, handler.getClass().getSimpleName());
            } else {
                log.warn("事件处理器名称为空，跳过注册: {}", handler.getClass().getSimpleName());
            }
        }
        
        log.info("事件处理器注册完成，总数: {}", handlerMap.size());
    }

    /**
     * 获取事件处理器
     * 
     * @param handlerName 处理器名称
     * @return 事件处理器
     */
    public EventHandler getHandler(String handlerName) {
        EventHandler handler = handlerMap.get(handlerName);
        if (handler == null) {
            log.warn("未找到事件处理器: {}", handlerName);
        }
        return handler;
    }

    /**
     * 手动注册事件处理器
     * 
     * @param handlerName 处理器名称
     * @param handler 事件处理器
     */
    public void registerHandler(String handlerName, EventHandler handler) {
        if (handlerName == null || handlerName.trim().isEmpty()) {
            throw new IllegalArgumentException("处理器名称不能为空");
        }
        if (handler == null) {
            throw new IllegalArgumentException("事件处理器不能为空");
        }
        
        handlerMap.put(handlerName, handler);
        log.info("手动注册事件处理器: {} -> {}", handlerName, handler.getClass().getSimpleName());
    }

    /**
     * 注销事件处理器
     * 
     * @param handlerName 处理器名称
     */
    public void unregisterHandler(String handlerName) {
        EventHandler removed = handlerMap.remove(handlerName);
        if (removed != null) {
            log.info("注销事件处理器: {}", handlerName);
        } else {
            log.warn("尝试注销不存在的事件处理器: {}", handlerName);
        }
    }

    /**
     * 检查处理器是否存在
     * 
     * @param handlerName 处理器名称
     * @return 是否存在
     */
    public boolean hasHandler(String handlerName) {
        return handlerMap.containsKey(handlerName);
    }

    /**
     * 获取所有已注册的处理器名称
     * 
     * @return 处理器名称集合
     */
    public java.util.Set<String> getAllHandlerNames() {
        return handlerMap.keySet();
    }

    /**
     * 获取已注册处理器数量
     * 
     * @return 处理器数量
     */
    public int getHandlerCount() {
        return handlerMap.size();
    }

    /**
     * 清空所有处理器
     */
    public void clear() {
        handlerMap.clear();
        log.info("清空所有事件处理器");
    }
}
