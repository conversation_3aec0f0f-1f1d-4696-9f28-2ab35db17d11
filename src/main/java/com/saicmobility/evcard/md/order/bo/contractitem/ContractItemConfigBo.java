package com.saicmobility.evcard.md.order.bo.contractitem;

import com.saicmobility.evcard.md.order.entity.ContractItemConfig;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
public class ContractItemConfigBo {

    // 租车合同号
    private String contractId;

    // 合同项编号
    private String contractItemId;

    // 最大用车时长（单位：天）
    private Integer maxUseDays;

    // 起租天数
    private Integer minRentDays;

    // 油价id
    private Long oilPriceId;

    // 选择的套餐id
    private Long packageId;

    // 价格id
    private Long priceId;

    // 基础服务费id
    private Long serviceId;

    // 用户选择的服务费项，用英文逗号分隔(包含必选项)
    private String chooseServiceId;

    // 订单可立减金额配置id
    private Long canReduceAmountId;

    // 订单可立减金额
    private BigDecimal canReduceAmount;

    // 取消订单配置id
    private Long cancelConfigId;

    // 超时还车配置id
    private Long timeOutConfigId;

    // 巡检超时-继续用车配置id
    private Long inspectorTimeOutUseCarId;

    // 巡检超时-用户取消配置id
    private Long inspectorTimeOutUserCancelId;

    // 巡检超时-巡检取消配置id
    private Long inspectorTimeOutInspectorCancelId;

    // 送车上门服务id
    private Long sendServerId;

    // 上门取车服务id
    private Long pickUpServerId;

    // 合并支付时的购卡记录id
    private Long cardPurchaseId;

    // 随享卡id(还车后更新)
    private Long accompanyingCardId = 0L;

    // 使用随享卡天数(还车后更新)
    private Integer accompanyingCardDays = 0;

    // 零散计费配置id
    private Long hourRentConfigId = 0L;

    // 营销活动编号
    private String activityNo;

    // 营销活动id
    private Long activityId = -1L;

    // 营销活动减免金额
    private BigDecimal activityReduceAmount;

    // 擎路计费方式 每日价格明细
    private String priceDetail;

    // 擎路计费方式 服务费明细
    private String serviceDetail;

    // 自营折扣配置明细
    private String selfOperatedDiscountDetail;

    // 优惠券编号
    private String couponNo;

    private String secondaryCardInfo;

    private String secondaryCardId;

    //价格日历对应参与活动情况
    private String priceExemptionDetail;


    public static ContractItemConfigBo from(ContractItemConfig info) {
        ContractItemConfigBo contractItemConfigBo = new ContractItemConfigBo();
        BeanUtils.copyProperties(info, contractItemConfigBo);
        return contractItemConfigBo;
    }

}
