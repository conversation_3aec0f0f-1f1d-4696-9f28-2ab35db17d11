package com.saicmobility.evcard.md.order.contract.enums;

/**
 * 合同处理流程状态枚举
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
public enum ContractProcessStateEnum {
    
    /**
     * 初始状态 - 等待开始处理
     */
    INIT("INIT", "初始状态"),
    
    /**
     * 验车中 - 正在调用验车接口
     */
    VEHICLE_CHECKING("VEHICLE_CHECKING", "验车中"),
    
    /**
     * 验车完成 - 等待验车回调结果
     */
    VEHICLE_CHECK_COMPLETED("VEHICLE_CHECK_COMPLETED", "验车完成"),
    
    /**
     * 发送短信中 - 正在调用短信发送接口
     */
    SMS_SENDING("SMS_SENDING", "发送短信中"),
    
    /**
     * 短信发送完成 - 等待短信发送回调结果
     */
    SMS_SEND_COMPLETED("SMS_SEND_COMPLETED", "短信发送完成"),
    
    /**
     * 盖章中 - 正在调用盖章接口
     */
    SEALING("SEALING", "盖章中"),
    
    /**
     * 盖章完成 - 等待盖章回调结果
     */
    SEAL_COMPLETED("SEAL_COMPLETED", "盖章完成"),
    
    /**
     * 流程完成 - 所有步骤都已完成
     */
    PROCESS_COMPLETED("PROCESS_COMPLETED", "流程完成"),
    
    /**
     * 验车失败
     */
    VEHICLE_CHECK_FAILED("VEHICLE_CHECK_FAILED", "验车失败"),
    
    /**
     * 短信发送失败
     */
    SMS_SEND_FAILED("SMS_SEND_FAILED", "短信发送失败"),
    
    /**
     * 盖章失败
     */
    SEAL_FAILED("SEAL_FAILED", "盖章失败"),
    
    /**
     * 流程失败 - 整个流程失败
     */
    PROCESS_FAILED("PROCESS_FAILED", "流程失败"),
    
    /**
     * 人工干预 - 需要人工处理
     */
    MANUAL_INTERVENTION("MANUAL_INTERVENTION", "人工干预");
    
    private final String code;
    private final String description;
    
    ContractProcessStateEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取状态
     */
    public static ContractProcessStateEnum fromCode(String code) {
        for (ContractProcessStateEnum state : values()) {
            if (state.getCode().equals(code)) {
                return state;
            }
        }
        throw new IllegalArgumentException("Unknown state code: " + code);
    }
    
    /**
     * 判断是否为失败状态
     */
    public boolean isFailedState() {
        return this == VEHICLE_CHECK_FAILED || 
               this == SMS_SEND_FAILED || 
               this == SEAL_FAILED || 
               this == PROCESS_FAILED;
    }
    
    /**
     * 判断是否为完成状态
     */
    public boolean isCompletedState() {
        return this == PROCESS_COMPLETED;
    }
    
    /**
     * 判断是否为中间状态(处理中)
     */
    public boolean isProcessingState() {
        return this == VEHICLE_CHECKING || 
               this == SMS_SENDING || 
               this == SEALING;
    }
    
    /**
     * 判断是否为等待回调状态
     */
    public boolean isWaitingCallbackState() {
        return this == VEHICLE_CHECK_COMPLETED || 
               this == SMS_SEND_COMPLETED || 
               this == SEAL_COMPLETED;
    }
    
    /**
     * 判断是否需要人工干预
     */
    public boolean needsManualIntervention() {
        return this == MANUAL_INTERVENTION;
    }
}
