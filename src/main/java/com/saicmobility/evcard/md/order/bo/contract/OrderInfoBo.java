package com.saicmobility.evcard.md.order.bo.contract;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.OrderInfo;
import lombok.Data;

import java.util.Date;

@Data
public class OrderInfoBo {

    private String contractId;

    private int contractStatus;

    private String mid;

    private Date createTime;

    private String appVersion;

    private int cancelWay;

    private String externalContractId;

    private String secondAppKey;

    private Date updateTime;

    public OrderInfo toRes() {
        return OrderInfo.newBuilder()
                .setContractId(contractId)
                .setContractStatus(contractStatus)
                .setMid(mid)
                .setCreateTime(DateUtil.dateToString(createTime, DateUtil.DATE_TYPE1))
                .setUpdateTime(DateUtil.dateToString(updateTime, DateUtil.DATE_TYPE1))
                .setAppVersion(appVersion)
                .setCancelWay(cancelWay)
                .setExternalContractId(externalContractId)
                .setSecondAppKey(secondAppKey)
                .build();
    }
}
