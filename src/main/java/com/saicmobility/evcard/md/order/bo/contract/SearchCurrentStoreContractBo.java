package com.saicmobility.evcard.md.order.bo.contract;

import com.extracme.evcard.rpc.util.DateUtil;
import com.saicmobility.evcard.md.mdorderservice.api.*;
import com.saicmobility.evcard.md.order.bo.contractitem.ContractItemConfigBo;
import com.saicmobility.evcard.md.order.bo.external.store.GetStoreDetailInfoForAppBo;
import com.saicmobility.evcard.md.order.bo.modifygoodsmodel.ModifyGoodsModelInfoBo;
import com.saicmobility.evcard.md.order.bo.ofcbuyservice.OfcBuyBaseServiceInfoBo;
import com.saicmobility.evcard.md.order.bo.other.RollMessageBo;
import com.saicmobility.evcard.md.order.bo.vehicle.VehicleDetailInfoForAppBo;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;
import com.saicmobility.evcard.md.order.entity.ContractItemInfo;
import com.saicmobility.evcard.md.order.enums.ItemTypeEnum;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import com.saicmobility.evcard.md.order.utils.OrderUtil;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class SearchCurrentStoreContractBo {

    private int code;

    private String msg;

    private String mid;

    private String contractId; // 合同id

    private Integer contractStatus; //  合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消

    private String planPickUpOrgCode; //  计划取车机构

    private Long planPickUpStoreId; //  计划取车门店id

    private Long planPickUpShopSeq; // 计划取车虚拟门店id (!=0 代表是虚拟门店)

    private String planPickUpStoreName; //  计划取车门店名称

    private String planPickUpDateTime; //  计划取车时间 yyyymmddhhmmss

    private Long planPickUpCityId; //  计划取车城市id

    private String planPickUpCityName; //  计划取车城市名称

    private String planReturnOrgCode; //  计划还车机构

    private Long planReturnStoreId; //  计划还车门店id

    private Long planReturnShopSeq; //  计划还车虚拟门店id (!=0 代表是虚拟门店)

    private String planReturnStoreName; //  计划还车门店名称

    private String planReturnDateTime; //  计划还车时间 yyyymmddhhmmss

    private Long planReturnCityId; //  计划还车城市id

    private String planReturnCityName; //  计划还车城市名称

    private Integer overPlanReturnHours; // 已超计划还车时间时长 (小时) 48小时后有终端、不可开关门

    private String lastReturnDateTime; // 最晚可还车时间

	private String orderRemainTime; // 订单保留最长时间 （取车延迟时展示）yyyymmddhhmmss

    private Integer planUseDays; // 计划用车天数 planReturnDateTime - planPickUpDateTime

    private Integer planUseDaysNew; // 计划用车天数 planReturnDateTime - planPickUpDateTime

    private Integer planUseHoursNew; // 计划用车小时

    private Integer realUseDays; // 实际用车天数 now - billingStartTime

    private Integer realUseDaysNew; // 实际用车天数 now - billingStartTIme

    private Integer realUseHoursNew; // 实际用车小时 now - billingStartTIme

    private Integer forceRenew; // 是否强行续租过 1没有 2有

    private int forceRenewDays; // 强行续租天数

    private Integer chooseSendService; //  // 是否选择了送车上门服务 1是 2否

    private Long sendServiceId; // 送车上门服务id

    private String sendBaiduLongitude; // 送车上门经度

    private String sendBaiduLatitude; // 送车上门维度

    private String sendPointName; // 送车上门取车点名称

    private String sendPointAddress; // 送车上门取车点地址

    private String sendPointDistance; // 送车上门取车点距离，单位：公里

    private String sendAmount; // 送车上门费用

    private Integer choosePickUpService; //  // 选择上门取车服务 1是 2否

    private Long pickUpServiceId; // 上门取车服务id

    private String pickUpBaiduLongitude; // 上门取车经度

    private String pickUpBaiduLatitude; // 上门取车维度

    private String pickUpPointName; // 上门取车取车点名称

    private String pickUpPointAddress; // 上门取车取车点地址

    private String pickUpPointDistance; // 上门取车取车点距离，单位：公里

    private String pickUpAmount; // 上门取车费用

    private Integer pickUpServerOrigin; // 上门取车服务来源 1：下单 2：订单进行中 3：续租

    private Integer isNotActivePickOrder; // 待生效的上门取车服务 1：存在 2：不存在

    private String cityName; // 下单城市名称

    private String realPickUpOrgCode; // 实际取车机构

    private Long realPickUpStoreId; //  实际取车门店id

    private Long realPickUpShopSeq; //  实际取车虚拟门店id (!=0 代表是虚拟门店)

    private String realPickUpStoreName; // 实际取车门店名称

    private String realPickUpTime;  //  实际取车时间 yyyyMMddHHmmss

    private String realReturnOrgCode; // 实际还车机构

    private Long realReturnStoreId; //  实际还车门店id

    private Long realReturnShopSeq; //  实际取车虚拟门店id (!=0 代表是虚拟门店)

    private String realReturnStoreName; // 实际还车门店名称

    private String realReturnTime;  //  实际还车时间 yyyyMMddHHmmss

    private Long goodsModelId; // 商品车型id

    private String vin; // 车架号

    private String payOrderNo; // 待支付订单号

    private Integer billingTagType; // 计费标签 1：续租 2：去支付 3：不展示(超过固定天数还车不允许续租)

    private Integer dueTagType; // 即将到期标签 1：是 2：否 (计费天数小于1天)

    private Integer remainOrOverDays; // 剩余天数或已超天数(负的表示还剩，正数表示超时)

    private Integer remainOrOverDaysNew = 0; // 剩余天数或已超天数(负的表示还剩，正数表示超时)

    private Integer remainOrOverHoursNew = 0; // 剩余小时或已超小时(负的表示还剩，正数表示超时)

    private List<RollMessageBo> rollMessage; // 轮播内容

    private Integer isSmqcSerivcePlaceOrder; // 是否可下上门取车服务 1：是 2：否 (判断还车门店是否支持购买上门取车)

    //------巡检迟到后 可获得赔偿------ contractStatus = 1, 且 now > planPickUpDateTime 有值
    private String continueUseCarCouponAmount; // 继续用车后可获得优惠券面额

    private String continueUseCarReduceAmount; // 继续用车后可减免金额

    private Integer continueUseCarType; // 备车、取车、用车中卡片状态 1：正常 2：延迟，请耐心等待 3：延迟，赔偿

    private String currentTime; // 当前时间 yyyyMMddHHmmss (app判断使用)

    private Integer isOpenDoorSuccess; // 是否有开门成功记录 1：有 2：否

    private String billingStartTime; // 计费开始时间

    private String crossStoreAmount; // 跨门店服务费

	private int stagingNum; // 续租时默认展示之前已选花呗分期数

    private Integer returnTimeType; // 还车超时类型，1-未超时、2-超过预计还车时间、3-超过最晚还车时间；

    private String damageAmount; // 超时违约金，当returnTimeType为3时才有此值，格式24.00；

    private OfcBuyBaseServiceInfoBo ofcBuyBaseServiceInfoBo; // 履约加购基本服务费

    //企业支付标识 1:个人支付  2:企业支付
    private int businessFree;

    // 修改车型提示语（5分钟）
    private String modifyGoodsModelMsg;

    private VehicleDetailInfoForAppBo vehicleDetailInfoForAppBo;// 车型详情

    // 修改车型信息
    private ModifyGoodsModelInfoBo modifyGoodsModelInfoBo;

    // 下单时选择的服务id
    private List<Integer> chooseServiceIds;
    private long vehicleModelId;

    private int isUseEarlyBird; // 是否使用了早鸟套餐：1-是、2-否

    private long fragmentHourConfigId; // 零散小时规则id

    private int mdModelIsDuplicate; // 门店车型价格是否相同1：默认相同 2：不相同

    private int billingType; // 是否擎路 计费方式 1：是  2：否

    private String renewTime;// 续租还车时间

    private int isExistHassleFreeCharge=0;//0：不存在充电无忧服务，1：存在已支付的充电无忧服务，2存在待支付的充电无忧服务，

    private int transferContractType=0;// 0:普通evcard订单 1：转移订单evcard个人订单 2：转移订单evcard企业订单 3：转移订单员工用车 4转移订单VIP用车

    /**
     *
     * @param contractInfo 合同信息
     * @param
     * @param itemType2Map 合同项信息
     * @param sendItemConfig 送车上门配置
     * @param pickUpItemConfig 上门取车
     */
    public void parse(ContractInfoBo contractInfo,
                      GetStoreDetailInfoForAppBo planPickUpStoreInfo,
                      GetStoreDetailInfoForAppBo planReturnStoreInfo,
                      GetStoreDetailInfoForAppBo realPickUpStoreInfo,
                      GetStoreDetailInfoForAppBo realReturnStoreInfo,
                      Map<Integer, ContractItemInfo> itemType2Map,
                      ContractItemConfigBo sendItemConfig,
                      ContractItemConfigBo pickUpItemConfig,
                      LocalDateTime nowLocalDateTime,
                      String overTimeDamageAmount) {
        setContractId(contractInfo.getContractId());
        setContractStatus(contractInfo.getContractStatus());

        // 计划取车门店信息
        setPlanPickUpOrgCode(planPickUpStoreInfo.getOrgCode());
        setPlanPickUpStoreId(planPickUpStoreInfo.getId());
        setPlanPickUpShopSeq(planPickUpStoreInfo.getShopSeq());
        setPlanPickUpStoreName(planPickUpStoreInfo.getStoreName());
        setPlanPickUpDateTime(DateUtil.dateToString(contractInfo.getPlanPickUpDateTime(), DateUtil.DATE_TYPE4));
        setPlanPickUpCityId(StringUtils.isEmpty(planPickUpStoreInfo.getCityCode()) ? 0 : Long.parseLong(planPickUpStoreInfo.getCityCode()));
        setPlanPickUpCityName(planPickUpStoreInfo.getCityName());

        // 计划还车门店信息
        setPlanReturnOrgCode(planReturnStoreInfo.getOrgCode());
        setPlanReturnStoreId(planReturnStoreInfo.getId());
        setPlanReturnShopSeq(planReturnStoreInfo.getShopSeq());
        setPlanReturnStoreName(planReturnStoreInfo.getStoreName());
        setPlanReturnDateTime(DateUtil.dateToString(contractInfo.getPlanReturnDateTime(), DateUtil.DATE_TYPE4));
        setPlanReturnCityId(StringUtils.isEmpty(planReturnStoreInfo.getCityCode()) ? 0 : Long.parseLong(planReturnStoreInfo.getCityCode()));
        setPlanReturnCityName(planReturnStoreInfo.getCityName());

        setLastReturnDateTime(DateUtil.dateToString(contractInfo.getLastReturnDateTime(), DateUtil.DATE_TYPE4));


        // 如果有实际计费时间，使用实际计费时间；如果没有就使用计划取车时间
        LocalDateTime billingStartTime = contractInfo.getBillingStartTime();
        LocalDateTime billingEndTime = contractInfo.getBillingEndTime();
        LocalDateTime planPickUpDateTime = contractInfo.getPlanPickUpDateTime();
        LocalDateTime planReturnDateTime = contractInfo.getPlanReturnDateTime();
        long betweenMinutes;
        if (billingStartTime != null) {
            if (billingEndTime != null) {
                betweenMinutes = Duration.between(billingStartTime, billingEndTime).toMinutes();
            } else {
                betweenMinutes = Duration.between(billingStartTime, LocalDateTime.now()).toMinutes();
            }
        } else {
            betweenMinutes = Duration.between(planPickUpDateTime, LocalDateTime.now()).toMinutes();
        }
        if (betweenMinutes < 0) {
            setRealUseDays(0);
        } else {
            setRealUseDays((int)Math.ceil(betweenMinutes / (24 * 60D)));

            setRealUseDaysNew((int)(betweenMinutes / (24 * 60D)));
            setRealUseHoursNew(Math.max((int) Math.ceil((betweenMinutes / (60D)) % 24), 1));
            if (getRealUseHoursNew() == 24) {
                setRealUseHoursNew(0);
                setRealUseDaysNew(getRealUseDaysNew() + 1);
            }
        }

        betweenMinutes = Duration.between(planPickUpDateTime, planReturnDateTime).toMinutes();
        setPlanUseDays((int) Math.ceil(betweenMinutes / (24 * 60D)));

        setPlanUseDaysNew((int) (betweenMinutes / (24 * 60D)));

        setPlanUseHoursNew(OrderUtil.getFragmentHour(planPickUpDateTime, planReturnDateTime));

        setForceRenew(contractInfo.getForceRenew());

        ContractItemInfo sendItemInfo = itemType2Map.get(ItemTypeEnum.ITEM_TYPE_SEND_CAR.getValue());
        if (sendItemInfo != null) {
            setChooseSendService(BusinessConstants.ENABLE);
            setSendServiceId(sendItemConfig.getSendServerId());
            setSendBaiduLongitude(sendItemInfo.getServerPointLon().toString());
            setSendBaiduLatitude(sendItemInfo.getServerPointLat().toString());
            setSendPointName(sendItemInfo.getServerPointName());
            setSendPointAddress(sendItemInfo.getServerPointAddress());
            setSendPointDistance(sendItemInfo.getServerPointDistance().toPlainString());
            setSendAmount(sendItemInfo.getTotalAmount().toPlainString());
        } else {
            setChooseSendService(BusinessConstants.DIS_ENABLE);
        }

        ContractItemInfo pickUpItemInfo = itemType2Map.get(ItemTypeEnum.ITEM_TYPE_PICK_CAR.getValue());
        if (pickUpItemInfo != null) {
            setChoosePickUpService(BusinessConstants.ENABLE);
            setPickUpServiceId(pickUpItemConfig.getSendServerId());
            setPickUpBaiduLongitude(pickUpItemInfo.getServerPointLon().toString());
            setPickUpBaiduLatitude(pickUpItemInfo.getServerPointLat().toString());
            setPickUpPointName(pickUpItemInfo.getServerPointName());
            setPickUpPointDistance(pickUpItemInfo.getServerPointDistance().toPlainString());
            setPickUpServerOrigin(pickUpItemInfo.getServerOrigin());
            setPickUpAmount(pickUpItemInfo.getTotalAmount().toPlainString());
            setPickUpPointAddress(pickUpItemInfo.getServerPointAddress());
        } else {
            setChoosePickUpService(BusinessConstants.DIS_ENABLE);
        }

        // 实际取车门店
        if (realPickUpStoreInfo != null) {
            setRealPickUpOrgCode(realPickUpStoreInfo.getOrgCode());
            setRealPickUpStoreName(realPickUpStoreInfo.getStoreName());
        }
        setRealPickUpStoreId(contractInfo.getRealPickUpStoreId());
        setRealPickUpShopSeq(contractInfo.getRealPickUpShopSeq());
        if (!StringUtils.isEmpty(contractInfo.getRealPickUpTime())) {
            setRealPickUpTime(DateUtil.dateToString(contractInfo.getRealPickUpTime(), DateUtil.DATE_TYPE4));
        }

        // 实际还车门店
        if (realReturnStoreInfo != null) {
            setRealReturnOrgCode(realReturnStoreInfo.getOrgCode());
            setRealReturnStoreName(realReturnStoreInfo.getStoreName());
        }
        setRealReturnStoreId(contractInfo.getRealReturnStoreId());
        setRealReturnShopSeq(contractInfo.getRealReturnShopSeq());
        if (!StringUtils.isEmpty(contractInfo.getRealReturnTime())) {
            setRealReturnTime(DateUtil.dateToString(contractInfo.getRealReturnTime(), DateUtil.DATE_TYPE4));
        }

        setGoodsModelId(contractInfo.getGoodsModelId());
        setVin(contractInfo.getVin());

        if (nowLocalDateTime.isAfter(contractInfo.getLastReturnDateTime())) {
            setReturnTimeType(3);
            setDamageAmount(overTimeDamageAmount);
        } else if (nowLocalDateTime.isAfter(contractInfo.getPlanReturnDateTime())) {
            setReturnTimeType(2);
        } else {
            setReturnTimeType(1);
        }
    }

    public SearchCurrentStoreContractRes toRes() {
        if (code != 0) {
            return SearchCurrentStoreContractRes.newBuilder().build();
        }
        SearchCurrentStoreContractRes.Builder builder = SearchCurrentStoreContractRes.newBuilder()
                .setContractId(getContractId())
                .setContractStatus(getContractStatus() == null ? 0 : getContractStatus())
                .setPlanPickUpOrgCode(getPlanPickUpOrgCode())
                .setPlanPickUpStoreId(getPlanPickUpStoreId() == null ? 0 : getPlanPickUpStoreId())
                .setPlanPickUpShopSeq(getPlanPickUpShopSeq() == null ? 0 : getPlanPickUpShopSeq())
                .setPlanPickUpStoreName(getPlanPickUpStoreName())
                .setPlanPickUpDateTime(getPlanPickUpDateTime())
                .setPlanPickUpCityId(getPlanPickUpCityId() == null ? 0 : getPlanPickUpCityId())
                .setPlanPickUpCityName(getPlanPickUpCityName())
                .setPlanReturnOrgCode(getPlanReturnOrgCode())
                .setPlanReturnStoreId(getPlanReturnStoreId() == null ? 0 : getPlanReturnStoreId())
                .setPlanReturnShopSeq(getPlanReturnShopSeq() == null ? 0 : getPlanReturnShopSeq())
                .setPlanReturnStoreName(getPlanReturnStoreName())
                .setPlanReturnCityId(getPlanReturnCityId() == null ? 0 : getPlanReturnCityId())
                .setPlanReturnCityName(getPlanReturnCityName())
                .setPlanReturnDateTime(getPlanReturnDateTime())
                .setOverPlanReturnHours(getOverPlanReturnHours() == null ? 0 : getOverPlanReturnHours())
                .setLastReturnDateTime(getLastReturnDateTime())
				.setOrderRemainTime(getOrderRemainTime())
                .setPlanUseDays(getPlanUseDays() == null ? 0 : getPlanUseDays())
                .setPlanUseDaysNew(getPlanUseDaysNew() == null ? 0 : getPlanUseDaysNew())
                .setPlanUseHoursNew(getPlanUseHoursNew() == null ? 0 : getPlanUseHoursNew())
                .setRealUseDays(getRealUseDays() == null ? 0 : getRealUseDays())
                .setRealUseDaysNew(getRealUseDaysNew() == null ? 0 : getRealUseDaysNew())
                .setRealUseHoursNew(getRealUseHoursNew() == null ? 0 : getRealUseHoursNew())
                .setForceRenew(getForceRenew() == null ? 1 : getForceRenew())
                .setForceRenewDays(getForceRenewDays())
                .setChooseSendService(getChooseSendService() == null ? BusinessConstants.DIS_ENABLE : getChooseSendService())
                .setSendServiceId(getSendServiceId() == null ? 0 : getSendServiceId())
                .setSendBaiduLongitude(getSendBaiduLongitude())
                .setSendBaiduLatitude(getSendBaiduLatitude())
                .setSendPointName(getSendPointName())
                .setSendPointAddress(getSendPointAddress())
                .setSendPointDistance(getSendPointDistance())
                .setSendAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(getSendAmount()))
                .setChoosePickUpService(getChoosePickUpService() == null ? BusinessConstants.DIS_ENABLE : getChoosePickUpService())
                .setPickUpServiceId(getPickUpServiceId() == null ? 0 : getPickUpServiceId())
                .setPickUpBaiduLongitude(getPickUpBaiduLongitude())
                .setPickUpBaiduLatitude(getPickUpBaiduLatitude())
                .setPickUpPointName(getPickUpPointName())
                .setPickUpPointAddress(getPickUpPointAddress())
                .setPickUpPointDistance(getPickUpPointDistance())
                .setPickUpAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(getPickUpAmount()))
                .setPickUpServerOrigin(getPickUpServerOrigin() == null ? 0 : getPickUpServerOrigin())
                .setIsNotActivePickOrder(getIsNotActivePickOrder() == null ? 0 : getIsNotActivePickOrder())
                .setCityName(getCityName())
                .setRealPickUpOrgCode(getRealPickUpOrgCode())
                .setRealPickUpStoreId(getRealPickUpStoreId() == null ? 0 : getRealPickUpStoreId())
                .setRealPickUpShopSeq(getRealPickUpShopSeq() == null ? 0 : getRealPickUpShopSeq())
                .setRealPickUpStoreName(getRealPickUpStoreName())
                .setRealPickUpTime(getRealPickUpTime())
                .setRealReturnOrgCode(getRealReturnOrgCode())
                .setRealReturnStoreId(getRealReturnStoreId() == null ? 0 : getRealReturnStoreId())
                .setRealReturnStoreName(getRealReturnStoreName())
                .setRealReturnTime(getRealReturnTime())
                .setRealReturnShopSeq(getRealReturnShopSeq() == null ? 0 : getRealReturnShopSeq())
                .setGoodsModelId(getGoodsModelId() == null ? 0 : getGoodsModelId())
                .setVin(getVin())
                .setPayOrderNo(getPayOrderNo())
                .setBillingTagType(getBillingTagType() == null ? 0 : getBillingTagType())
                .setDueTagType(getDueTagType() == null ? 0 : getDueTagType())
                .setRemainOrOverDays(getRemainOrOverDays() == null ? 0 : getRemainOrOverDays())
                .setRemainOrOverDaysNew(getRemainOrOverDaysNew() == null ? 0 : getRemainOrOverDaysNew())
                .setRemainOrOverHoursNew(getRemainOrOverHoursNew() == null ? 0 : getRemainOrOverHoursNew())
                .setIsSmqcSerivcePlaceOrder(getIsSmqcSerivcePlaceOrder() == null ? 0 : getIsSmqcSerivcePlaceOrder())
                .setContinueUseCarCouponAmount(getContinueUseCarCouponAmount())
                .setContinueUseCarReduceAmount(getContinueUseCarReduceAmount())
                .setContinueUseCarType(getContinueUseCarType() == null ? 0 : getContinueUseCarType())
                .setCurrentTime(DateUtil.dateToString(new Date(), DateUtil.DATE_TYPE4))
                .setBillStartTime(getBillingStartTime())
				.setCrossStoreAmount(getCrossStoreAmount())
                .setStagingNum(getStagingNum())
                .setReturnTimeType(getReturnTimeType())
                .setVehicleModelId(getVehicleModelId())
                .setDamageAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(getDamageAmount()));

        List<RollMessage> rollMessageForRes = getRollMessageForRes();
        if (!CollectionUtils.isEmpty(rollMessageForRes)) {
            builder.addAllRollMessage(getRollMessageForRes());
        }
        if (getOfcBuyBaseServiceInfoBo() != null && getOfcBuyBaseServiceInfoBo().getPayStatus() == 1) {
            BaseServiceInfo.Builder builder1 = BaseServiceInfo.newBuilder();
            builder1.setPayStatus(getOfcBuyBaseServiceInfoBo().getPayStatus());
            if (getOfcBuyBaseServiceInfoBo().getCancelTime() != null) {
                builder1.setCancelTime(DateUtil.dateToString(getOfcBuyBaseServiceInfoBo().getCancelTime(), DateUtil.DATE_TYPE1));
            }
            if (getOfcBuyBaseServiceInfoBo().getTotalAmount() != null) {
                builder1.setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(getOfcBuyBaseServiceInfoBo().getTotalAmount().toPlainString()));
            }
            if (!CollectionUtils.isEmpty(getOfcBuyBaseServiceInfoBo().getItem())) {
                List<ServiceAmountItem> serviceAmountItemList = getOfcBuyBaseServiceInfoBo().getItem().stream().map(ServiceAmountItemAppBo::toRes).collect(Collectors.toList());
                builder1.addAllItem(serviceAmountItemList);
            }
            builder.setBaserServiceInfo(builder1.build());
        }
        builder.setBusinessFree(getBusinessFree());

        if (getVehicleDetailInfoForAppBo() != null) {
            builder.setVehicleModeInfo(getVehicleDetailInfoForAppBo().toRes());
        }

        if (getModifyGoodsModelInfoBo() != null) {
            ModifyGoodsModelInfo build = ModifyGoodsModelInfo.newBuilder()
                    .setPayStatus(getModifyGoodsModelInfoBo().getPayStatus())
                    .setCancelTime(DateUtil.dateToString(getModifyGoodsModelInfoBo().getCancelTime(), DateUtil.DATE_TYPE1))
                    .setTotalAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(getModifyGoodsModelInfoBo().getTotalAmount().toPlainString()))
                    .setGoodsName(getModifyGoodsModelInfoBo().getGoodsName())
                    .build();
            builder.setModifyGoodsModelInfo(build);
        }
        builder.setModifyGoodsModelMsg(modifyGoodsModelMsg);

        builder.addAllChooseServiceIds(chooseServiceIds);
        builder.setIsUseEarlyBird(isUseEarlyBird);
        builder.setFragmentHourConfigId(fragmentHourConfigId);
        builder.setMdModelIsDuplicate(mdModelIsDuplicate);
        builder.setBillingType(billingType);
        builder.setRenewTime(renewTime);
        builder.setIsExistHassleFreeCharge(isExistHassleFreeCharge);
        builder.setTransferContractType(transferContractType);
        return builder.build();
    }

    private List<RollMessage> getRollMessageForRes(){
        List<RollMessage> rollMessageList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(getRollMessage())) {
            for (RollMessageBo rollMessageBo : getRollMessage()) {
                RollMessage rollMessage = RollMessage.newBuilder()
                        .setColor(rollMessageBo.getColor())
                        .setMessage(rollMessageBo.getMessage())
                        .setType(rollMessageBo.getType() == null ? 0 : rollMessageBo.getType())
						.setClickType(rollMessageBo.getClickType())
                        .build();
                rollMessageList.add(rollMessage);
            }
        }
        return rollMessageList;
    }
}
