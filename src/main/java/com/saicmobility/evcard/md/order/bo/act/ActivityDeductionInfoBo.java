package com.saicmobility.evcard.md.order.bo.act;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.order.bo.external.billing.SelfOperatedActivityDiscountBo;
import com.saicmobility.evcard.md.order.utils.OrderComUtils;
import org.springframework.util.CollectionUtils;

import com.saicmobility.evcard.md.order.bo.external.billing.CalCardInfoBo;
import com.saicmobility.evcard.md.order.bo.external.billing.CalCouponInfoBo;
import com.saicmobility.evcard.md.order.bo.external.billing.CalRenewCardInfoBo;
import com.saicmobility.evcard.md.order.bo.external.billing.ExemptionAmountBo;
import com.saicmobility.evcard.md.order.constants.AmountType;

import lombok.Data;

/**
 * 活动卡券
 */
@Data
public class ActivityDeductionInfoBo {

    //  抵扣总金额
    private String discountTotalAmount = "0";

    // 活动实际立减金额
    private String reduceAmount;

    //  会员卡id	无满足的会员卡，返回-1
    private Long userCardNo;

    //  会员卡名称
    private String userCardName;

    // 会员卡活动id		用户无会员卡时，返回购买会员卡的活动id，无满足的活动时，返回-1
    private Long activityId;

    //  会员卡抵扣金额
    private String userCardDeductibleAmount;

    //  推荐会员卡活动可抵扣金额
    private String recommendCarActivityDeductibleAmount;

    //  可用会员卡数量
    private Integer userCardNum;

    // 会员卡总数
    private Long userCardTotalNum;

    //  会员卡折扣率 1~100的整数
    private Integer discountRate;

    //  优惠券抵扣金额
    private String couponDeductibleAmount;

    // 可使用优惠券金额
    private String canUseCouponRentAmount;

    //  优惠券编号 默认值 -1
    private Long couponSeq;

    //  可用优惠券张数
    private Integer couponNum;

    //  优惠券总数
    private Integer couponTotalNum;

    private ReductionActivityConfigBo reductionActivityConfig;

    // 自营折扣抵扣金额
    private String selfOperatedDiscountAmount;

    private int selfOperatedDiscountType;
    private int selfOperatedDiscountDays;
    private int activityDay;

    // 自营折扣描述
    private String selfOperatedDiscountDesc;

    //-----------续租时使用-------------
    //  会员卡使用状态 续租时使用 1未选择会员卡 2选择了但不满足使用条件 3选择了且满足使用条件 4选择了但金额已达到上限
    private Integer cardUseStatus;
    //会员卡名称-折扣
    private String userCardDiscountName;

    public static ActivityDeductionInfoBo from(CalCardInfoBo calCard, CalCouponInfoBo calCoupon,
                                               List<ExemptionAmountBo> exemptionAmount, ReduceActivityBo reduceActivityBo,
                                               SelfOperatedActivityDiscountBo activityInfo, Map<Long, String> actMap) {
        ActivityDeductionInfoBo bo = new ActivityDeductionInfoBo();
        Map<Integer, BigDecimal> amountType2AmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(exemptionAmount)) {
            amountType2AmountMap = exemptionAmount.stream().collect(Collectors.toMap(ExemptionAmountBo::getAmountType, ExemptionAmountBo::getAmount));
        }

        BigDecimal discountTotalAmount = BigDecimal.ZERO; // 减免总金额

        BigDecimal activityDeductionAmount = amountType2AmountMap.get(AmountType.ACTIVITY_DEDUCTION);
		bo.setReduceAmount(activityDeductionAmount == null || activityDeductionAmount.compareTo(BigDecimal.ZERO) == 0
				? null
				: activityDeductionAmount.toPlainString()); // 立减活动金额
		discountTotalAmount = discountTotalAmount
				.add(activityDeductionAmount == null || activityDeductionAmount.compareTo(BigDecimal.ZERO) == 0
						? BigDecimal.ZERO
						: activityDeductionAmount);

        if (calCard != null) {
            bo.setUserCardNo(calCard.getUserCardNo());
            bo.setUserCardName(calCard.getUserCardName());
            bo.setActivityId(calCard.getActivityId());
            bo.setUserCardDeductibleAmount(calCard.getCardDeductionAmount() == null ? "" : calCard.getCardDeductionAmount().toPlainString());
            bo.setRecommendCarActivityDeductibleAmount(calCard.getRecommendCarActivityDeductibleAmount() == null ? "" :
                    calCard.getRecommendCarActivityDeductibleAmount()
                    .toPlainString());
            bo.setUserCardNum(calCard.getUserCardNum());
            bo.setUserCardTotalNum(calCard.getUserCardTotalNum());
            bo.setDiscountRate(calCard.getDiscountRate());
            bo.setUserCardDiscountName(calCard.getUserCardDiscountName());
            discountTotalAmount = discountTotalAmount.add(calCard.getCardDeductionAmount() == null ? BigDecimal.ZERO : calCard.getCardDeductionAmount());
        }

        if (calCoupon != null) {
            bo.setCouponDeductibleAmount(calCoupon.getCouponDeductionAmount() == null ? "" : calCoupon.getCouponDeductionAmount().toPlainString());
            bo.setCanUseCouponRentAmount(calCoupon.getCanUseCouponRentAmount() == null ? "" : calCoupon.getCanUseCouponRentAmount().toPlainString());
            bo.setCouponSeq(calCoupon.getCouponSeq());
            bo.setCouponNum(calCoupon.getCouponNum());
            bo.setCouponTotalNum(calCoupon.getCouponTotalNum());
            discountTotalAmount = discountTotalAmount.add(calCoupon.getCouponDeductionAmount() == null ? BigDecimal.ZERO : calCoupon.getCouponDeductionAmount());
        }

        if (activityInfo != null && activityInfo.getActId() > 0 && actMap.get(activityInfo.getActId()) != null) {
            bo.setSelfOperatedDiscountAmount(activityInfo.getActRealDiscount());
            bo.setSelfOperatedDiscountDesc(actMap.get(activityInfo.getActId()));
            bo.setSelfOperatedDiscountType(activityInfo.getActivityType());
            bo.setActivityDay(activityInfo.getActivityDay());
            if(activityInfo.getRuler()!= null) {
                bo.setSelfOperatedDiscountDays(activityInfo.getRuler().getDays());
            }
            discountTotalAmount = discountTotalAmount.add(new BigDecimal(activityInfo.getActRealDiscount()));
        }

        bo.setDiscountTotalAmount(discountTotalAmount.toPlainString());

        if (reduceActivityBo != null) {
            ReductionActivityConfigBo reductionActivityConfigBo = new ReductionActivityConfigBo();
            reductionActivityConfigBo.setTitle(reduceActivityBo.getTitle());
            reductionActivityConfigBo.setContent(reduceActivityBo.getContent());
            bo.setReductionActivityConfig(reductionActivityConfigBo);
        }
        return bo;
    }

    public static ActivityDeductionInfoBo from(CalRenewCardInfoBo calCard,
                                               List<ExemptionAmountBo> exemptionAmount, ReduceActivityBo reduceActivityBo) {
        ActivityDeductionInfoBo bo = new ActivityDeductionInfoBo();
        Map<Integer, BigDecimal> amountType2AmountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(exemptionAmount)) {
            amountType2AmountMap = exemptionAmount.stream().collect(Collectors.toMap(ExemptionAmountBo::getAmountType, ExemptionAmountBo::getAmount));
        }

        BigDecimal discountTotalAmount = BigDecimal.ZERO;

        BigDecimal activityDeductionAmount = amountType2AmountMap.get(AmountType.ACTIVITY_DEDUCTION);
        bo.setReduceAmount(activityDeductionAmount == null ? null : activityDeductionAmount.toPlainString()); // 立减活动金额
        discountTotalAmount = discountTotalAmount.add(activityDeductionAmount == null ? BigDecimal.ZERO : activityDeductionAmount);

        if (calCard != null) {
            bo.setUserCardDeductibleAmount(calCard.getCardDeductionAmount() == null ? "0" :
                    calCard.getCardDeductionAmount().toPlainString());
            bo.setUserCardNo(calCard.getUserCardNo());
            bo.setUserCardName(calCard.getUserCardName());
            bo.setUserCardDiscountName(calCard.getUserCardDiscountName());
            bo.setDiscountRate(calCard.getDiscountRate());
            bo.setCardUseStatus(calCard.getCardUseStatus() == null ? 1 : (int) (calCard.getCardUseStatus() + 1)); // 适配billingService

            discountTotalAmount = discountTotalAmount.add(calCard.getCardDeductionAmount() == null ? BigDecimal.ZERO : calCard.getCardDeductionAmount());
        }

        BigDecimal couponDeductionAmount = amountType2AmountMap.get(AmountType.COUPON_DEDUCTION); // 优惠券金额
        bo.setCouponDeductibleAmount(couponDeductionAmount == null ? null : couponDeductionAmount.toPlainString());
        discountTotalAmount = discountTotalAmount.add(couponDeductionAmount == null ? BigDecimal.ZERO : couponDeductionAmount);

        bo.setDiscountTotalAmount(discountTotalAmount.toPlainString());

        if (reduceActivityBo != null && activityDeductionAmount != null && activityDeductionAmount.compareTo(BigDecimal.ZERO) > 0) {
            ReductionActivityConfigBo reductionActivityConfigBo = new ReductionActivityConfigBo();
            reductionActivityConfigBo.setTitle(reduceActivityBo.getTitle());
            reductionActivityConfigBo.setContent(reduceActivityBo.getContent());
            bo.setReductionActivityConfig(reductionActivityConfigBo);
        }
        return bo;
    }
}
