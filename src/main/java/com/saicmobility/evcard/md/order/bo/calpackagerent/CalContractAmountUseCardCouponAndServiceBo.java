package com.saicmobility.evcard.md.order.bo.calpackagerent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.saicmobility.evcard.md.order.bo.external.accompanyingcard.CalAccompanyingCardInfoBo;
import com.saicmobility.evcard.md.order.constants.AmountType;
import com.saicmobility.evcard.md.order.support.CalFeeSupport;
import com.saicmobility.evcard.md.order.utils.AppVersionUtil;
import com.saicmobility.evcard.md.order.utils.ThreadLocalUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.saicmobility.evcard.md.mdorderservice.api.*;
import com.saicmobility.evcard.md.order.bo.act.ActivityDeductionInfoBo;
import com.saicmobility.evcard.md.order.bo.act.ReduceActivityBo;
import com.saicmobility.evcard.md.order.bo.act.ReductionActivityConfigBo;
import com.saicmobility.evcard.md.order.bo.external.billing.*;
import com.saicmobility.evcard.md.order.bo.external.rentpackage.PackageDetailInfoBo;
import com.saicmobility.evcard.md.order.bo.fee.CalFeeDetailBo;
import com.saicmobility.evcard.md.order.bo.fee.FeeDetailsBo;
import com.saicmobility.evcard.md.order.constants.BusinessConstants;

import lombok.Data;

/**
 * 计算套餐、卡券、服务费项返回套餐列表对应的租车费、抵扣金额、服务费、合同总金额
 */
@Data
public class CalContractAmountUseCardCouponAndServiceBo {

    //  预付款金额(应付租金+服务费-减免)
    private String amount;

    //  选中套餐计费
    private CalPackageRentAmountBo packageRentAmount;

    //  标准计费金额(标准租金+服务费)
    private String standardCalAmount;

    //  活动卡券抵扣数据
    private ActivityDeductionInfoBo activityDeduction;

    //  费用明细
    private List<CalFeeDetailBo> feeDetail;

    // 立减活动
    private ReduceActivityBo reduceActivity;

    //  预付款中的租金
    private String rentAmount;

    //  用车时长 单位：分钟
    private Long orderCostTime;

    //  可使用会员卡金额
    private String canUseCardRentAmount;

    //  可使用优惠券金额
    private String canUseCouponRentAmount;

    // 随享卡信息
    private CalAccompanyingCardInfoBo accompanyingCardInfo;
    // 随享卡费用减免列表
    private List<ExemptionAmountBo> accompanyingCardInfoExemptionAmount;

    // V5.7.2费用明细
    private List<CalFeeDetailBo> newFeeDetail;

    // 自营折扣id，无满足的活动时，返回-1
    private long selfOperatedDiscountId = -1;

    public static CalContractAmountUseCardCouponAndServiceBo from(CalculatePrePayFeeBo bo, ReduceActivityBo reduceActivityBo,
                                                                  PackageDetailInfoBo packageDetailInfoBo, CalculateRentAmountBo standardCalculateRentAmountBo,
                                                                  String couponOrigin, boolean isModifyContract, Map<Long, String> actMap) {
        CalContractAmountUseCardCouponAndServiceBo res = new CalContractAmountUseCardCouponAndServiceBo();

        res.setAmount(bo.getOrderRealAmount().toPlainString());

        CalPackageRentAmountBo calPackageRentAmountBo = CalPackageRentAmountBo.from(bo.getCalRent(), standardCalculateRentAmountBo.getAverageAmount());
        if (packageDetailInfoBo != null) {
            calPackageRentAmountBo.setPackageInfo(PackageInfoBo.from(packageDetailInfoBo));
            calPackageRentAmountBo.setActivityType(BusinessConstants.PACKAGE_TYPE_PACK);
        } else {
            PackageInfoBo packageInfoBo = new PackageInfoBo();
            packageInfoBo.setPackageId(BusinessConstants.PACKAGE_STANDARD_ID);
            packageInfoBo.setPackageType(BusinessConstants.PACKAGE_TYPE_STANDARD);
            packageInfoBo.setPackageName("标准计费");
            packageInfoBo.setDaysNumber(standardCalculateRentAmountBo.getTotalRentDays());
            packageInfoBo.setTotalPrice(standardCalculateRentAmountBo.getTotalRentAmount().toPlainString());
            packageInfoBo.setPackageDesc("标准计费-日租");
            calPackageRentAmountBo.setPackageInfo(packageInfoBo);
            calPackageRentAmountBo.setActivityType(BusinessConstants.PACKAGE_TYPE_STANDARD);
        }
        res.setPackageRentAmount(calPackageRentAmountBo);

        res.setStandardCalAmount(bo.getStandardCalAmount().toPlainString());

        ActivityDeductionInfoBo activityDeductionInfoBo = ActivityDeductionInfoBo.from(bo.getCalCard(), bo.getCalCoupon(), bo.getExemptionAmount(),
                reduceActivityBo, bo.getActivityInfo(), actMap);
        res.setActivityDeduction(activityDeductionInfoBo);

        List<CalFeeDetailBo> feeDetail = new ArrayList<>();

        // 租车费
        CalFeeSupport.getRentalFee(feeDetail, calPackageRentAmountBo, packageDetailInfoBo);

        List<ServiceAmountBo> serviceAmount = bo.getServiceAmount();
        if (!CollectionUtils.isEmpty(serviceAmount)) {
            // 燃油费
            CalFeeSupport.getOilFee(feeDetail, serviceAmount);
            // 燃油费差额
            CalFeeSupport.getOilDiffFee(feeDetail, serviceAmount);
            // 基础服务费
            CalFeeSupport.getBaseFee(feeDetail, serviceAmount);
            // 增值服务费
            CalFeeSupport.getAddFee(feeDetail, serviceAmount);
        }

        // 折扣减免
        List<ExemptionAmountBo> exemptionAmount = bo.getExemptionAmount();
        // 小于appv5.12版本，才需要返回折扣减免
        if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) < 0) {
            if (!CollectionUtils.isEmpty(exemptionAmount)) {
                CalFeeSupport.getDisCountFee(feeDetail, exemptionAmount);
            }
        }

        CalCardInfoBo calCard = bo.getCalCard();
        // 购买折扣卡
        CalFeeSupport.getBuyCard(feeDetail, calCard);

        res.setFeeDetail(feeDetail); // 费用明细

		if (reduceActivityBo != null && !StringUtils.isEmpty(reduceActivityBo.getActivityDiscount())) {
			res.setReduceActivity(reduceActivityBo); // 立减活动
		}

        res.setRentAmount(calPackageRentAmountBo.getTotalAmount()); // 预付款中的租金

        res.setOrderCostTime((long) (calPackageRentAmountBo.getRentDay() * 24 * 60)); // 分钟

        if (calCard != null) { //  可使用会员卡金额
            res.setCanUseCardRentAmount(calCard.getCanUseCardRentAmount() != null ? calCard.getCanUseCardRentAmount().toPlainString() : null);
        }
        CalCouponInfoBo calCoupon = bo.getCalCoupon();
        if (calCoupon != null) { //  可使用优惠券金额
            res.setCanUseCouponRentAmount(calCoupon.getCanUseCouponRentAmount() != null ? calCoupon.getCanUseCouponRentAmount().toPlainString() : null);
        }

        res.setAccompanyingCardInfo(bo.getAccompanyingCardInfo());
        res.setAccompanyingCardInfoExemptionAmount(bo.getAccompanyingCardInfoExemptionAmount());

        // V5.7.2新版本费用明细
        List<CalFeeDetailBo> newFeeDetail = new ArrayList<>();
        // 租车费
        // appv5.12版本，增加设置日均租金、优惠前日均租金、租金明细，并把原来feeType=8抵扣的自营折扣、随享卡抵扣（企业折扣）、优惠券，转移到feeType=1租车费里；
        if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) >= 0) {
            CalFeeSupport.getRentalFeeNew(newFeeDetail, calPackageRentAmountBo, bo, couponOrigin, isModifyContract, actMap);
        } else {
            CalFeeSupport.getRentalFee(newFeeDetail, calPackageRentAmountBo, packageDetailInfoBo);
        }

        if (!CollectionUtils.isEmpty(serviceAmount)) {
            // 燃油费
            CalFeeSupport.getOilFee(newFeeDetail, serviceAmount);
            // 燃油费差额
            CalFeeSupport.getOilDiffFee(newFeeDetail, serviceAmount);
            // appv5.12，FeeDetails对象中的feeExplanation，需要把各种费用的 几元几天 随享卡抵扣几天，都拼接好，前端直接使用；
            if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) >= 0) {
                // 保障服务费
                CalFeeSupport.getAssureFeeNew(newFeeDetail, serviceAmount, bo.getAccompanyingCardInfoExemptionAmount(), isModifyContract);
                // 门店手续费
                CalFeeSupport.getStoreProceduresFeeNew(newFeeDetail, serviceAmount, bo.getAccompanyingCardInfoExemptionAmount(), isModifyContract);
            } else {
                // 保障服务费
                CalFeeSupport.getAssureFee(newFeeDetail, serviceAmount);
                // 门店手续费
                CalFeeSupport.getStoreProceduresFee(newFeeDetail, serviceAmount);
            }
            // 增值服务费
            CalFeeSupport.getAddFee(newFeeDetail, serviceAmount);
        }

        // 小于appv5.12版本，才需要返回折扣减免
        if (AppVersionUtil.compareTo(ThreadLocalUtil.getAppVersion(), BusinessConstants.APP_VERSION_5_12_0) < 0) {
            // 折扣减免
            if (!CollectionUtils.isEmpty(exemptionAmount)) {
                CalFeeSupport.getDisCountFee(newFeeDetail, exemptionAmount);
            }
        }

        // 购买折扣卡
        CalFeeSupport.getBuyCard(newFeeDetail, calCard);

        res.setNewFeeDetail(newFeeDetail); // 费用明细

        // 设置自营折扣id
        if (bo.getActivityInfo() != null && bo.getActivityInfo().getActId() > 0) {
            res.setSelfOperatedDiscountId(bo.getActivityInfo().getActId());
        }

        return res;
    }

    public CalContractAmountUseCardCouponAndServiceRes toRes() {
        CalPackageRentAmountBo packageRentAmountBo = getPackageRentAmount();
        PackageInfoBo packageInfoBo = packageRentAmountBo.getPackageInfo();
        CalPackageRentAmount.Builder builder = CalPackageRentAmount.newBuilder()
                .setPackageId(Optional.ofNullable(packageRentAmountBo.getPackageId()).orElse(0L))
                .setPackageAmount(packageRentAmountBo.getPackageAmount())
                .setPackageDayNum(packageRentAmountBo.getPackageDayNum())
                .setOutStandardAmount(packageRentAmountBo.getOutStandardAmount())
                .setOutStandardDayNum(packageRentAmountBo.getOutStandardDayNum())
                .setAverageAmount(packageRentAmountBo.getAverageAmount())
                .setPackageReduceAmount(packageRentAmountBo.getPackageReduceAmount())
                .setRentDay(packageRentAmountBo.getRentDay())
                .setUnitPrice(packageRentAmountBo.getUnitPrice())
                .setActivityType(packageRentAmountBo.getActivityType())
                .setBestMatch(Optional.ofNullable(packageRentAmountBo.getBestMatch()).orElse(0))
                .setTotalAmount(packageRentAmountBo.getTotalAmount())
                .setIsUseEarlyBird(Optional.ofNullable(packageRentAmountBo.getIsUseEarlyBird()).orElse(0));
        Optional.ofNullable(packageInfoBo).ifPresent(bo -> builder.setPackageInfo(PackageInfo.newBuilder()
                .setPackageId(packageInfoBo.getPackageId())
                .setPackageType(packageInfoBo.getPackageType())
                .setPackageName(packageInfoBo.getPackageName())
                .setDaysNumber(packageInfoBo.getDaysNumber())
                .setTotalPrice(packageInfoBo.getTotalPrice())
                .setPackageDesc(packageInfoBo.getPackageDesc())
                .build()));

        CalPackageRentAmount calPackageRentAmount = builder.build();

        BigDecimal accompanyingCardExemptionAmount = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(getAccompanyingCardInfoExemptionAmount())) {
            accompanyingCardExemptionAmount = getAccompanyingCardInfoExemptionAmount().stream().map(ExemptionAmountBo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        ActivityDeductionInfoBo activityDeductionInfoBo = getActivityDeduction();
        ActivityDeductionInfo.Builder activityDeduction = null;
        if (activityDeductionInfoBo != null) {
            ReductionActivityConfigBo reductionActivityConfigBo = activityDeductionInfoBo.getReductionActivityConfig();
            activityDeduction = ActivityDeductionInfo.newBuilder()
                    .setDiscountTotalAmount(new BigDecimal(activityDeductionInfoBo.getDiscountTotalAmount()).add(accompanyingCardExemptionAmount).toPlainString())
                    .setReduceAmount(activityDeductionInfoBo.getReduceAmount() == null ? "0" : activityDeductionInfoBo.getReduceAmount())
                    .setUserCardNo(activityDeductionInfoBo.getUserCardNo() == null ? 0 : activityDeductionInfoBo.getUserCardNo())
                    .setUserCardName(activityDeductionInfoBo.getUserCardName())
                    .setUserCardDiscountName(activityDeductionInfoBo.getUserCardDiscountName())
                    .setActivityId(activityDeductionInfoBo.getActivityId() == null ? 0 : activityDeductionInfoBo.getActivityId())
                    .setUserCardDeductibleAmount(activityDeductionInfoBo.getUserCardDeductibleAmount() == null ? "0" : activityDeductionInfoBo.getUserCardDeductibleAmount())
                    .setRecommendCarActivityDeductibleAmount(activityDeductionInfoBo.getRecommendCarActivityDeductibleAmount() == null ? "0" : activityDeductionInfoBo.getRecommendCarActivityDeductibleAmount())
                    .setUserCardNum(activityDeductionInfoBo.getUserCardNum() == null ? 0 : activityDeductionInfoBo.getUserCardNum())
                    .setUserCardTotalNum(activityDeductionInfoBo.getUserCardTotalNum() == null ? 0 : activityDeductionInfoBo.getUserCardTotalNum())
                    .setDiscountRate(activityDeductionInfoBo.getDiscountRate() == null ? 0 : activityDeductionInfoBo.getDiscountRate())
                    .setCouponDeductibleAmount(activityDeductionInfoBo.getCouponDeductibleAmount() == null ? "0" : activityDeductionInfoBo.getCouponDeductibleAmount())
					.setCanUseCouponRentAmount(activityDeductionInfoBo.getCanUseCouponRentAmount() == null
							? "0"
							: activityDeductionInfoBo.getCanUseCouponRentAmount())
                    .setCouponSeq(activityDeductionInfoBo.getCouponSeq() == null ? 0 : activityDeductionInfoBo.getCouponSeq())
                    .setCouponNum(activityDeductionInfoBo.getCouponNum() == null ? 0 : activityDeductionInfoBo.getCouponNum())
                    .setCouponTotalNum(activityDeductionInfoBo.getCouponTotalNum() == null ? 0 : activityDeductionInfoBo.getCouponTotalNum())
                    .setSelfOperatedDiscountAmount(activityDeductionInfoBo.getSelfOperatedDiscountAmount() == null ? "" : activityDeductionInfoBo.getSelfOperatedDiscountAmount())
                    .setSelfOperatedDiscountType(activityDeductionInfoBo.getSelfOperatedDiscountType())
                    .setSelfOperatedDiscountDays(activityDeductionInfoBo.getSelfOperatedDiscountDays())
                    .setActivityDay(activityDeductionInfoBo.getActivityDay())
                    .setSelfOperatedDiscountDesc(activityDeductionInfoBo.getSelfOperatedDiscountDesc() == null ? "" : activityDeductionInfoBo.getSelfOperatedDiscountDesc());
                    if (reductionActivityConfigBo != null) {
                        activityDeduction.setReductionActivityConfig(ReductionActivityConfig.newBuilder()
                                .setTitle(reductionActivityConfigBo.getTitle())
                                .setContent(reductionActivityConfigBo.getContent()).build())
                                .setCardUseStatus(activityDeductionInfoBo.getCardUseStatus() == null ? 1 : activityDeductionInfoBo.getCardUseStatus());
                    }
        }

        List<CalFeeDetail> feeDetail = new ArrayList<>();
        List<CalFeeDetailBo> feeDetailBo = getFeeDetail();
        if (!CollectionUtils.isEmpty(feeDetailBo)) {
            for (CalFeeDetailBo calFeeDetailBo : feeDetailBo) {
                List<FeeDetailsBo> detailsBo = calFeeDetailBo.getDetails();

                List<FeeDetails> feeDetails = new ArrayList<>();
                if (!CollectionUtils.isEmpty(detailsBo)) {
                    for (FeeDetailsBo feeDetailsBo : detailsBo) {
                        FeeDetails feeDetails1 = FeeDetails.newBuilder()
                                .setFeeName(feeDetailsBo.getFeeName())
                                .setFee(feeDetailsBo.getFee())
                                .setFeeExplanation(feeDetailsBo.getFeeExplanation())
                                .setFeeType(feeDetailsBo.getFeeType() == null ? 0 : feeDetailsBo.getFeeType())
                                .setOriginFee(feeDetailsBo.getOriginFee())
                                .build();
                        feeDetails.add(feeDetails1);
                    }
                }

                CalFeeDetail build = CalFeeDetail.newBuilder()
                        .setFeeName(calFeeDetailBo.getFeeName())
                        .setFee(calFeeDetailBo.getFee())
                        .setFeeType(calFeeDetailBo.getFeeType() == null ? 0 : calFeeDetailBo.getFeeType())
                        .addAllDetails(feeDetails)
                        .build();
                feeDetail.add(build);
            }
        }

        // 新版本费用明细
        List<CalFeeDetail> newFeeDetail = new ArrayList<>();
        List<CalFeeDetailBo> newFeeDetailBo = getNewFeeDetail();
        if (!CollectionUtils.isEmpty(newFeeDetailBo)) {
            for (CalFeeDetailBo calFeeDetailBo : newFeeDetailBo) {
                newFeeDetail.add(calFeeDetailBo.toRes());
            }
        }

		CalContractAmountUseCardCouponAndServiceRes.Builder resBuilder = CalContractAmountUseCardCouponAndServiceRes
				.newBuilder();
		if (activityDeduction != null) {
			resBuilder.setActivityDeduction(activityDeduction.build());
		}

        ReduceActivityBo reduceActivityBo = getReduceActivity();
		ReduceActivity reduceActivity;
        if (reduceActivityBo != null) {
            reduceActivity = ReduceActivity.newBuilder()
                    .setActivityPicture(reduceActivityBo.getActivityPicture())
                    .setTitle(reduceActivityBo.getTitle())
                    .setContent(reduceActivityBo.getContent())
                    .setStartTime(reduceActivityBo.getStartTime())
                    .setEndTime(reduceActivityBo.getEndTime())
                    .setActivityDiscount(reduceActivityBo.getActivityDiscount())
                    .build();
			resBuilder.setReduceActivity(reduceActivity);
        }

        // 随享卡信息
        if (getAccompanyingCardInfo() != null) {
            resBuilder.setAccompanyingCardInfo(getAccompanyingCardInfo().toRes());
        }

        // 随享卡减免明细
        if (!CollectionUtils.isEmpty(getAccompanyingCardInfoExemptionAmount())) {
            ExemptionAmountBo nightServiceItemBo = null;

            List<CalExemptionAmount> calExemptionAmountList = new ArrayList<>();
            for (ExemptionAmountBo itemBo : getAccompanyingCardInfoExemptionAmount()) {
                CalExemptionAmount.Builder builder1 = CalExemptionAmount.newBuilder();

                if (itemBo.getAmountType().equals(AmountType.PICK_UP_NIGHT_AMOUNT) || itemBo.getAmountType().equals(AmountType.RETURN_NIGHT_AMOUNT)) {
                    if (nightServiceItemBo == null) {
                        nightServiceItemBo = itemBo;
                    } else {
                        nightServiceItemBo.setNum(nightServiceItemBo.getNum() + 1);
                        nightServiceItemBo.setAmount(nightServiceItemBo.getAmount().add(itemBo.getAmount()));
                    }
                } else if (itemBo.getAmountType().equals(AmountType.RENT_AMOUNT_STANDARD)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle("车辆租金（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.FREE_TRAVEL_SERVICE_FEE)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.EXCLUSIVE_SERVICE_AMOUNT)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.DAILY_RENT_SERVICE_FEE)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "天）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.VEH_MAINTENANCE_FEE)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "次）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.ADD_OIL_AMOUNT)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "次）");
                    calExemptionAmountList.add(builder1.build());
                } else if (itemBo.getAmountType().equals(AmountType.ADD_ELE_AMOUNT)) {
                    builder1.setAmountType(itemBo.getAmountType());
                    builder1.setAmount(itemBo.getAmount().toPlainString());
                    builder1.setNum(itemBo.getNum());
                    builder1.setUnitPrice(itemBo.getUnitPrice().toPlainString());
                    builder1.setTitle(AmountType.getFeeName(itemBo.getAmountType()) + "（" + itemBo.getNum() + "次）");
                    calExemptionAmountList.add(builder1.build());
                }
            }

            if (nightServiceItemBo != null) {
                CalExemptionAmount.Builder builder1 = CalExemptionAmount.newBuilder();
                builder1.setAmountType(nightServiceItemBo.getAmountType());
                builder1.setAmount(nightServiceItemBo.getAmount().toPlainString());
                builder1.setNum(nightServiceItemBo.getNum());
                builder1.setUnitPrice(nightServiceItemBo.getUnitPrice().toPlainString());
                builder1.setTitle("夜间服务费（" + nightServiceItemBo.getNum() + "次）");
                calExemptionAmountList.add(builder1.build());
            }
            resBuilder.addAllAccompanyingCardInfoExemptionAmount(calExemptionAmountList);
        }

        if (!StringUtils.isEmpty(getAmount())) {
            resBuilder.setAmount(new BigDecimal(getAmount()).compareTo(BigDecimal.ZERO) == 0 ? "0" : getAmount());
        }

        return resBuilder
                .setPackageRentAmount(calPackageRentAmount)
                .setStandardCalAmount(getStandardCalAmount())
                .addAllFeeDetail(feeDetail)
                .addAllNewFeeDetail(newFeeDetail)
                .setRentAmount(getRentAmount())
                .setOrderCostTime(getOrderCostTime())
                .setCanUseCardRentAmount(getCanUseCardRentAmount())
                .setCanUseCouponRentAmount(getCanUseCouponRentAmount())
                .setSelfOperatedDiscountId(getSelfOperatedDiscountId())
                .build();
    }
}
