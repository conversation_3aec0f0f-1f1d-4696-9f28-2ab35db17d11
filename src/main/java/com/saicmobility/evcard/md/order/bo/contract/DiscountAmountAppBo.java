package com.saicmobility.evcard.md.order.bo.contract;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import com.saicmobility.evcard.md.mdorderservice.api.DiscountAmount;
import com.saicmobility.evcard.md.mdorderservice.api.DiscountAmountItem;

import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

@Data
public class DiscountAmountAppBo {
    // 总减免金额
    private BigDecimal serviceAmount;
    // 减免明细列表
    private List<DiscountAmountItemAppBo> discountAmountItem;

    public DiscountAmount toRes() {
        return DiscountAmount.newBuilder()
                .setServiceAmount(BigDecimalUtil.toPlainStringNoDecimalPoint(serviceAmount.toPlainString()))
                .addAllDiscountAmountItem(getAllDiscountAmount(discountAmountItem))
                .build();
    }

    private List<DiscountAmountItem> getAllDiscountAmount(List<DiscountAmountItemAppBo> discountAmountItem) {
        return discountAmountItem.stream().map(discount -> DiscountAmountItem.newBuilder()
                .setFeeName(discount.getFeeName())
                .setAmountType(discount.getAmountType())
                .setUnitPrice(BigDecimalUtil.toPlainStringNoDecimalPoint(discount.getUnitPrice()))
                .build()).collect(Collectors.toList());
    }
}
