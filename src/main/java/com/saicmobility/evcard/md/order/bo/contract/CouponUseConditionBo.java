package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.CouponUseCondition;
import com.saicmobility.evcard.md.order.utils.BigDecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CouponUseConditionBo {

    private Integer couponUseStatus; // 优惠券使用状态 1未选择优惠券 2选择了但不满足使用条件 3选择了且满足使用条件
    private BigDecimal couponDeductionAmount; //  优惠券抵扣金额

    public CouponUseCondition toRes() {
        return CouponUseCondition.newBuilder()
                .setCouponUseStatus(couponUseStatus)
                .setCouponDeductionAmount(BigDecimalUtil.toBigDecimalNoDecimalPoint(couponDeductionAmount).doubleValue())
                .build();
    }
}
