package com.saicmobility.evcard.md.order.bo.external.accompanyingcard;

import com.saicmobility.evcard.md.mduserservice.api.SuiXiangCardBaseInfoForOrder;
import com.saicmobility.evcard.md.mduserservice.api.UnavailableDate;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * mdUser 随享卡信息
 */
@Data
public class SuiXiangCardBaseInfoForOrderBo {

    //  随享卡价格id
    private long cardPriceId;

    // 使用随享卡id
    private long userCardId;

    // 卡名称
    private String name;

    // 随享卡总天数
    private int totalDays;

    // 剩余可用天数
    private int availableDays;

    // 划线价
    private BigDecimal price;

    // 购买价
    private BigDecimal buyPrice;

    // 减免费用类型(订单的amountType)
    private List<Integer> exemptionAmountType;

    // 售卖开始时间，格式 yyyy-MM-dd HH:mm:ss
    private String saleStartTime;

    // 售卖开始时间，格式 yyyy-MM-dd HH:mm:ss
    private String saleEndTime;

    // 使用开始时间，格式 yyyy-MM-dd HH:mm:ss
    private String startTime;

    // 使用结束时间，格式 yyyy-MM-dd HH:mm:ss
    private String endTime;

    // 可用城市id
    private String cityIds;

    // 可用车型id
    private String goodsModelIds;

    // 不可用日期
    private List<UnavailableDateBo> unavailableDate;

    public static SuiXiangCardBaseInfoForOrderBo parse(SuiXiangCardBaseInfoForOrder baseInfoForOrder) {
        SuiXiangCardBaseInfoForOrderBo bo = new SuiXiangCardBaseInfoForOrderBo();
        bo.setCardPriceId(baseInfoForOrder.getCardPriceId());
        bo.setUserCardId(baseInfoForOrder.getUserCardId());
        bo.setName(baseInfoForOrder.getName());
        bo.setTotalDays(baseInfoForOrder.getTotalDays());
        bo.setAvailableDays(baseInfoForOrder.getAvailableDays());
        bo.setPrice(new BigDecimal(baseInfoForOrder.getPrice()));
        bo.setBuyPrice(new BigDecimal(baseInfoForOrder.getBuyPrice()));
        bo.setExemptionAmountType(baseInfoForOrder.getExemptionAmountTypeList());
        bo.setSaleStartTime(baseInfoForOrder.getSaleStartTime());
        bo.setSaleEndTime(baseInfoForOrder.getSaleEndTime());
        bo.setStartTime(baseInfoForOrder.getStartTime());
        bo.setEndTime(baseInfoForOrder.getEndTime());
        bo.setCityIds(baseInfoForOrder.getCityIds());
        bo.setGoodsModelIds(baseInfoForOrder.getGoodsModelIds());

        if (CollectionUtils.isNotEmpty(baseInfoForOrder.getUnavailableDateList())) {
            List<UnavailableDateBo> unavailableDate = new ArrayList<>();
            for (UnavailableDate date : baseInfoForOrder.getUnavailableDateList()) {
                UnavailableDateBo unavailableDateBo = UnavailableDateBo.parse(date);
                unavailableDate.add(unavailableDateBo);
            }
            bo.setUnavailableDate(unavailableDate);
        }
        return bo;
    }
}
