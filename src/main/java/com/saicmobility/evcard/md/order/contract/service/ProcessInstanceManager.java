package com.saicmobility.evcard.md.order.contract.service;

import com.saicmobility.evcard.md.order.contract.dto.ContractProcessContext;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.enums.TriggerType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 流程实例管理器
 * 负责管理同一合同的多个流程实例（发车和收车）
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Service
public class ProcessInstanceManager {

    @Autowired
    private ContractProcessDataService dataService;

    /**
     * 生成流程实例ID
     * 格式：{contractId}_{triggerType}_{timestamp}_{uuid}
     */
    public String generateProcessInstanceId(String contractId, String triggerType) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        return String.format("%s_%s_%s_%s", contractId, triggerType, timestamp, uuid);
    }

    /**
     * 检查是否已存在相同触发类型的流程实例
     */
    public boolean hasExistingInstance(String contractId, String triggerType) {
        ContractProcessState existingState = dataService.selectByContractIdAndTriggerType(contractId, triggerType);
        return existingState != null;
    }

    /**
     * 获取指定触发类型的流程实例
     */
    public ContractProcessState getProcessInstance(String contractId, String triggerType) {
        return dataService.selectByContractIdAndTriggerType(contractId, triggerType);
    }

    /**
     * 初始化流程上下文的实例信息
     */
    public void initializeProcessInstance(ContractProcessContext context) {
        // 如果没有设置触发类型，默认为发车触发
        if (context.getTriggerType() == null) {
            context.setTriggerType(TriggerType.START_RENTAL.getCode());
        }

        // 如果没有设置流程实例ID，生成一个
        if (context.getProcessInstanceId() == null) {
            String processInstanceId = generateProcessInstanceId(context.getContractId(), context.getTriggerType());
            context.setProcessInstanceId(processInstanceId);
        }

        log.info("初始化流程实例, contractId: {}, triggerType: {}, processInstanceId: {}", 
                context.getContractId(), context.getTriggerType(), context.getProcessInstanceId());
    }

    /**
     * 验证流程实例的合法性
     */
    public boolean validateProcessInstance(ContractProcessContext context) {
        try {
            // 验证触发类型
            TriggerType.fromCode(context.getTriggerType());
            
            // 验证必要字段
            if (context.getContractId() == null || context.getContractId().trim().isEmpty()) {
                log.warn("合同ID不能为空");
                return false;
            }
            
            if (context.getProcessInstanceId() == null || context.getProcessInstanceId().trim().isEmpty()) {
                log.warn("流程实例ID不能为空");
                return false;
            }
            
            return true;
            
        } catch (IllegalArgumentException e) {
            log.warn("无效的触发类型: {}", context.getTriggerType());
            return false;
        }
    }

    /**
     * 检查是否可以启动新的流程实例
     */
    public boolean canStartNewInstance(String contractId, String triggerType) {
        // 检查是否已存在相同触发类型的流程实例
        ContractProcessState existingState = dataService.selectByContractIdAndTriggerType(contractId, triggerType);
        
        if (existingState == null) {
            return true; // 没有现有实例，可以启动
        }
        
        // 如果存在实例，检查其状态
        ContractProcessStateEnum currentState =
                ContractProcessStateEnum.fromCode(existingState.getCurrentState());
        
        // 如果现有实例已完成或失败，可以启动新实例
        if (currentState.isCompletedState() || currentState.isFailedState()) {
            log.info("现有流程实例已完成或失败，可以启动新实例, contractId: {}, triggerType: {}, currentState: {}", 
                    contractId, triggerType, currentState);
            return true;
        }
        
        log.warn("已存在进行中的流程实例，无法启动新实例, contractId: {}, triggerType: {}, currentState: {}", 
                contractId, triggerType, currentState);
        return false;
    }

    /**
     * 获取流程实例的显示名称
     */
    public String getInstanceDisplayName(String triggerType) {
        try {
            TriggerType type = TriggerType.fromCode(triggerType);
            return type.getDescription();
        } catch (IllegalArgumentException e) {
            return triggerType;
        }
    }

    /**
     * 检查两个流程实例是否为同一合同的不同触发类型
     */
    public boolean isRelatedInstance(String contractId1, String triggerType1, String contractId2, String triggerType2) {
        return contractId1.equals(contractId2) && !triggerType1.equals(triggerType2);
    }

    /**
     * 获取合同的所有流程实例
     */
    public java.util.List<ContractProcessState> getAllInstancesForContract(String contractId) {
        return dataService.selectByUserId(contractId); // 这里需要修改为按合同ID查询的方法
    }

    /**
     * 清理已完成的流程实例（可选的清理逻辑）
     */
    public void cleanupCompletedInstances(String contractId, int keepDays) {
        log.info("清理已完成的流程实例, contractId: {}, keepDays: {}", contractId, keepDays);
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(keepDays);
        
        // 这里可以实现清理逻辑，比如将旧的已完成实例归档或删除
        // 具体实现根据业务需求决定
    }
}
