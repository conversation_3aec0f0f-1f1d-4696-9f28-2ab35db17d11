package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.FullMinusStandardPricing;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/13
 */
@Data
public class FullMinusStandardPricingBo {
    private String days; //满多少天 天数
    private String discountAmount; //优惠金额

    public static FullMinusStandardPricingBo from(FullMinusStandardPricing item) {
        FullMinusStandardPricingBo bo = new FullMinusStandardPricingBo();
        bo.setDays(item.getDays());
        bo.setDiscountAmount(item.getDiscountAmount());
        return bo;
    }
}
