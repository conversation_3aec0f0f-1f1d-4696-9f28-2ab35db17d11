package com.saicmobility.evcard.md.order.bo.contract;

import com.saicmobility.evcard.md.mdorderservice.api.PickUpVehOrderInfo;
import com.saicmobility.evcard.md.order.entity.ContractItemInfo;

import lombok.Data;

@Data
public class PickUpVehOrderInfoBo {

    private String planReturnPointName; // 计划上门取车点名
    private String planReturnPointAddress; // 计划上门取车点地址
    private String planReturnPointLongitude; // 计划上门取车点经度
    private String planReturnPointLatitude; // 计划上门取车点纬度

    public static PickUpVehOrderInfoBo parse(ContractItemInfo pickUpInfo) {
        PickUpVehOrderInfoBo bo = new PickUpVehOrderInfoBo();
        bo.setPlanReturnPointName(pickUpInfo.getServerPointName());
        bo.setPlanReturnPointAddress(pickUpInfo.getServerPointAddress());
        bo.setPlanReturnPointLongitude(pickUpInfo.getServerPointLon() + "");
        bo.setPlanReturnPointLatitude(pickUpInfo.getServerPointLat() + "");
        return bo;
    }

    public PickUpVehOrderInfo toRes() {
        return PickUpVehOrderInfo.newBuilder()
                .setPlanReturnPointName(planReturnPointName)
                .setPlanReturnPointAddress(planReturnPointAddress)
                .setPlanReturnPointLongitude(planReturnPointLongitude)
                .setPlanReturnPointLatitude(planReturnPointLatitude)
                .build();
    }
}
