package com.saicmobility.evcard.md.order.contract.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 事件处理器配置表实体
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_event_handler_config")
public class EventHandlerConfig {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 处理器名称
     */
    private String handlerName;

    /**
     * 处理器类名
     */
    private String handlerClass;

    /**
     * 执行顺序
     */
    private Integer executionOrder;

    /**
     * 执行模式：SERIAL(串行), PARALLEL(并行)
     */
    private String executionMode;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 超时时间(秒)
     */
    private Integer timeoutSeconds;

    /**
     * 配置数据(JSON格式)
     */
    private String configData;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态（0=正常   1=已删除）
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createOperId;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createOperName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateOperId;

    /**
     * 更新人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateOperName;
}
