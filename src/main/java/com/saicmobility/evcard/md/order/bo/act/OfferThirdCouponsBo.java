package com.saicmobility.evcard.md.order.bo.act;

import com.saicmobility.evcard.md.mdactservice.api.OfferThirdCouponsRes;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/22 22:58
 */
@Data
public class OfferThirdCouponsBo {

    // 0成功
    private int code;

    //  用户优惠券id
    private Long userCouponSeq;

    public static OfferThirdCouponsBo from(OfferThirdCouponsRes offerThirdCouponsRes) {
        OfferThirdCouponsBo offerThirdCouponsBo = new OfferThirdCouponsBo();
        offerThirdCouponsBo.setCode(offerThirdCouponsRes.getRetCode());
        offerThirdCouponsBo.setUserCouponSeq(offerThirdCouponsRes.getUserCouponSeq());
        return offerThirdCouponsBo;
    }
}
