package com.saicmobility.evcard.md.order.bo.external.billing;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** 
* @Desc
* <AUTHOR>
* @Date 2022年6月21日 下午8:42:53
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OilFeeItemBo {
	private String oilUnitPrice;// 油单价
	private Integer totalUseOil;// 总耗油量
	private String oilTotalAmount;// 油量总价
	private String feeItemDesc;// 费用描述
	private String compensateECoin;// 补偿E币
	private Integer feeType;// 费用类型
}

