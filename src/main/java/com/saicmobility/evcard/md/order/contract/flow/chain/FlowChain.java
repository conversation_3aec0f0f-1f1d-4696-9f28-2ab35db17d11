package com.saicmobility.evcard.md.order.contract.flow.chain;

import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.flow.enums.ActionType;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * 流程责任链
 * 定义整个合同处理流程的执行链路
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Data
@Accessors(chain = true)
public class FlowChain {
    
    /**
     * 流程节点列表
     */
    private List<FlowNode> nodes;
    
    /**
     * 流程名称
     */
    private String chainName;

    
    /**
     * 根据当前状态获取下一个需要执行的动作
     * 
     * @param currentState 当前状态
     * @return 下一个动作类型
     */
    public ActionType getNextAction(ContractProcessStateEnum currentState) {
        return getNextAction(currentState, null);
    }
    
    /**
     * 根据当前状态和上下文获取下一个需要执行的动作
     * 
     * @param currentState 当前状态
     * @param context 上下文对象
     * @return 下一个动作类型
     */
    public ActionType getNextAction(ContractProcessStateEnum currentState, Object context) {
        Optional<FlowNode> nodeOpt = findNode(currentState, context);
        
        if (nodeOpt.isPresent()) {
            FlowNode node = nodeOpt.get();
            log.debug("找到匹配节点: {} -> {}", currentState, node.getActionType());
            return node.getActionType();
        }
        
        log.warn("未找到状态 {} 对应的流程节点", currentState);
        return ActionType.NONE;
    }
    
    /**
     * 根据当前状态和动作结果获取下一个状态
     * 
     * @param currentState 当前状态
     * @param actionSuccess 动作是否成功
     * @return 下一个状态
     */
    public ContractProcessStateEnum getNextState(ContractProcessStateEnum currentState, boolean actionSuccess) {
        return getNextState(currentState, actionSuccess, null);
    }
    
    /**
     * 根据当前状态、动作结果和上下文获取下一个状态
     * 
     * @param currentState 当前状态
     * @param actionSuccess 动作是否成功
     * @param context 上下文对象
     * @return 下一个状态
     */
    public ContractProcessStateEnum getNextState(ContractProcessStateEnum currentState, boolean actionSuccess, Object context) {
        Optional<FlowNode> nodeOpt = findNode(currentState, context);
        
        if (nodeOpt.isPresent()) {
            FlowNode node = nodeOpt.get();
            ContractProcessStateEnum nextState = actionSuccess ? node.getSuccessState() : node.getFailureState();
            log.debug("状态转换: {} -> {} (动作成功: {})", currentState, nextState, actionSuccess);
            return nextState;
        }
        
        log.warn("未找到状态 {} 对应的流程节点，保持当前状态", currentState);
        return currentState;
    }
    
    /**
     * 判断流程是否已完成
     * 
     * @param currentState 当前状态
     * @return 是否完成
     */
    public boolean isCompleted(ContractProcessStateEnum currentState) {
        return currentState == ContractProcessStateEnum.PROCESS_COMPLETED ||
               findNode(currentState, null)
                       .map(FlowNode::isTerminal)
                       .orElse(false);
    }
    
    /**
     * 判断流程是否失败
     * 
     * @param currentState 当前状态
     * @return 是否失败
     */
    public boolean isFailed(ContractProcessStateEnum currentState) {
        return currentState == ContractProcessStateEnum.PROCESS_FAILED ||
               currentState.isFailedState();
    }
    
    /**
     * 获取流程中的所有状态
     * 
     * @return 状态列表
     */
    public List<ContractProcessStateEnum> getAllStates() {
        return nodes.stream()
                .map(FlowNode::getCurrentState)
                .distinct()
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 查找匹配的流程节点
     * 
     * @param currentState 当前状态
     * @param context 上下文对象
     * @return 匹配的节点
     */
    private Optional<FlowNode> findNode(ContractProcessStateEnum currentState, Object context) {
        return nodes.stream()
                .filter(node -> node.matches(currentState))
                .filter(node -> node.satisfiesCondition(context))
                .findFirst();
    }
}
