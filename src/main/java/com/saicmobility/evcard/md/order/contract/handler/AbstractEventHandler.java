package com.saicmobility.evcard.md.order.contract.handler;

import com.saicmobility.evcard.md.order.contract.core.EventDrivenStateMachine;
import com.saicmobility.evcard.md.order.contract.core.EventHandler;
import com.saicmobility.evcard.md.order.contract.core.ProcessContext;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象事件处理器
 * 提供通用的处理逻辑
 *
 * <AUTHOR> Assistant
 * @date 2025-09-04
 */
@Slf4j
public abstract class AbstractEventHandler implements EventHandler {

    @Override
    public EventDrivenStateMachine.ProcessResult handle(ProcessContext context) {
        log.info("开始执行处理器: {}, contractId: {}", getHandlerName(), context.getContractId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 前置检查
            EventDrivenStateMachine.ProcessResult preCheckResult = preCheck(context);
            if (!preCheckResult.isSuccess()) {
                return preCheckResult;
            }

            // 执行具体业务逻辑
            EventDrivenStateMachine.ProcessResult result = doHandle(context);
            
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("处理器执行完成: {}, contractId: {}, executionTime: {}ms, success: {}", 
                    getHandlerName(), context.getContractId(), executionTime, result.isSuccess());
            
            return result;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("处理器执行异常: {}, contractId: {}, executionTime: {}ms", 
                    getHandlerName(), context.getContractId(), executionTime, e);
            
            return EventDrivenStateMachine.ProcessResult.failure("HANDLER_EXECUTION_ERROR", 
                    "处理器执行异常: " + e.getMessage());
        }
    }

    /**
     * 前置检查
     * 
     * @param context 处理上下文
     * @return 检查结果
     */
    protected EventDrivenStateMachine.ProcessResult preCheck(ProcessContext context) {
        // 检查基本参数
        if (context == null) {
            return EventDrivenStateMachine.ProcessResult.failure("INVALID_CONTEXT", "处理上下文为空");
        }
        
        if (context.getContractId() == null || context.getContractId().trim().isEmpty()) {
            return EventDrivenStateMachine.ProcessResult.failure("INVALID_CONTRACT_ID", "合同ID为空");
        }
        
        if (context.getProcessInstanceId() == null || context.getProcessInstanceId().trim().isEmpty()) {
            return EventDrivenStateMachine.ProcessResult.failure("INVALID_PROCESS_INSTANCE_ID", "流程实例ID为空");
        }
        
        return EventDrivenStateMachine.ProcessResult.success("前置检查通过");
    }

    /**
     * 执行具体的业务逻辑
     * 子类需要实现此方法
     * 
     * @param context 处理上下文
     * @return 处理结果
     */
    protected abstract EventDrivenStateMachine.ProcessResult doHandle(ProcessContext context);

    /**
     * 获取配置参数
     *
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    protected String getConfigParam(String key, String defaultValue) {
        // 硬编码配置，直接返回默认值
        return defaultValue;
    }

    /**
     * 获取超时时间
     *
     * @return 超时时间（秒）
     */
    protected int getTimeoutSeconds() {
        return 30; // 硬编码默认超时时间
    }

    /**
     * 获取重试次数
     *
     * @return 重试次数
     */
    protected int getRetryCount() {
        return 3; // 硬编码默认重试次数
    }
}
