package com.saicmobility.evcard.md.order.contract.service.impl;

import com.alibaba.fastjson.JSON;
import com.saicmobility.evcard.md.order.contract.dto.ContractProcessContext;
import com.saicmobility.evcard.md.order.contract.dto.ThirdPartyCallbackRequest;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessLog;
import com.saicmobility.evcard.md.order.contract.entity.ContractProcessState;
import com.saicmobility.evcard.md.order.contract.enums.ContractProcessStateEnum;
import com.saicmobility.evcard.md.order.contract.flow.dto.ActionResult;
import com.saicmobility.evcard.md.order.contract.flow.engine.FlowEngine;
import com.saicmobility.evcard.md.order.contract.flow.manager.StateTransitionManager;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessLogMapper;
import com.saicmobility.evcard.md.order.contract.mapper.ContractProcessStateMapper;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessDataService;
import com.saicmobility.evcard.md.order.contract.service.ContractProcessService;
import com.saicmobility.evcard.md.order.contract.service.ProcessInstanceManager;
import com.saicmobility.evcard.md.order.entity.UserContract;
import com.saicmobility.evcard.md.order.mapper.UserContractMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
// import org.springframework.statemachine.StateMachine;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 合同处理服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-09-03
 */
@Slf4j
@Service
public class ContractProcessServiceImpl implements ContractProcessService {

    @Autowired
    private ContractProcessStateMapper contractProcessStateMapper;

    @Autowired
    private ContractProcessLogMapper contractProcessLogMapper;

    @Autowired
    private UserContractMapper userContractMapper;

    @Autowired
    private FlowEngine flowEngine;

    @Autowired
    private StateTransitionManager stateTransitionManager;

    @Autowired
    private ContractProcessDataService dataService;

    @Autowired
    private ProcessInstanceManager processInstanceManager;

    @Value("${contract.process.max-retry-count:3}")
    private int maxRetryCount;

    @Value("${contract.process.retry-interval:60000}")
    private long retryInterval;

    @Value("${contract.process.callback-timeout:1800000}")
    private long callbackTimeout;

    @Override
    @Transactional
    public boolean startProcess(ContractProcessContext context) {
        try {
            log.info("开始合同处理流程, contractId: {}, userId: {}, triggerType: {}",
                    context.getContractId(), context.getUserId(), context.getTriggerType());

            // 初始化流程实例信息
            processInstanceManager.initializeProcessInstance(context);

            // 验证流程实例
            if (!processInstanceManager.validateProcessInstance(context)) {
                log.warn("流程实例验证失败, contractId: {}", context.getContractId());
                return false;
            }

            // 检查是否可以启动新的流程实例
            if (!processInstanceManager.canStartNewInstance(context.getContractId(), context.getTriggerType())) {
                log.warn("无法启动新的流程实例, contractId: {}, triggerType: {}",
                        context.getContractId(), context.getTriggerType());
                return false;
            }

            // 创建初始状态记录
            ContractProcessState processState = new ContractProcessState()
                    .setContractId(context.getContractId())
                    .setUserId(context.getUserId())
                    .setProcessInstanceId(context.getProcessInstanceId())
                    .setTriggerType(context.getTriggerType())
                    .setCurrentState(ContractProcessStateEnum.INIT.getCode())
                    .setPreviousState(null)
                    .setProcessData(JSON.toJSONString(context))
                    .setRetryCount(0)
                    .setMaxRetryCount(maxRetryCount)
                    .setIsManualIntervention(false)
                    .setVersion(1)
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now())
                    .setCreateOperName(context.getOperator())
                    .setUpdateOperName(context.getOperator());

            contractProcessStateMapper.insert(processState);

            // 记录操作日志
            logOperation(context.getContractId(), "START_PROCESS", null,
                    ContractProcessStateEnum.INIT.getCode(),
                    "SUCCESS", JSON.toJSONString(context), null, null, null, 0L, context.getOperator());

            // 使用流程引擎执行流程
            ActionResult result = flowEngine.executeFlow(context);

            if (result.isSuccess()) {
                log.info("合同处理流程启动成功, contractId: {}", context.getContractId());
                return true;
            } else {
                log.error("合同处理流程启动失败, contractId: {}, error: {}",
                        context.getContractId(), result.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("开始合同处理流程失败, contractId: {}", context.getContractId(), e);

            // 记录失败日志
            logOperation(context.getContractId(), "START_PROCESS", null, null,
                    "FAILED", JSON.toJSONString(context), null, "SYSTEM_ERROR", e.getMessage(), 0L, context.getOperator());

            return false;
        }
    }

    @Override
    @Transactional
    public boolean handleCallback(ThirdPartyCallbackRequest callbackRequest) {
        try {
            log.info("处理第三方回调, contractId: {}, callbackType: {}, status: {}",
                    callbackRequest.getContractId(), callbackRequest.getCallbackType(), callbackRequest.getStatus());

            ContractProcessState processState = dataService.selectByContractId(callbackRequest.getContractId());
            if (processState == null) {
                log.warn("未找到合同处理状态, contractId: {}", callbackRequest.getContractId());
                return false;
            }

            // 解析流程上下文
            ContractProcessContext context = JSON.parseObject(processState.getProcessData(), ContractProcessContext.class);

            // 更新回调结果到上下文
            updateContextWithCallback(context, callbackRequest);

            // 根据回调结果更新状态
            boolean success = "SUCCESS".equals(callbackRequest.getStatus());
            ContractProcessStateEnum currentState =
                    ContractProcessStateEnum.fromCode(processState.getCurrentState());

            // 获取下一个状态
            ContractProcessStateEnum nextState = getCallbackNextState(currentState, success);

            // 状态转换
            stateTransitionManager.transitionTo(callbackRequest.getContractId(), nextState, context);

            // 记录回调日志
            logOperation(callbackRequest.getContractId(), "CALLBACK_" + callbackRequest.getCallbackType(),
                    currentState.getCode(), nextState.getCode(),
                    success ? "SUCCESS" : "FAILED", JSON.toJSONString(callbackRequest),
                    callbackRequest.getThirdPartyRequestId(), callbackRequest.getErrorCode(),
                    callbackRequest.getErrorMessage(), 0L, "SYSTEM");

            // 如果成功，继续执行下一步
            if (success && !nextState.isFailedState() && !nextState.isCompletedState()) {
                ActionResult result = flowEngine.continueFlow(context);
                log.info("回调后继续流程执行结果: {}, contractId: {}", result.isSuccess(), callbackRequest.getContractId());
            }

            return true;

        } catch (Exception e) {
            log.error("处理第三方回调失败, contractId: {}", callbackRequest.getContractId(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean retryProcess(String contractId) {
        try {
            log.info("重试合同处理流程, contractId: {}", contractId);

            ContractProcessState processState = dataService.selectByContractId(contractId);
            if (processState == null) {
                log.warn("未找到合同处理状态, contractId: {}", contractId);
                return false;
            }

            // 检查重试次数
            if (processState.getRetryCount() >= processState.getMaxRetryCount()) {
                log.warn("已达到最大重试次数, contractId: {}, retryCount: {}", 
                        contractId, processState.getRetryCount());
                
                // 转为人工干预
                updateProcessState(contractId, 
                        ContractProcessStateEnum.MANUAL_INTERVENTION.getCode(),
                        "已达到最大重试次数", true, "SYSTEM");
                
                return false;
            }

            // 解析流程数据
            ContractProcessContext context = JSON.parseObject(processState.getProcessData(), ContractProcessContext.class);
            context.setRetryCount(processState.getRetryCount() + 1);

            // 使用流程引擎重试
            ActionResult result = flowEngine.executeFlow(context);

            if (result.isSuccess()) {
                log.info("重试流程成功, contractId: {}", contractId);
                return true;
            } else {
                log.error("重试流程失败, contractId: {}, error: {}", contractId, result.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("重试合同处理流程失败, contractId: {}", contractId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean changeState(String contractId, String targetState, String operator) {
        try {
            log.info("手动修改合同处理状态, contractId: {}, targetState: {}, operator: {}", 
                    contractId, targetState, operator);

            ContractProcessState processState = dataService.selectByContractId(contractId);
            if (processState == null) {
                log.warn("未找到合同处理状态, contractId: {}", contractId);
                return false;
            }

            String previousState = processState.getCurrentState();
            
            // 更新状态
            processState.setPreviousState(previousState)
                      .setCurrentState(targetState)
                      .setUpdateTime(LocalDateTime.now())
                      .setUpdateOperName(operator)
                      .setIsManualIntervention(false);

            contractProcessStateMapper.updateById(processState);

            // 记录操作日志
            logOperation(contractId, "MANUAL_CHANGE_STATE", previousState, targetState, 
                    "SUCCESS", null, null, null, null, 0L, operator);

            // 如果是恢复到处理状态，使用流程引擎继续流程
            ContractProcessStateEnum state =
                    ContractProcessStateEnum.fromCode(targetState);

            if (state.isProcessingState()) {
                ContractProcessContext context = JSON.parseObject(processState.getProcessData(), ContractProcessContext.class);
                context.setOperator(operator);

                ActionResult result = flowEngine.executeFlow(context);
                return result.isSuccess();
            }

            return true;

        } catch (Exception e) {
            log.error("手动修改合同处理状态失败, contractId: {}", contractId, e);
            return false;
        }
    }

    @Override
    public ContractProcessState getProcessState(String contractId) {
        return dataService.selectByContractId(contractId);
    }

    @Override
    public List<ContractProcessState> getRetryableRecords() {
        return dataService.selectRetryableRecords(LocalDateTime.now());
    }

    @Override
    public List<ContractProcessState> getTimeoutRecords() {
        LocalDateTime timeoutTime = LocalDateTime.now().minusSeconds(callbackTimeout / 1000);
        return dataService.selectTimeoutRecords(timeoutTime);
    }

    @Override
    @Transactional
    public int autoRecover() {
        int recoveredCount = 0;
        
        try {
            // 处理重试记录
            List<ContractProcessState> retryableRecords = getRetryableRecords();
            for (ContractProcessState record : retryableRecords) {
                if (retryProcess(record.getContractId())) {
                    recoveredCount++;
                }
            }

            // 处理超时记录
            List<ContractProcessState> timeoutRecords = getTimeoutRecords();
            for (ContractProcessState record : timeoutRecords) {
                handleTimeout(record);
                recoveredCount++;
            }

            log.info("自动恢复完成, 处理记录数: {}", recoveredCount);

        } catch (Exception e) {
            log.error("自动恢复失败", e);
        }

        return recoveredCount;
    }

    /**
     * 更新上下文中的回调结果
     */
    private void updateContextWithCallback(ContractProcessContext context, ThirdPartyCallbackRequest callbackRequest) {
        switch (callbackRequest.getCallbackType()) {
            case "VEHICLE_CHECK":
                if (context.getVehicleCheckData() != null) {
                    context.getVehicleCheckData().setCheckResult(callbackRequest.getStatus());
                    if (callbackRequest.getData() != null) {
                        context.getVehicleCheckData().setReportUrl((String) callbackRequest.getData().get("reportUrl"));
                    }
                }
                break;
            case "SMS_SEND":
                if (context.getSmsData() != null) {
                    context.getSmsData().setSendResult(callbackRequest.getStatus());
                }
                break;
            case "SEAL":
                if (context.getSealData() != null) {
                    context.getSealData().setSealedImageUrl((String) callbackRequest.getData().get("sealedImageUrl"));
                }
                break;
        }
    }

    /**
     * 根据回调结果获取下一个状态
     */
    private ContractProcessStateEnum getCallbackNextState(
            ContractProcessStateEnum currentState, boolean success) {

        if (success) {
            switch (currentState) {
                case VEHICLE_CHECKING:
                    return ContractProcessStateEnum.VEHICLE_CHECK_COMPLETED;
                case SMS_SENDING:
                    return ContractProcessStateEnum.SMS_SEND_COMPLETED;
                case SEALING:
                    return ContractProcessStateEnum.SEAL_COMPLETED;
                default:
                    return currentState;
            }
        } else {
            switch (currentState) {
                case VEHICLE_CHECKING:
                    return ContractProcessStateEnum.VEHICLE_CHECK_FAILED;
                case SMS_SENDING:
                    return ContractProcessStateEnum.SMS_SEND_FAILED;
                case SEALING:
                    return ContractProcessStateEnum.SEAL_FAILED;
                default:
                    return ContractProcessStateEnum.PROCESS_FAILED;
            }
        }
    }



    @Override
    @Transactional
    public boolean manualIntervention(String contractId, String targetState, String operator, String reason) {
        try {
            log.info("人工干预处理, contractId: {}, targetState: {}, operator: {}, reason: {}",
                    contractId, targetState, operator, reason);

            ContractProcessState processState = dataService.selectByContractId(contractId);
            if (processState == null) {
                log.warn("未找到合同处理状态, contractId: {}", contractId);
                return false;
            }

            // 验证目标状态的合法性
            if (!isValidTargetState(targetState)) {
                log.warn("无效的目标状态: {}", targetState);
                return false;
            }

            String previousState = processState.getCurrentState();

            // 更新状态
            processState.setPreviousState(previousState)
                      .setCurrentState(targetState)
                      .setUpdateTime(LocalDateTime.now())
                      .setUpdateOperName(operator)
                      .setIsManualIntervention(false)
                      .setRetryCount(0)  // 重置重试次数
                      .setErrorMessage(null);  // 清除错误信息

            contractProcessStateMapper.updateById(processState);

            // 记录人工干预操作日志
            logOperation(contractId, "MANUAL_INTERVENTION", previousState, targetState,
                    "SUCCESS", reason, null, null, null, 0L, operator);

            log.info("人工干预状态修改成功, contractId: {}, 从 {} 转为 {}",
                    contractId, previousState, targetState);

            // 如果目标状态需要执行动作，使用流程引擎自动触发
            ContractProcessStateEnum state =
                    ContractProcessStateEnum.fromCode(targetState);

            if (state.isProcessingState() || state == ContractProcessStateEnum.INIT) {
                ContractProcessContext context = JSON.parseObject(processState.getProcessData(), ContractProcessContext.class);
                context.setOperator(operator);

                ActionResult result = flowEngine.executeFlow(context);

                if (result.isSuccess()) {
                    log.info("人工干预后自动触发流程成功, contractId: {}, targetState: {}",
                            contractId, targetState);
                } else {
                    log.warn("人工干预后自动触发流程失败, contractId: {}, targetState: {}, error: {}",
                            contractId, targetState, result.getErrorMessage());
                }

                return result.isSuccess();
            }

            log.info("人工干预处理完成, contractId: {}, 从 {} 转为 {}", contractId, previousState, targetState);
            return true;

        } catch (Exception e) {
            log.error("人工干预处理失败, contractId: {}", contractId, e);

            // 记录失败日志
            logOperation(contractId, "MANUAL_INTERVENTION", null, targetState,
                    "FAILED", reason, null, "SYSTEM_ERROR", e.getMessage(), 0L, operator);

            return false;
        }
    }

    /**
     * 验证目标状态的合法性
     */
    private boolean isValidTargetState(String targetState) {
        try {
            ContractProcessStateEnum.fromCode(targetState);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    // 旧的回调处理方法已被新的架构替代

    /**
     * 完成整个流程
     */
    private boolean completeProcess(String contractId) {
        try {
            // 更新状态为流程完成
            updateProcessState(contractId,
                    ContractProcessStateEnum.PROCESS_COMPLETED.getCode(),
                    null, false, "SYSTEM");

            // 更新用户合同表的流程结束时间
            // 注意：需要确保 t_user_contract 表包含以下字段：
            // process_end_time, contract_status
            // 如果表结构不包含这些字段，请先执行相应的 ALTER TABLE 语句
            try {
                UserContract userContract = userContractMapper.selectOne(
                        new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserContract>()
                                .eq("contract_id", contractId));

                if (userContract != null) {
                    // userContract.setProcessEndTime(LocalDateTime.now());
                    // userContract.setContractStatus("COMPLETED");
                    // userContractMapper.updateById(userContract);
                    log.info("合同流程完成，需要更新用户合同表状态, contractId: {}", contractId);
                }
            } catch (Exception e) {
                log.warn("更新用户合同表失败，可能是字段不存在: {}", e.getMessage());
            }

            // 记录日志
            logOperation(contractId, "PROCESS_COMPLETE",
                    ContractProcessStateEnum.SEAL_COMPLETED.getCode(),
                    ContractProcessStateEnum.PROCESS_COMPLETED.getCode(),
                    "SUCCESS", null, null, null, null, 0L, "SYSTEM");

            log.info("合同处理流程完成, contractId: {}", contractId);
            return true;

        } catch (Exception e) {
            log.error("完成合同处理流程失败, contractId: {}", contractId, e);
            return false;
        }
    }

    // 旧的重试方法已被流程引擎替代

    /**
     * 处理超时
     */
    private void handleTimeout(ContractProcessState processState) {
        try {
            ContractProcessStateEnum currentState =
                    ContractProcessStateEnum.fromCode(processState.getCurrentState());

            String failedState;
            switch (currentState) {
                case VEHICLE_CHECK_COMPLETED:
                    failedState = ContractProcessStateEnum.VEHICLE_CHECK_FAILED.getCode();
                    break;
                case SMS_SEND_COMPLETED:
                    failedState = ContractProcessStateEnum.SMS_SEND_FAILED.getCode();
                    break;
                case SEAL_COMPLETED:
                    failedState = ContractProcessStateEnum.SEAL_FAILED.getCode();
                    break;
                default:
                    log.warn("不支持的超时状态: {}", currentState);
                    return;
            }

            // 更新状态为失败
            updateProcessState(processState.getContractId(), failedState, "回调超时", false, "SYSTEM");

            // 记录日志
            logOperation(processState.getContractId(), "TIMEOUT",
                    processState.getCurrentState(), failedState,
                    "FAILED", null, null, "TIMEOUT", "回调超时", 0L, "SYSTEM");

            // 设置重试时间
            setRetryTime(processState.getContractId());

            log.info("处理超时记录, contractId: {}, 从 {} 转为 {}",
                    processState.getContractId(), currentState, failedState);

        } catch (Exception e) {
            log.error("处理超时失败, contractId: {}", processState.getContractId(), e);
        }
    }

    /**
     * 更新流程状态
     */
    private void updateProcessState(String contractId, String newState, String errorMessage,
                                   boolean isManualIntervention, String operator) {
        ContractProcessState processState = dataService.selectByContractId(contractId);
        if (processState != null) {
            processState.setPreviousState(processState.getCurrentState())
                      .setCurrentState(newState)
                      .setErrorMessage(errorMessage)
                      .setIsManualIntervention(isManualIntervention)
                      .setUpdateTime(LocalDateTime.now())
                      .setUpdateOperName(operator);

            contractProcessStateMapper.updateById(processState);
        }
    }

    /**
     * 更新流程数据
     */
    private void updateProcessData(String contractId, String processData) {
        ContractProcessState processState = dataService.selectByContractId(contractId);
        if (processState != null) {
            processState.setProcessData(processData)
                      .setUpdateTime(LocalDateTime.now());

            contractProcessStateMapper.updateById(processState);
        }
    }

    /**
     * 增加重试次数
     */
    private void incrementRetryCount(String contractId) {
        ContractProcessState processState = dataService.selectByContractId(contractId);
        if (processState != null) {
            processState.setRetryCount(processState.getRetryCount() + 1)
                      .setUpdateTime(LocalDateTime.now());

            contractProcessStateMapper.updateById(processState);
        }
    }

    /**
     * 设置重试时间
     */
    private void setRetryTime(String contractId) {
        ContractProcessState processState = dataService.selectByContractId(contractId);
        if (processState != null) {
            LocalDateTime nextRetryTime = LocalDateTime.now().plusSeconds(retryInterval / 1000);
            processState.setNextRetryTime(nextRetryTime)
                      .setUpdateTime(LocalDateTime.now());

            contractProcessStateMapper.updateById(processState);
        }
    }

    /**
     * 更新用户合同验车状态
     * 注意：需要确保 t_user_contract 表包含 vehicle_check_status, vehicle_check_result 字段
     */
    private void updateUserContractVehicleCheck(String contractId, String status, String result) {
        try {
            UserContract userContract = userContractMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserContract>()
                            .eq("contract_id", contractId));

            if (userContract != null) {
                // userContract.setVehicleCheckStatus(status);
                // userContract.setVehicleCheckResult(result);
                // userContractMapper.updateById(userContract);
                log.info("验车状态更新, contractId: {}, status: {}", contractId, status);
            }
        } catch (Exception e) {
            log.warn("更新用户合同验车状态失败，可能是字段不存在: {}", e.getMessage());
        }
    }

    /**
     * 更新用户合同短信状态
     * 注意：需要确保 t_user_contract 表包含 sms_status, sms_result 字段
     */
    private void updateUserContractSms(String contractId, String status, String result) {
        try {
            UserContract userContract = userContractMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserContract>()
                            .eq("contract_id", contractId));

            if (userContract != null) {
                // userContract.setSmsStatus(status);
                // userContract.setSmsResult(result);
                // userContractMapper.updateById(userContract);
                log.info("短信状态更新, contractId: {}, status: {}", contractId, status);
            }
        } catch (Exception e) {
            log.warn("更新用户合同短信状态失败，可能是字段不存在: {}", e.getMessage());
        }
    }

    /**
     * 更新用户合同盖章状态
     * 注意：需要确保 t_user_contract 表包含 seal_status, seal_result, contract_image_url 字段
     */
    private void updateUserContractSeal(String contractId, String status, String result, String imageUrl) {
        try {
            UserContract userContract = userContractMapper.selectOne(
                    new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<UserContract>()
                            .eq("contract_id", contractId));

            if (userContract != null) {
                // userContract.setSealStatus(status);
                // userContract.setSealResult(result);
                // if (imageUrl != null) {
                //     userContract.setContractImageUrl(imageUrl);
                // }
                // userContractMapper.updateById(userContract);
                log.info("盖章状态更新, contractId: {}, status: {}, imageUrl: {}", contractId, status, imageUrl);
            }
        } catch (Exception e) {
            log.warn("更新用户合同盖章状态失败，可能是字段不存在: {}", e.getMessage());
        }
    }

    /**
     * 记录操作日志
     */
    private void logOperation(String contractId, String operationType, String fromState, String toState,
                             String operationResult, String requestData, String responseData,
                             String errorCode, String errorMessage, Long executionTime, String operator) {
        ContractProcessLog log = new ContractProcessLog()
                .setContractId(contractId)
                .setOperationType(operationType)
                .setFromState(fromState)
                .setToState(toState)
                .setOperationResult(operationResult)
                .setRequestData(requestData)
                .setResponseData(responseData)
                .setErrorCode(errorCode)
                .setErrorMessage(errorMessage)
                .setExecutionTime(executionTime)
                .setOperator(operator)
                .setCreateTime(LocalDateTime.now());

        contractProcessLogMapper.insert(log);
    }
}
